"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { MapPin, /* Calendar, */ Users, RotateCcw, X } from "lucide-react"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { statesApi } from "@/lib/api/states.api"

interface VehicleFiltersProps {
  onFiltersChange: (filters: {
    location?: string;
    state?: string;
    startDate?: string;
    endDate?: string;
    passengers?: number;
    bodyType?: string;
    priceMin?: number;
    priceMax?: number;
    transmission?: string;
    amenities?: string[];
    search?: string;
  }) => void;
  variant?: 'dashboard' | 'public';
}

export default function VehicleFilters({ onFiltersChange, /* variant = 'public' */ }: VehicleFiltersProps) {
  const [filters, setFilters] = useState({
    location: "",
    state: "",
    startDate: "",
    endDate: "",
    passengers: "",
    bodyType: "",
    priceRange: "",
    transmission: "",
    search: ""
  });
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);

  // Lista de amenities disponibles con traducción
  const availableAmenities = [
    { value: "air_conditioning", label: "Aire acondicionado" },
    { value: "bluetooth", label: "Bluetooth" },
    { value: "cruise_control", label: "Control de crucero" },
    { value: "backup_camera", label: "Cámara de reversa" },
    { value: "gps", label: "GPS" },
    { value: "heated_seats", label: "Asientos calefactados" },
    { value: "sunroof", label: "Techo solar" },
    { value: "apple_carplay", label: "Apple CarPlay" },
    { value: "android_auto", label: "Android Auto" },
    { value: "usb_ports", label: "Puertos USB" },
    { value: "keyless_entry", label: "Entrada sin llave" },
    { value: "push_button_start", label: "Encendido por botón" },
  ];

  // Obtener lista de estados
  const { data: states } = useQuery({
    queryKey: ['states'],
    queryFn: statesApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // Convertir y enviar filtros
    const processedFilters: any = {};

    if (newFilters.location) processedFilters.location = newFilters.location;
    if (newFilters.state) processedFilters.state = newFilters.state;
    if (newFilters.startDate) processedFilters.startDate = newFilters.startDate;
    if (newFilters.endDate) processedFilters.endDate = newFilters.endDate;
    if (newFilters.passengers) processedFilters.passengers = parseInt(newFilters.passengers);
    if (newFilters.bodyType) processedFilters.bodyType = newFilters.bodyType;
    if (newFilters.transmission) processedFilters.transmission = newFilters.transmission;
    if (selectedAmenities.length > 0) processedFilters.amenities = selectedAmenities;
    if (newFilters.search) processedFilters.search = newFilters.search;

    // Procesar rango de precio
    if (newFilters.priceRange) {
      const [min, max] = newFilters.priceRange.split('-');
      if (min !== undefined) processedFilters.priceMin = parseInt(min);
      if (max !== undefined && max !== '+') processedFilters.priceMax = parseInt(max);
    }

    onFiltersChange(processedFilters);
  };

  const handleAmenityToggle = (amenityValue: string) => {
    const newAmenities = selectedAmenities.includes(amenityValue)
      ? selectedAmenities.filter(a => a !== amenityValue)
      : [...selectedAmenities, amenityValue];

    setSelectedAmenities(newAmenities);

    // Trigger filter change with all current filters
    const processedFilters: any = {};
    if (filters.location) processedFilters.location = filters.location;
    if (filters.state) processedFilters.state = filters.state;
    if (filters.startDate) processedFilters.startDate = filters.startDate;
    if (filters.endDate) processedFilters.endDate = filters.endDate;
    if (filters.passengers) processedFilters.passengers = parseInt(filters.passengers);
    if (filters.bodyType) processedFilters.bodyType = filters.bodyType;
    if (filters.transmission) processedFilters.transmission = filters.transmission;
    if (newAmenities.length > 0) processedFilters.amenities = newAmenities;
    if (filters.search) processedFilters.search = filters.search;

    // Procesar rango de precio
    if (filters.priceRange) {
      const [min, max] = filters.priceRange.split('-');
      if (min !== undefined) processedFilters.priceMin = parseInt(min);
      if (max !== undefined && max !== '+') processedFilters.priceMax = parseInt(max);
    }

    onFiltersChange(processedFilters);
  };

  const handleResetFilters = () => {
    const resetFilters = {
      location: "",
      state: "",
      startDate: "",
      endDate: "",
      passengers: "",
      bodyType: "",
      priceRange: "",
      transmission: "",
      search: ""
    };
    setFilters(resetFilters);
    setSelectedAmenities([]);
    onFiltersChange({});
  };

  // Variant público (sidebar)
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-4">Filtros</h2>
        <div className="space-y-4">
          {/* Estado */}
          <div>
            <label className="text-sm font-medium mb-2 block">Estado</label>
            <Select value={filters.state} onValueChange={(value) => handleFilterChange('state', value)}>
              <SelectTrigger>
                <MapPin className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Selecciona un estado" />
              </SelectTrigger>
              <SelectContent>
                {states?.map((state) => (
                  <SelectItem key={state.id} value={state.code}>
                    {state.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Fechas */}
          <div>
            <label className="text-sm font-medium mb-2 block">Fecha de inicio</label>
            <Input
              type="date"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Fecha de fin</label>
            <Input
              type="date"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
            />
          </div>

          {/* Pasajeros */}
          <div>
            <label className="text-sm font-medium mb-2 block">Pasajeros</label>
            <Select value={filters.passengers} onValueChange={(value) => handleFilterChange('passengers', value)}>
              <SelectTrigger>
                <Users className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Número de pasajeros" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2">2 pasajeros</SelectItem>
                <SelectItem value="4">4 pasajeros</SelectItem>
                <SelectItem value="5">5 pasajeros</SelectItem>
                <SelectItem value="7">7+ pasajeros</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Rango de Precio */}
          <div>
            <label className="text-sm font-medium mb-2 block">Rango de Precio</label>
            <Select value={filters.priceRange} onValueChange={(value) => handleFilterChange('priceRange', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona rango" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0-50">$0 - $50</SelectItem>
                <SelectItem value="50-100">$50 - $100</SelectItem>
                <SelectItem value="100-200">$100 - $200</SelectItem>
                <SelectItem value="200-500">$200 - $500</SelectItem>
                <SelectItem value="500+">$500+</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tipo de Vehículo */}
          <div>
            <label className="text-sm font-medium mb-2 block">Tipo de Vehículo</label>
            <Select value={filters.bodyType} onValueChange={(value) => handleFilterChange('bodyType', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Tipo de vehículo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sedan">Sedán</SelectItem>
                <SelectItem value="suv">SUV</SelectItem>
                <SelectItem value="hatchback">Hatchback</SelectItem>
                <SelectItem value="pickup">Pickup</SelectItem>
                <SelectItem value="coupe">Coupe</SelectItem>
                <SelectItem value="convertible">Convertible</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Transmisión */}
          <div>
            <label className="text-sm font-medium mb-2 block">Transmisión</label>
            <Select value={filters.transmission} onValueChange={(value) => handleFilterChange('transmission', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Tipo de transmisión" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manual">Manual</SelectItem>
                <SelectItem value="automatic">Automática</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Comodidades */}
          <div>
            <label className="text-sm font-medium mb-2 block">Comodidades</label>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {availableAmenities.map((amenity) => (
                <div key={amenity.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`amenity-${amenity.value}`}
                    checked={selectedAmenities.includes(amenity.value)}
                    onCheckedChange={() => handleAmenityToggle(amenity.value)}
                  />
                  <label htmlFor={`amenity-${amenity.value}`} className="text-sm">
                    {amenity.label}
                  </label>
                </div>
              ))}
            </div>

            {/* Mostrar amenities seleccionadas */}
            {selectedAmenities.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {selectedAmenities.map((amenityValue) => {
                  const amenity = availableAmenities.find(a => a.value === amenityValue);
                  return (
                    <Badge key={amenityValue} variant="secondary" className="flex items-center gap-1">
                      {amenity?.label || amenityValue}
                      <button
                        type="button"
                        onClick={() => handleAmenityToggle(amenityValue)}
                        className="rounded-full hover:bg-gray-200 p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  );
                })}
              </div>
            )}
          </div>

          {/* Botón resetear */}
          <Button variant="outline" onClick={handleResetFilters} className="w-full flex items-center gap-2">
            <RotateCcw className="h-4 w-4" />
            Resetear filtros
          </Button>
        </div>
      </div>
    </div>
  );
}
