import { authMiddleware } from '@/app/middlewares/auth.middleware';
import { checkAdmin } from '@/lib/check-admin';
import Elysia, { t } from 'elysia';
import { UsersService } from './users.service';

// Controlador para administradores
export const adminUsersController = new Elysia({ prefix: '/admin/users' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/hosts', async ({ query }) => {
    return await UsersService.getAllHosts({
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })
  .get('/clients', async ({ query }) => {
    return await UsersService.getAllClients({
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })
  .get('/', async ({ query }) => {
    console.log('Query:', query);
    return await UsersService.getAllUsersWithPagination({
      query: {
        page: query.page!,
        limit: query.limit!,
        search: query.search
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
      search: t.Optional(t.String())
    }))
  })
  .patch('/:id/role', async ({ params, body }) => {
    return await UsersService.changeUserRole(params.id, body.role);
  }, {
    body: t.Object({
      role: t.Union([t.Literal('user'), t.Literal('admin')])
    })
  })
  .patch('/:id/ban', async ({ params }) => {
    return await UsersService.banUser(params.id);
  })
  .patch('/:id/unban', async ({ params }) => {
    return await UsersService.unbanUser(params.id);
  })
  .get('/:id/sessions', async ({ params }) => {
    return await UsersService.getUserSessions(params.id);
  })
  .delete('/:id/sessions/:sessionId', async ({ params }) => {
    return await UsersService.revokeUserSession(params.id, params.sessionId);
  })
  .delete('/:id/sessions', async ({ params }) => {
    return await UsersService.revokeAllUserSessions(params.id);
  })
  .delete('/:id', async ({ params }) => {
    return await UsersService.deleteUser(params.id);
  })
  .get('/stats', async () => {
    return await UsersService.getUserStats();
  })
  .patch('/:id/verify', async ({ params }) => {
    return await UsersService.verifyUser(params.id);
  })
// .patch('/:id/block', async ({ params }) => {
//   return await UsersService.blockUser(params.id);
// });