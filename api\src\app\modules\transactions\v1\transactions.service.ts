import { HttpException } from '@/exceptions/HttpExceptions';
import { prisma } from "@/lib/prisma";
// import { HttpException } from "@/exceptions/HttpExceptions";
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  // apiVersion: '2024-11-20.acacia',
});

export class TransactionsService {

  /**
   * Crear una transacción después de un pago exitoso
   */
  static async createTransaction(data: {
    reservationId: string;
    stripePaymentIntentId: string;
    amount: number;
    currency: string;
    paymentMethod?: string;
    description?: string;
  }) {
    try {
      // Obtener configuración actual de la plataforma
      let platformSettings = await prisma.platformSettings.findFirst();

      // Si no existe configuración, crear una por defecto
      if (!platformSettings) {
        platformSettings = await prisma.platformSettings.create({
          data: {
            commissionRate: 0.12, // 12%
            currency: 'mxn'
          }
        });
      }

      // Calcular comisiones
      const platformFeeRate = platformSettings.commissionRate;
      const platformFee = data.amount * platformFeeRate;
      const hostEarnings = data.amount - platformFee;

      // Crear la transacción
      const transaction = await prisma.transaction.create({
        data: {
          reservationId: data.reservationId,
          stripePaymentIntentId: data.stripePaymentIntentId,
          amount: data.amount,
          currency: data.currency,
          platformFeeRate,
          platformFee,
          hostEarnings,
          status: 'succeeded',
          paymentMethod: data.paymentMethod,
          description: data.description,
          paidAt: new Date()
        }
      });

      // Actualizar el estado de pago de la reserva
      await prisma.vehicleReservation.update({
        where: { id: data.reservationId },
        data: { paymentStatus: 'paid' }
      });

      return transaction;
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw HttpException.InternalServerError('Error al crear la transacción');
    }
  }

  /**
   * Actualizar metadatos de Stripe con información de la reserva
   */
  static async updateStripePaymentMetadata(data: {
    stripePaymentIntentId: string;
    reservationId: string;
    vehicleId: string;
    hostId: string;
    clientId: string;
  }) {
    try {
      // Actualizar metadatos del PaymentIntent en Stripe
      await stripe.paymentIntents.update(data.stripePaymentIntentId, {
        metadata: {
          reservationId: data.reservationId,
          vehicleId: data.vehicleId,
          hostId: data.hostId,
          clientId: data.clientId,
          platform: 'autoop'
        }
      });

      return { success: true };
    } catch (error) {
      console.error('Error updating Stripe metadata:', error);
      throw HttpException.InternalServerError('Error al actualizar metadatos de Stripe');
    }
  }

  /**
   * Procesar transferencia al host (llamado por cron job)
   */
  static async processHostTransfer(transactionId: string) {
    try {
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId },
        include: {
          reservation: {
            include: {
              vehicle: {
                include: {
                  host: true
                }
              }
            }
          }
        }
      });

      if (!transaction) {
        throw HttpException.NotFound('Transacción no encontrada');
      }

      if (transaction.status !== 'succeeded') {
        throw HttpException.BadRequest('La transacción no está en estado válido para transferir');
      }

      const host = transaction.reservation.vehicle.host;

      // Verificar que el host tenga cuenta conectada de Stripe
      if (!host.stripeConnectedAccountId) {
        console.log(`Host ${host.id} no tiene cuenta conectada de Stripe para transferencias`);
        return null;
      }

      // Verificar que la cuenta pueda recibir transferencias
      const account = await stripe.accounts.retrieve(host.stripeConnectedAccountId);
      if (!account.charges_enabled || !account.payouts_enabled) {
        console.log(`Cuenta conectada del host ${host.id} no está habilitada para recibir pagos`);
        return null;
      }

      // Crear transferencia en Stripe a la cuenta conectada
      const transfer = await stripe.transfers.create({
        amount: Math.round(transaction.hostEarnings * 100), // Convertir a centavos
        currency: transaction.currency,
        destination: host.stripeConnectedAccountId, // Cuenta conectada, no customer
        description: `Pago por reserva ${transaction.reservation.id}`,
        metadata: {
          transactionId: transaction.id,
          reservationId: transaction.reservationId,
          hostId: host.id
        }
      });

      // Actualizar la transacción
      const updatedTransaction = await prisma.transaction.update({
        where: { id: transactionId },
        data: {
          stripeTransferId: transfer.id,
          status: 'transferred',
          transferredAt: new Date()
        }
      });

      return updatedTransaction;
    } catch (error) {
      console.error('Error processing host transfer:', error);
      throw HttpException.InternalServerError('Error al procesar transferencia al host');
    }
  }

  /**
   * Procesar reembolso
   */
  static async processRefund(transactionId: string, refundData: {
    amount?: number; // Si no se especifica, reembolso total
    reason?: string;
  }) {
    try {
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId },
        include: {
          reservation: true
        }
      });

      if (!transaction) {
        throw HttpException.NotFound('Transacción no encontrada');
      }

      if (transaction.status === 'refunded') {
        throw HttpException.BadRequest('Esta transacción ya fue reembolsada');
      }

      const refundAmount = refundData.amount || transaction.amount;

      if (refundAmount > transaction.amount) {
        throw HttpException.BadRequest('El monto de reembolso no puede ser mayor al monto original');
      }

      // Crear reembolso en Stripe
      const refund = await stripe.refunds.create({
        payment_intent: transaction.stripePaymentIntentId,
        amount: Math.round(refundAmount * 100), // Convertir a centavos
        reason: 'requested_by_customer',
        metadata: {
          transactionId: transaction.id,
          reservationId: transaction.reservationId,
          reason: refundData.reason || 'Reembolso solicitado'
        }
      });

      // Actualizar la transacción
      const updatedTransaction = await prisma.transaction.update({
        where: { id: transactionId },
        data: {
          status: refundAmount === transaction.amount ? 'refunded' : 'partially_refunded',
          refundAmount,
          refundReason: refundData.reason,
          refundedAt: new Date()
        }
      });

      // Actualizar estado de la reserva
      await prisma.vehicleReservation.update({
        where: { id: transaction.reservationId },
        data: {
          paymentStatus: refundAmount === transaction.amount ? 'refunded' : 'partially_refunded',
          status: 'cancelled'
        }
      });

      return updatedTransaction;
    } catch (error) {
      console.error('Error processing refund:', error);
      throw HttpException.InternalServerError('Error al procesar reembolso');
    }
  }

  /**
   * Obtener transacciones de un host
   */
  static async getHostTransactions(hostId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    status?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      const where: any = {
        reservation: {
          vehicle: {
            hostId
          }
        }
      };

      if (filters?.startDate || filters?.endDate) {
        where.paidAt = {};
        if (filters.startDate) where.paidAt.gte = filters.startDate;
        if (filters.endDate) where.paidAt.lte = filters.endDate;
      }

      if (filters?.status) {
        where.status = filters.status;
      }

      const transactions = await prisma.transaction.findMany({
        where,
        include: {
          reservation: {
            include: {
              vehicle: {
                select: {
                  make: true,
                  model: true,
                  year: true
                }
              },
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        },
        orderBy: { paidAt: 'desc' },
        take: filters?.limit || 50,
        skip: filters?.offset || 0
      });

      return transactions;
    } catch (error) {
      console.error('Error getting host transactions:', error);
      throw HttpException.InternalServerError('Error al obtener transacciones del host');
    }
  }

  /**
   * Obtener transacciones de un cliente
   */
  static async getClientTransactions(userId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    status?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      const where: any = {
        reservation: {
          userId
        }
      };

      if (filters?.startDate || filters?.endDate) {
        where.paidAt = {};
        if (filters.startDate) where.paidAt.gte = filters.startDate;
        if (filters.endDate) where.paidAt.lte = filters.endDate;
      }

      if (filters?.status) {
        where.status = filters.status;
      }
      console.log('Where on [getClientTransactions]:', JSON.stringify(where, null, 2));
      console.log('UserId:', userId);

      const transactions = await prisma.transaction.findMany({
        where,
        include: {
          reservation: {
            // include: {
            //   user: {
            //     select: {
            //       id: true,
            //       name: true,
            //       email: true
            //     }
            //   },
            //   vehicle: {
            //     select: {
            //       id: true,
            //       make: true,
            //       model: true,
            //       year: true,
            //       images: true,
            //       hostId: true,
            //       host: {
            //         select: {
            //           id: true,
            //           name: true,
            //           email: true
            //         }
            //       }
            //     }
            //   },
            // },
            select: {
              createdAt: true,
              startDate: true,
              endDate: true,
              totalPrice: true,
              status: true,
              vehicle: {
                select: {
                  id: true,
                  make: true,
                  model: true,
                  year: true,
                  images: true,
                  hostId: true,
                  host: {
                    select: {
                      id: true,
                      name: true,
                      email: true
                    }
                  }
                }
              },
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: filters?.limit || 50,
        skip: filters?.offset || 0
      });
      console.log(`Found ${transactions.length} transactions for user ${userId}`);
      console.log('Sample transaction:', transactions[0] ? {
        id: transactions[0].id,
        amount: transactions[0].amount,
        status: transactions[0].status,
        reservationId: transactions[0].reservationId,
        userId: transactions[0].reservation?.user.id
      } : 'No transactions found');

      return transactions;
    } catch (error) {
      console.error('Error getting client transactions:', error);
      throw HttpException.InternalServerError('Error al obtener transacciones del cliente');
    }
  }

  /**
   * Obtener transacciones de un cliente con paginación
   */
  static async getClientTransactionsWithPagination(userId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    status?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      // Construir el where clause combinando filtros de transacción y usuario
      const where: any = {
        reservation: {
          userId: userId
        }
      };

      // Agregar filtros de fecha si existen
      if (filters?.startDate && filters?.endDate) {
        where.paidAt = {
          gte: filters.startDate,
          lte: filters.endDate
        };
      }

      // Agregar filtro de estado si existe
      if (filters?.status) {
        where.status = filters.status;
      }

      const limit = filters?.limit || 10;
      const offset = filters?.offset || 0;

      console.log('Where clause for getClientTransactionsWithPagination:', JSON.stringify(where, null, 2));

      // Obtener el total de transacciones para paginación
      const total = await prisma.transaction.count({ where });

      // Obtener las transacciones
      const transactions = await prisma.transaction.findMany({
        where,
        include: {
          reservation: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              },
              vehicle: {
                select: {
                  id: true,
                  make: true,
                  model: true,
                  year: true,
                  images: true,
                  hostId: true,
                  host: {
                    select: {
                      id: true,
                      name: true,
                      email: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      // Calcular estadísticas
      const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);
      const totalRefunded = transactions.filter(t => t.refundAmount).reduce((sum, t) => sum + (t.refundAmount || 0), 0);
      const successfulPayments = transactions.filter(t => t.status === 'succeeded' || t.status === 'transferred').length;

      const pagination = {
        total,
        limit,
        offset,
        pages: Math.ceil(total / limit),
        currentPage: Math.floor(offset / limit) + 1
      };

      console.log(`Found ${transactions.length} transactions for user ${userId}, total: ${total}`);

      return {
        transactions,
        pagination,
        stats: {
          totalSpent,
          totalRefunded,
          successfulPayments,
          totalTransactions: total
        }
      };
    } catch (error) {
      console.error('Error getting client transactions with pagination:', error);
      throw HttpException.InternalServerError('Error al obtener transacciones del cliente');
    }
  }

  /**
   * Marcar transacción como transferida manualmente
   */
  static async markAsTransferred(transactionId: string, adminId: string) {
    try {
      // Verificar que la transacción existe y está en estado succeeded
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId },
        include: {
          reservation: {
            include: {
              vehicle: {
                select: { hostId: true }
              }
            }
          }
        }
      });

      if (!transaction) {
        throw HttpException.NotFound('Transacción no encontrada');
      }

      if (transaction.status !== 'succeeded') {
        throw HttpException.BadRequest('Solo se pueden marcar como transferidas las transacciones exitosas');
      }

      if (transaction.transferredAt) {
        throw HttpException.BadRequest('Esta transacción ya fue marcada como transferida');
      }

      // Actualizar la transacción
      const updatedTransaction = await prisma.transaction.update({
        where: { id: transactionId },
        data: {
          transferredAt: new Date(),
          // Agregar información del admin que marcó la transferencia en metadata si es necesario
        }
      });

      return updatedTransaction;
    } catch (error) {
      console.error('Error marking transaction as transferred:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw HttpException.InternalServerError('Error al marcar transacción como transferida');
    }
  }
}
