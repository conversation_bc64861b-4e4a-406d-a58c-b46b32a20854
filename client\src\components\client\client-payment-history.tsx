"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, /* Download, */ ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useEffect, useState } from "react"
import { clientTransactionsApi, Transaction } from "@/lib/api/transactions.api"
import { DateTime } from "luxon"
import Link from "next/link"

export function ClientPaymentHistory() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        const response = await clientTransactionsApi.getTransactions({
          limit: 5,
          offset: 0
        });

        if (response.success) {
          const transactionData = response.data?.transactions || [];
          const totalTransactions = response.data?.pagination?.total || 0;

          setTransactions(transactionData);
          setHasMore(totalTransactions > 5);
        }
      } catch (error) {
        console.error('Error fetching payment history:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'succeeded':
      case 'transferred':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completado</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pendiente</Badge>;
      case 'refunded':
        return <Badge variant="destructive">Reembolsado</Badge>;
      case 'failed':
        return <Badge variant="destructive">Fallido</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatPaymentMethod = (method?: string) => {
    if (!method) return 'N/A';
    if (method === 'card') return 'Tarjeta';
    return method.charAt(0).toUpperCase() + method.slice(1);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Historial de Pagos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-5 w-5 bg-muted animate-pulse rounded-full" />
                  <div>
                    <div className="h-4 w-32 bg-muted animate-pulse rounded mb-2" />
                    <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                  </div>
                </div>
                <div className="text-right">
                  <div className="h-4 w-16 bg-muted animate-pulse rounded mb-2" />
                  <div className="h-3 w-12 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Pagos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No tienes transacciones aún</p>
              <p className="text-sm">Tus pagos aparecerán aquí una vez que realices una reserva</p>
            </div>
          ) : (
            transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium">
                      ${transaction.amount.toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {transaction.reservation.vehicle.make} {transaction.reservation.vehicle.model} {transaction.reservation.vehicle.year}
                    </p>
                    <p className="text-xs text-muted-foreground">{formatPaymentMethod(transaction.paymentMethod)}</p>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(transaction.status)}
                  <p className="text-sm text-muted-foreground mt-2">
                    {DateTime.fromISO(transaction.paidAt || transaction.createdAt).setLocale('es').toFormat('dd MMM yyyy')}
                  </p>
                  {/* <Button variant="ghost" size="sm" className="mt-1">
                    <Download className="h-4 w-4 mr-1" />
                    Factura
                  </Button> */}
                </div>
              </div>
            ))
          )}

          {hasMore && transactions.length > 0 && (
            <div className="pt-4 border-t">
              <Link href="/dashboard/client/payments/history">
                <Button variant="outline" className="w-full">
                  Ver todos los pagos
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
