"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { stripeConnectApi } from "@/lib/api/stripe-connect.api"
// import { useSession } from "@/hooks/use-session"
import { useUser } from '@/context/user-context'
import { useRouter } from "next/navigation"
import toast from "react-hot-toast"
import {/*  useEffect, */ useState } from "react"

export interface OnboardingStatus {
  needsOnboarding: boolean
  hasAccount: boolean
  canReceivePayments: boolean
  isComplete: boolean
  onboardingUrl?: string
  accountId?: string
}

export function useStripeOnboarding() {
  // const { data: session } = useSession()
  const { user } = useUser()
  const queryClient = useQueryClient()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)

  // Solo ejecutar para hosts
  const isHost = user?.userType === 'host' || user?.availableUserTypes?.includes('host')

  // Query para obtener el estado de la cuenta
  const { data: accountStatus, isLoading, error } = useQuery({
    queryKey: ['stripe-account-status'],
    queryFn: async () => {
      const response = await stripeConnectApi.getAccountStatus()
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener estado de cuenta')
      }
      return response.data
    },
    enabled: isHost, // Solo ejecutar si es host
    staleTime: 60 * 1000, // 1 minuto
    retry: 3,
  })

  // Mutación para crear cuenta conectada automáticamente
  const createAccountMutation = useMutation({
    mutationFn: async () => {
      const response = await stripeConnectApi.createAccount({
        email: user?.email || '',
        country: 'MX',
        type: 'express'
      })
      if (!response.success) {
        throw new Error(response.error || 'Error al crear cuenta')
      }
      return response.data
    },
    onSuccess: (data) => {
      toast.success('Cuenta creada exitosamente')
      queryClient.invalidateQueries({ queryKey: ['stripe-account-status'] })

      // Si hay URL de onboarding, redirigir
      if (data?.onboardingUrl) {
        setIsRedirecting(true)
        window.open(data.onboardingUrl, '_blank')
      }
    },
    onError: (error: any) => {
      console.error('Error creating account:', error)
      toast.error(error?.message || 'Error al crear cuenta conectada')
    }
  })

  // Mutación para crear enlace de onboarding
  const createOnboardingMutation = useMutation({
    mutationFn: async () => {
      const response = await stripeConnectApi.createOnboardingLink()
      if (!response.success) {
        throw new Error(response.error || 'Error al crear enlace de onboarding')
      }
      return response.data
    },
    onSuccess: (data) => {
      if (data?.url) {
        setIsRedirecting(true)
        window.open(data.url, '_blank')
      }
    },
    onError: (error: any) => {
      console.error('Error creating onboarding link:', error)
      toast.error(error?.message || 'Error al abrir configuración')
    }
  })

  // Calcular estado de onboarding
  const onboardingStatus: OnboardingStatus = {
    needsOnboarding: isHost && (!accountStatus?.hasAccount || !accountStatus?.canReceivePayments),
    hasAccount: accountStatus?.hasAccount || false,
    canReceivePayments: accountStatus?.canReceivePayments || false,
    isComplete: accountStatus?.hasAccount && accountStatus?.canReceivePayments || false,
    accountId: accountStatus?.accountId
  }

  // Función para iniciar onboarding automático
  const startOnboarding = async () => {
    if (!isHost) {
      toast.error('Solo los hosts pueden configurar pagos')
      return
    }

    try {
      if (!onboardingStatus.hasAccount) {
        // Crear cuenta primero
        await createAccountMutation.mutateAsync()
      } else {
        // Ya tiene cuenta, solo necesita completar onboarding
        await createOnboardingMutation.mutateAsync()
      }
    } catch (error) {
      console.error('Error starting onboarding:', error)
    }
  }

  // Función para verificar si necesita onboarding y redirigir automáticamente
  const checkAndRedirectOnboarding = (autoRedirect = false) => {
    if (!isHost || isLoading || isRedirecting) return false

    if (onboardingStatus.needsOnboarding && autoRedirect) {
      startOnboarding()
      return true
    }

    return onboardingStatus.needsOnboarding
  }

  // Función para navegar a la página de configuración bancaria
  const goToBankingSetup = () => {
    router.push('/dashboard/host/banking')
  }

  return {
    // Estados
    onboardingStatus,
    isLoading,
    error,
    isRedirecting,
    isHost,

    // Acciones
    startOnboarding,
    checkAndRedirectOnboarding,
    goToBankingSetup,

    // Mutaciones
    createAccount: createAccountMutation.mutate,
    createOnboardingLink: createOnboardingMutation.mutate,
    isCreatingAccount: createAccountMutation.isPending,
    isCreatingOnboarding: createOnboardingMutation.isPending,

    // Datos
    accountStatus
  }
}
