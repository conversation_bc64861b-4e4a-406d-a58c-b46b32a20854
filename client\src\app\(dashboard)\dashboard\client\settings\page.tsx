"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AdditionalRoleRequest } from "@/components/settings/additional-role-request"

export default function ClientSettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Configuración</h1>
        <p className="text-muted-foreground">Gestiona tu perfil y preferencias de cliente</p>
      </div>

      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">Perfil</TabsTrigger>
          <TabsTrigger value="notifications">Notificaciones</TabsTrigger>
          <TabsTrigger value="payments">Pagos</TabsTrigger>
          <TabsTrigger value="preferences">Preferencias</TabsTrigger>
          <TabsTrigger value="security">Seguridad</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Información Personal</CardTitle>
              <CardDescription>Actualiza tu información de perfil</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">Nombre</Label>
                  <Input id="firstName" defaultValue="María" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Apellido</Label>
                  <Input id="lastName" defaultValue="González" />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" defaultValue="<EMAIL>" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Teléfono</Label>
                <Input id="phone" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="birthDate">Fecha de Nacimiento</Label>
                <Input id="birthDate" type="date" defaultValue="1990-05-15" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">Dirección</Label>
                <Textarea
                  id="address"
                  placeholder="Tu dirección completa..."
                  defaultValue="Av. Reforma 123, Col. Centro, Ciudad de México, CDMX 06000"
                />
              </div>
              <Button>Guardar Cambios</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Verificaciones</CardTitle>
              <CardDescription>Estado de tus verificaciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Identidad</p>
                  <p className="text-sm text-muted-foreground">Documento de identidad verificado</p>
                </div>
                <Badge variant="default">Verificado</Badge>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Licencia de Conducir</p>
                  <p className="text-sm text-muted-foreground">Licencia válida verificada</p>
                </div>
                <Badge variant="default">Verificado</Badge>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Teléfono</p>
                  <p className="text-sm text-muted-foreground">Número de teléfono verificado</p>
                </div>
                <Badge variant="default">Verificado</Badge>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">Dirección de email verificada</p>
                </div>
                <Badge variant="default">Verificado</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Componente para solicitar rol adicional */}
          <AdditionalRoleRequest />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Preferencias de Notificación</CardTitle>
              <CardDescription>Configura cómo quieres recibir notificaciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Confirmaciones de Reserva</p>
                    <p className="text-sm text-muted-foreground">Recibir confirmación cuando se apruebe una reserva</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Recordatorios de Viaje</p>
                    <p className="text-sm text-muted-foreground">Recordatorios antes del inicio del viaje</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Mensajes de Anfitriones</p>
                    <p className="text-sm text-muted-foreground">Notificaciones de nuevos mensajes</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Ofertas y Promociones</p>
                    <p className="text-sm text-muted-foreground">Recibir ofertas especiales y descuentos</p>
                  </div>
                  <Switch />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Actualizaciones de Pagos</p>
                    <p className="text-sm text-muted-foreground">Confirmaciones de pagos y recibos</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Canales de Notificación</CardTitle>
              <CardDescription>Elige cómo recibir las notificaciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">Notificaciones por correo electrónico</p>
                </div>
                <Switch defaultChecked />
              </div>
              {/*  <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">SMS</p>
                  <p className="text-sm text-muted-foreground">Mensajes de texto para notificaciones urgentes</p>
                </div>
                <Switch defaultChecked />
              </div> */}
              {/* <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Push</p>
                  <p className="text-sm text-muted-foreground">Notificaciones push en la aplicación</p>
                </div>
                <Switch defaultChecked />
              </div> */}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Métodos de Pago</CardTitle>
              <CardDescription>Gestiona tus métodos de pago</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-bold">
                      VISA
                    </div>
                    <div>
                      <p className="font-medium">•••• •••• •••• 4242</p>
                      <p className="text-sm text-muted-foreground">Expira 12/25</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Badge variant="default">Principal</Badge>
                    <Button variant="outline" size="sm">
                      Editar
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-8 bg-red-600 rounded flex items-center justify-center text-white text-xs font-bold">
                      MC
                    </div>
                    <div>
                      <p className="font-medium">•••• •••• •••• 8888</p>
                      <p className="text-sm text-muted-foreground">Expira 08/26</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    Editar
                  </Button>
                </div>
              </div>
              <Button variant="outline" className="w-full">
                Agregar Método de Pago
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Configuración de Facturación</CardTitle>
              <CardDescription>Información para facturación</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="billingName">Nombre para Facturación</Label>
                <Input id="billingName" defaultValue="María González" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="billingAddress">Dirección de Facturación</Label>
                <Textarea
                  id="billingAddress"
                  defaultValue="Av. Reforma 123, Col. Centro, Ciudad de México, CDMX 06000"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="taxId">RFC (Opcional)</Label>
                <Input id="taxId" placeholder="GOMA900515XXX" />
              </div>
              <Button>Actualizar Información</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Preferencias de Búsqueda</CardTitle>
              <CardDescription>Personaliza tu experiencia de búsqueda</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="preferredLocation">Ubicación Preferida</Label>
                <Input id="preferredLocation" defaultValue="Ciudad de México, CDMX" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="preferredCategory">Categoría Preferida</Label>
                <Select defaultValue="economico">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="economico">Económico</SelectItem>
                    <SelectItem value="compacto">Compacto</SelectItem>
                    <SelectItem value="suv">SUV</SelectItem>
                    <SelectItem value="lujo">Lujo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxPrice">Precio Máximo por Día</Label>
                <Input id="maxPrice" type="number" defaultValue="100" />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Solo Vehículos con Entrega</p>
                  <p className="text-sm text-muted-foreground">Mostrar solo vehículos con servicio de entrega</p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Solo Anfitriones Verificados</p>
                  <p className="text-sm text-muted-foreground">Mostrar solo anfitriones con verificación completa</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Configuración de Idioma y Región</CardTitle>
              <CardDescription>Personaliza tu idioma y configuración regional</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="language">Idioma</Label>
                <Select defaultValue="es">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="es">Español</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency">Moneda</Label>
                <Select defaultValue="mxn">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mxn">MXN (Peso Mexicano)</SelectItem>
                    <SelectItem value="usd">USD (Dólar Americano)</SelectItem>
                    <SelectItem value="eur">EUR (Euro)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">Zona Horaria</Label>
                <Select defaultValue="america/mexico_city">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="america/mexico_city">Ciudad de México (GMT-6)</SelectItem>
                    <SelectItem value="america/new_york">Nueva York (GMT-5)</SelectItem>
                    <SelectItem value="europe/madrid">Madrid (GMT+1)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Seguridad de la Cuenta</CardTitle>
              <CardDescription>Protege tu cuenta con estas configuraciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Contraseña Actual</Label>
                <Input id="currentPassword" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newPassword">Nueva Contraseña</Label>
                <Input id="newPassword" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirmar Nueva Contraseña</Label>
                <Input id="confirmPassword" type="password" />
              </div>
              <Button>Cambiar Contraseña</Button>
            </CardContent>
          </Card>

          {/*   <Card>
            <CardHeader>
              <CardTitle>Autenticación de Dos Factores</CardTitle>
              <CardDescription>Agrega una capa extra de seguridad</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">2FA por SMS</p>
                  <p className="text-sm text-muted-foreground">Recibir códigos por mensaje de texto</p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">App Autenticadora</p>
                  <p className="text-sm text-muted-foreground">Usar Google Authenticator o similar</p>
                </div>
                <Switch />
              </div>
            </CardContent>
          </Card> */}

          <Card>
            <CardHeader>
              <CardTitle>Sesiones Activas</CardTitle>
              <CardDescription>Gestiona tus sesiones activas</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Navegador Web - Chrome</p>
                    <p className="text-sm text-muted-foreground">Ciudad de México, México • Activa ahora</p>
                  </div>
                  <Badge variant="default">Actual</Badge>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Aplicación Móvil - iOS</p>
                    <p className="text-sm text-muted-foreground">Ciudad de México, México • Hace 2 horas</p>
                  </div>
                  <Button variant="outline" size="sm">
                    Cerrar Sesión
                  </Button>
                </div>
              </div>
              <Button variant="destructive" className="w-full">
                Cerrar Todas las Sesiones
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
