"use client"

import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { 
  CheckCircle, 
  AlertCircle, 
  Clock,
  CreditCard
} from "lucide-react"
import { useStripeOnboarding } from "@/hooks/use-stripe-onboarding"
import { useRouter } from "next/navigation"

export function OnboardingProgressIndicator() {
  const { onboardingStatus, isLoading, isHost, startOnboarding } = useStripeOnboarding()
  const router = useRouter()

  // No mostrar nada si no es host
  if (!isHost || isLoading) {
    return null
  }

  // Si ya está completamente configurado, mostrar estado exitoso compacto
  if (onboardingStatus.isComplete) {
    return (
      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <div className="flex-1">
            <p className="text-sm font-medium text-green-800">Pagos Configurados</p>
            <p className="text-xs text-green-600">Transferencias habilitadas</p>
          </div>
        </div>
      </div>
    )
  }

  // Calcular progreso
  const getProgress = () => {
    if (!onboardingStatus.hasAccount) return 0
    if (onboardingStatus.hasAccount && !onboardingStatus.canReceivePayments) return 50
    return 100
  }

  const getStatusIcon = () => {
    if (!onboardingStatus.hasAccount) return <AlertCircle className="h-4 w-4 text-orange-600" />
    if (onboardingStatus.hasAccount && !onboardingStatus.canReceivePayments) return <Clock className="h-4 w-4 text-blue-600" />
    return <CheckCircle className="h-4 w-4 text-green-600" />
  }

  const getStatusText = () => {
    if (!onboardingStatus.hasAccount) return "Configuración Pendiente"
    if (onboardingStatus.hasAccount && !onboardingStatus.canReceivePayments) return "Verificación Pendiente"
    return "Configuración Completa"
  }

  const getStatusColor = () => {
    if (!onboardingStatus.hasAccount) return "orange"
    if (onboardingStatus.hasAccount && !onboardingStatus.canReceivePayments) return "blue"
    return "green"
  }

  return (
    <div className={`p-3 border rounded-lg ${
      getStatusColor() === 'orange' ? 'bg-orange-50 border-orange-200' :
      getStatusColor() === 'blue' ? 'bg-blue-50 border-blue-200' :
      'bg-green-50 border-green-200'
    }`}>
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <div className="flex-1">
              <p className={`text-sm font-medium ${
                getStatusColor() === 'orange' ? 'text-orange-800' :
                getStatusColor() === 'blue' ? 'text-blue-800' :
                'text-green-800'
              }`}>
                Sistema de Pagos
              </p>
              <p className={`text-xs ${
                getStatusColor() === 'orange' ? 'text-orange-600' :
                getStatusColor() === 'blue' ? 'text-blue-600' :
                'text-green-600'
              }`}>
                {getStatusText()}
              </p>
            </div>
          </div>
          <Badge 
            variant={getStatusColor() === 'green' ? 'default' : 'secondary'}
            className={
              getStatusColor() === 'orange' ? 'bg-orange-100 text-orange-800' :
              getStatusColor() === 'blue' ? 'bg-blue-100 text-blue-800' :
              'bg-green-100 text-green-800'
            }
          >
            {getProgress()}%
          </Badge>
        </div>

        {/* Progress Bar */}
        <Progress value={getProgress()} className="h-2" />

        {/* Action Button */}
        {!onboardingStatus.isComplete && (
          <div className="flex space-x-2">
            <Button 
              size="sm" 
              variant="outline"
              onClick={startOnboarding}
              className={`flex-1 ${
                getStatusColor() === 'orange' ? 'border-orange-300 text-orange-700 hover:bg-orange-100' :
                'border-blue-300 text-blue-700 hover:bg-blue-100'
              }`}
            >
              <CreditCard className="h-3 w-3 mr-1" />
              {!onboardingStatus.hasAccount ? 'Configurar' : 'Completar'}
            </Button>
            <Button 
              size="sm" 
              variant="ghost"
              onClick={() => router.push('/dashboard/host/onboarding')}
              className="text-xs px-2"
            >
              Info
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
