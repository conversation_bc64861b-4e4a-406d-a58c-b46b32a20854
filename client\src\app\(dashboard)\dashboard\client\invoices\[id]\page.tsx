"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"


export default function ClientInvoiceDetailPage() {

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/client/invoices">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Facturas
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Detalle de Factura</h1>
          <p className="text-muted-foreground">
            Información completa de la factura.
          </p>
        </div>
      </div>

      {/* <InvoiceDetail invoiceId={id} userType="client" /> */}
    </div>
  )
}
