
"use client"

// import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useBookingStoreForVehicle } from "@/hooks/use-booking-store-for-vehicle"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
// import { useSession } from "next-auth/react"
// import { useRouter } from "next/navigation"
import { useUser } from '@/context/user-context'
import toast from 'react-hot-toast'
// import { useState } from 'react'

// Esquema de validación para el formulario
const contactSchema = z.object({
  contactName: z.string().min(3, "El nombre debe tener al menos 3 caracteres"),
  contactEmail: z.string().email("Correo electrónico inválido"),
  contactPhone: z.string().min(10, "El teléfono debe tener al menos 10 dígitos"),
  termsAccepted: z.boolean().refine(value => value === true, {
    message: "Debes aceptar los términos y condiciones de reserva."
  }),
});

type ContactFormValues = z.infer<typeof contactSchema>;

export default function BookingReview({ vehicleId }: { vehicleId: string }) {
  const {
    vehicle,
    dateRange,
    totalPrice,
    prevStep,
    nextStep,
    setContactInfo,
  } = useBookingStoreForVehicle(vehicleId)

  console.log('vehicle: ', vehicle)

  const session = useUser()

  // Inicializar el formulario con los datos del usuario si está disponible
  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      contactName: session?.user?.name || "",
      contactEmail: session?.user?.email || "",
      contactPhone: "",
      termsAccepted: false,
    },
  });
  // const [testCheckboxValue, setTestCheckboxValue] = useState("")
  // console.log('testCheckboxValue: ', testCheckboxValue)
  console.log('form state: ', form.formState.errors)
  console.log('values: ', form.getValues())

  // Calculate number of days
  const days =
    dateRange.startDate instanceof Date && dateRange.endDate instanceof Date
      ? Math.ceil((dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24))
      : 0

  const onSubmit = (data: ContactFormValues) => {
    // No es necesario validar termsAccepted aquí, ya que el esquema lo hace

    if (!vehicle) {
      return toast.error("No se pudo continuar. Por favor, intenta de nuevo más tarde.")
    }

    if (!dateRange.startDate || !dateRange.endDate) {
      return toast.error("Por favor selecciona las fechas de reserva.")
    }

    // Guardar los datos de contacto en el store
    setContactInfo({
      contactName: data.contactName,
      contactEmail: data.contactEmail,
      contactPhone: data.contactPhone,
    });

    // Avanzar al siguiente paso (pago)
    nextStep();
  };

  if (!vehicle || !dateRange.startDate || !dateRange.endDate) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="p-4 border border-red-300 bg-red-50 rounded-lg text-red-800">
          <h3 className="font-bold">Información incompleta</h3>
          <p>Por favor, selecciona un vehículo y fechas de reserva.</p>
          <Button
            onClick={prevStep}
            className="mt-4"
            variant="outline"
          >
            Volver a seleccionar fechas
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <h2 className="text-xl font-bold mb-6">Revisa tu reserva</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="border rounded-lg p-4">
          <h3 className="font-bold mb-2">Detalles del vehículo</h3>
          <p className="mb-1">{vehicle.make} {vehicle.model} {vehicle.year}</p>
          <p className="mb-1">Color: {vehicle.color}</p>
          <p className="font-medium">${vehicle.price} / día</p>
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="font-bold mb-2">Fechas de reserva</h3>
          <p className="mb-1">
            <span className="font-medium">Desde:</span> {format(dateRange.startDate, "PPP", { locale: es })}
          </p>
          <p className="mb-1">
            <span className="font-medium">Hasta:</span> {format(dateRange.endDate, "PPP", { locale: es })}
          </p>
          <p className="font-medium">{days} {days === 1 ? 'día' : 'días'}</p>
        </div>
      </div>

      <div className="border rounded-lg p-4 mb-6">
        <h3 className="font-bold mb-4">Información de contacto</h3>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Label htmlFor="contactName">Nombre completo</Label>
            <Input
              id="contactName"
              {...form.register("contactName")}
            />
            {form.formState.errors.contactName && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.contactName.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="contactEmail">Correo electrónico</Label>
            <Input
              id="contactEmail"
              type="email"
              {...form.register("contactEmail")}
            />
            {form.formState.errors.contactEmail && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.contactEmail.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="contactPhone">Teléfono</Label>
            <Input
              id="contactPhone"
              {...form.register("contactPhone")}
            />
            {form.formState.errors.contactPhone && (
              <p className="text-sm text-red-500 mt-1">{form.formState.errors.contactPhone.message}</p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="termsAccepted"
              checked={form.watch("termsAccepted") === true}
              onCheckedChange={(checked) => {
                console.log('checked: ', checked)
                form.setValue("termsAccepted", checked === true, { shouldValidate: true });
              }}
            />
            <Label htmlFor="termsAccepted" className="text-sm">
              Acepto los términos y condiciones de reserva
            </Label>
          </div>
          {form.formState.errors.termsAccepted && (
            <p className="text-sm text-red-500">{form.formState.errors.termsAccepted.message}</p>
          )}

          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex justify-between mb-2">
              <span>Precio por día:</span>
              <span className="font-medium">${vehicle.price}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Duración:</span>
              <span className="font-medium">{days} {days === 1 ? 'día' : 'días'}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Total:</span>
              <span>${totalPrice}</span>
            </div>
          </div>

          <div className="flex justify-between pt-4">
            <Button
              type="button"
              onClick={prevStep}
              variant="outline"
            >
              Volver
            </Button>
            <Button
              type="submit"
              className="bg-[#1a2b5e] hover:bg-[#152348]"
            >
              Continuar
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
