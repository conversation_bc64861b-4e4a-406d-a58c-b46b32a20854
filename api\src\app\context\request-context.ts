import { AsyncLocalStorage } from 'node:async_hooks';
// import { Session, User } from '@prisma/client';

interface RequestContext {
  requestId: string;
  user: {
    id: string;
    role: string;
    email: string;
    timezone?: string;
  };
  session: {
    userId: string;
  },
  preferenceTimezone?: string;
}

export const requestContextStorage = new AsyncLocalStorage<RequestContext>();


export function setRequestContext(context: RequestContext): void {
  return requestContextStorage.enterWith(context);
}


export function getRequestContext(fallback?: Partial<RequestContext>): RequestContext {
  const store = requestContextStorage.getStore();

  if (store) {
    return store;
  }

  if (fallback) {
    return fallback as RequestContext;
  }
  console.error('Contexto no disponible y no se proporcionó un valor predeterminado.');
  throw new Error('Contexto no disponible y no se proporcionó un valor predeterminado.');
}


export function getCurrentUserIdFromRequestContext(): string | null {
  const context = getRequestContext();
  return context.user.id;
}