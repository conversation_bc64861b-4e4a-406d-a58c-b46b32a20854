"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"

import { Combobox } from "@/components/ui/combobox"
import {
  CreditCard,
  // CheckCircle,
  AlertCircle,
  Plus,
  Building2,
  Edit,
  Trash2
} from "lucide-react"
import { stripeConnectApi, /* ConnectedAccountStatus, */ BankAccount, BankAccountData } from "@/lib/api/stripe-connect.api"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { MEXICAN_BANKS, /* getBankByValue */ } from "@/lib/banks-mexico"
import toast from "react-hot-toast"
import Swal from "sweetalert2"

// Esquema de validación con Zod
const bankAccountSchema = z.object({
  accountHolderName: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  routingNumber: z.string()
    .length(18, "La CLABE debe tener exactamente 18 dígitos")
    .regex(/^\d+$/, "La CLABE solo debe contener números"),
  bankName: z.string().min(1, "Selecciona un banco"),
  accountType: z.enum(["checking", "savings"])
})

type BankAccountFormData = z.infer<typeof bankAccountSchema>

export function HostBankingSetup() {
  const queryClient = useQueryClient()
  const [editingBankAccount, setEditingBankAccount] = useState<BankAccount | null>(null)

  // React Hook Form
  const form = useForm<BankAccountFormData>({
    resolver: zodResolver(bankAccountSchema),
    defaultValues: {
      accountHolderName: '',
      routingNumber: '',
      bankName: '',
      accountType: 'checking'
    }
  })

  // Query para obtener el estado de la cuenta
  const { data: accountStatusResponse, isLoading: accountLoading, /* error: accountError */ } = useQuery({
    queryKey: ['stripe-account-status'],
    queryFn: async () => {
      try {
        const response = await stripeConnectApi.getAccountStatus()
        return response
      } catch (error) {
        console.error('Error in getAccountStatus:', error)
        // Retornar un estado por defecto si hay error
        return {
          success: true,
          data: {
            hasAccount: false,
            isComplete: false,
            canReceivePayments: false
          }
        }
      }
    },
    staleTime: 60 * 1000, // 1 minuto
  })

  const accountStatus = accountStatusResponse?.data
  console.log('Account Status:', accountStatus)

  // Query para obtener cuentas bancarias (solo si tiene cuenta)
  const { data: bankAccounts = [], isLoading: bankAccountsLoading } = useQuery({
    queryKey: ['stripe-bank-accounts'],
    queryFn: async () => {
      const response = await stripeConnectApi.getBankAccounts()
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener cuentas bancarias')
      }
      return response.data
    },
    enabled: !!accountStatus?.hasAccount,
    staleTime: 60 * 1000, // 1 minuto
  })
  console.log('Bank Accounts:', bankAccounts)

  const loading = accountLoading || bankAccountsLoading



  // Mutación para agregar cuenta bancaria
  const addBankAccountMutation = useMutation({
    mutationFn: async (data: BankAccountFormData) => {
      // Si no hay cuenta de Stripe, crear una automáticamente
      if (!accountStatus?.hasAccount) {
        await stripeConnectApi.createAccount({
          email: '', // Se puede obtener del usuario actual
          country: 'MX',
          type: 'express'
        })
      }

      // Convertir a formato esperado por la API
      const apiData: BankAccountData = {
        ...data,
        bankName: data.bankName
      }
      return stripeConnectApi.addBankAccount(apiData)
    },
    onSuccess: () => {
      toast.success('Cuenta bancaria agregada exitosamente')
      form.reset() // Reset del formulario
      setEditingBankAccount(null)
      queryClient.invalidateQueries({ queryKey: ['stripe-account-status'] })
      queryClient.invalidateQueries({ queryKey: ['stripe-bank-accounts'] })
    },
    onError: (error: any) => {
      console.log('Error adding bank account:', error)

      let errorMessage = 'Error al agregar cuenta bancaria'

      if (typeof error === 'string') {
        errorMessage = error
      } else if (error?.message) {
        errorMessage = error.message
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error?.data?.message) {
        errorMessage = error.data.message
      }

      toast.error(errorMessage)
    }
  })

  // Mutación para eliminar cuenta bancaria
  const removeBankAccountMutation = useMutation({
    mutationFn: (bankAccountId: string) => stripeConnectApi.removeBankAccount(bankAccountId),
    onSuccess: () => {
      toast.success('Cuenta bancaria eliminada exitosamente')
      queryClient.invalidateQueries({ queryKey: ['stripe-bank-accounts'] })
      queryClient.invalidateQueries({ queryKey: ['stripe-account-status'] })
    },
    onError: (error: any) => {
      console.log('Error removing bank account:', error)

      // Extraer mensaje de error de diferentes estructuras posibles
      let errorMessage = 'Error al eliminar cuenta bancaria'

      if (typeof error === 'string') {
        errorMessage = error
      } else if (error?.message) {
        errorMessage = error.message
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error?.data?.message) {
        errorMessage = error.data.message
      }

      toast.error(errorMessage)
    }
  })

  // Mutación para actualizar cuenta bancaria
  const updateBankAccountMutation = useMutation({
    mutationFn: ({ bankAccountId, data }: { bankAccountId: string; data: BankAccountFormData }) => {
      const apiData: BankAccountData = {
        ...data,
        bankName: data.bankName
      }
      return stripeConnectApi.updateBankAccount(bankAccountId, apiData)
    },
    onSuccess: () => {
      toast.success('Cuenta bancaria actualizada exitosamente')
      form.reset()
      setEditingBankAccount(null)
      queryClient.invalidateQueries({ queryKey: ['stripe-bank-accounts'] })
    },
    onError: () => {
      toast.error('Error al actualizar cuenta bancaria')
    }
  })

  // Handlers
  const onSubmit = (data: BankAccountFormData) => {
    if (editingBankAccount && editingBankAccount.id) {
      // Actualizar cuenta existente
      updateBankAccountMutation.mutate({
        bankAccountId: editingBankAccount.id,
        data
      })
    } else {
      // Agregar nueva cuenta
      addBankAccountMutation.mutate(data)
    }
  }

  const handleEditBankAccount = (bankAccount: BankAccount) => {
    setEditingBankAccount(bankAccount)
    // Pre-llenar el formulario con los datos existentes solo si tiene ID (cuenta existente)
    if (bankAccount.id) {
      console.log('Editing bank account:', bankAccount) // Debug log
      form.setValue('accountHolderName', bankAccount.account_holder_name || '')
      // La CLABE está en metadata.clabe, si no existe usar routing_number
      form.setValue('routingNumber', bankAccount.metadata?.clabe || bankAccount.routing_number || '')
      form.setValue('bankName', bankAccount.metadata?.bank_name || '')
      form.setValue('accountType', 'checking') // Default
    } else {
      // Limpiar formulario para nueva cuenta
      form.reset()
    }
  }

  const handleCancelEdit = () => {
    setEditingBankAccount(null)
    form.reset()
  }

  const handleRemoveBankAccount = async (account: BankAccount) => {
    const isOnlyAccount = bankAccounts.length === 1
    const isDefaultAccount = account.default_for_currency

    let title = 'Eliminar cuenta bancaria'
    let text = '¿Estás seguro de que quieres eliminar esta cuenta bancaria?'
    let icon: 'warning' | 'question' = 'warning'

    if (isOnlyAccount) {
      title = 'Eliminar única cuenta bancaria'
      text = 'Esta es tu única cuenta bancaria. Si la eliminas, no podrás recibir pagos hasta que agregues otra.'
      icon = 'warning'
    } else if (isDefaultAccount) {
      title = 'Eliminar cuenta por defecto'
      text = 'Esta es tu cuenta bancaria por defecto. Se establecerá otra cuenta como predeterminada automáticamente.'
      icon = 'question'
    }

    const result = await Swal.fire({
      title,
      text,
      icon,
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar',
      reverseButtons: true
    })

    if (result.isConfirmed) {
      removeBankAccountMutation.mutate(account.id)
    }
  }

  if (loading && !accountStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Configuración Bancaria</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Configuración Bancaria
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {!accountStatus?.hasAccount ? (
            <div className="space-y-6">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Para recibir pagos automáticos, necesitas configurar una cuenta bancaria.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">
                  {editingBankAccount ? 'Editar Información Bancaria' : 'Agregar Información Bancaria'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {editingBankAccount
                    ? 'Actualiza tu información bancaria para recibir transferencias.'
                    : 'Agrega tu información bancaria para recibir transferencias automáticas.'
                  }
                </p>

                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="accountHolderName">Nombre del Titular</Label>
                      <Input
                        id="accountHolderName"
                        {...form.register("accountHolderName")}
                        placeholder="Nombre completo"
                      />
                      {form.formState.errors.accountHolderName && (
                        <p className="text-xs text-red-500 mt-1">
                          {form.formState.errors.accountHolderName.message}
                        </p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="routingNumber">CLABE Interbancaria</Label>
                      <Input
                        id="routingNumber"
                        {...form.register("routingNumber", {
                          onChange: (e) => {
                            // Solo permitir números
                            const value = e.target.value.replace(/\D/g, '');
                            form.setValue("routingNumber", value);
                          }
                        })}
                        placeholder="18 dígitos"
                        maxLength={18}
                      />
                      {form.formState.errors.routingNumber && (
                        <p className="text-xs text-red-500 mt-1">
                          {form.formState.errors.routingNumber.message}
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        La CLABE debe tener exactamente 18 dígitos
                      </p>
                    </div>
                  </div>



                  <div>
                    <Label htmlFor="bankName">Banco</Label>
                    <Combobox
                      options={MEXICAN_BANKS}
                      value={form.watch("bankName")}
                      onValueChange={(value) => form.setValue("bankName", value)}
                      placeholder="Selecciona tu banco"
                      searchPlaceholder="Buscar banco..."
                      emptyMessage="No se encontró el banco"
                      className="w-full"
                    />
                    {form.formState.errors.bankName && (
                      <p className="text-xs text-red-500 mt-1">
                        {form.formState.errors.bankName.message}
                      </p>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <Button
                      type="submit"
                      disabled={addBankAccountMutation.isPending || updateBankAccountMutation.isPending}
                      className="flex-1"
                    >
                      {editingBankAccount
                        ? (updateBankAccountMutation.isPending ? 'Actualizando...' : 'Actualizar Cuenta')
                        : (addBankAccountMutation.isPending ? 'Agregando...' : 'Agregar Cuenta Bancaria')
                      }
                    </Button>
                    {editingBankAccount && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleCancelEdit}
                      >
                        Cancelar
                      </Button>
                    )}
                  </div>
                </form>
              </div>
            </div>
          ) : (
            // Configuración automática en segundo plano - no mostrar detalles técnicos al usuario
            <div className="space-y-4">
              {/* <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium">Configuración Bancaria en Proceso</span>
              </div> */}

              {!accountStatus?.canReceivePayments && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">Completando configuración del sistema de pagos automátizados...</p>
                      <p className="text-sm text-muted-foreground">
                        {/* Nuestro equipo está finalizando la configuración de tu cuenta para recibir pagos. */}
                        {/* Esto puede tomar unos minutos. Mientras tanto, puedes agregar tu información bancaria. */}
                        Nuestro equipo está trabajando para integrar transferencias automáticas.
                        Por el momento, puedes agregar tu información bancaria para recibir pagos de nuestro equipo manualmente.
                      </p>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {accountStatus?.hasAccount && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Cuentas Bancarias
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {bankAccounts.length === 0 ? (
              <div className="space-y-4">
                <div className="text-center py-4 text-muted-foreground">
                  <p>No tienes cuentas bancarias configuradas</p>
                </div>

                {/* Mostrar formulario para agregar cuenta bancaria cuando no hay ninguna */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">
                      {editingBankAccount ? 'Editar Información Bancaria' : 'Agregar Información Bancaria'}
                    </h3>
                    {!editingBankAccount && (
                      <Button
                        variant="outline"
                        onClick={() => setEditingBankAccount({} as BankAccount)}
                        size="sm"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Agregar Cuenta
                      </Button>
                    )}
                  </div>

                  {(editingBankAccount || bankAccounts.length === 0) && (
                    <>
                      <p className="text-sm text-muted-foreground">
                        {editingBankAccount && editingBankAccount.id
                          ? 'Actualiza tu información bancaria para recibir transferencias.'
                          : 'Agrega tu información bancaria para recibir transferencias automáticas.'
                        }
                      </p>

                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="accountHolderName">Nombre del Titular</Label>
                            <Input
                              id="accountHolderName"
                              {...form.register("accountHolderName")}
                              placeholder="Nombre completo"
                            />
                            {form.formState.errors.accountHolderName && (
                              <p className="text-xs text-red-500 mt-1">
                                {form.formState.errors.accountHolderName.message}
                              </p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="routingNumber">CLABE Interbancaria</Label>
                            <Input
                              id="routingNumber"
                              {...form.register("routingNumber", {
                                onChange: (e) => {
                                  // Solo permitir números
                                  const value = e.target.value.replace(/\D/g, '');
                                  form.setValue("routingNumber", value);
                                }
                              })}
                              placeholder="18 dígitos"
                              maxLength={18}
                            />
                            {form.formState.errors.routingNumber && (
                              <p className="text-xs text-red-500 mt-1">
                                {form.formState.errors.routingNumber.message}
                              </p>
                            )}
                            <p className="text-xs text-muted-foreground mt-1">
                              La CLABE debe tener exactamente 18 dígitos
                            </p>
                          </div>
                        </div>



                        <div>
                          <Label htmlFor="bankName">Banco</Label>
                          <Combobox
                            options={MEXICAN_BANKS}
                            value={form.watch("bankName")}
                            onValueChange={(value) => form.setValue("bankName", value)}
                            placeholder="Selecciona tu banco"
                            searchPlaceholder="Buscar banco..."
                            emptyMessage="No se encontró el banco"
                            className="w-full"
                          />
                          {form.formState.errors.bankName && (
                            <p className="text-xs text-red-500 mt-1">
                              {form.formState.errors.bankName.message}
                            </p>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Button
                            type="submit"
                            disabled={addBankAccountMutation.isPending || updateBankAccountMutation.isPending}
                            className="flex-1"
                          >
                            {editingBankAccount && editingBankAccount.id
                              ? (updateBankAccountMutation.isPending ? 'Actualizando...' : 'Actualizar Cuenta')
                              : (addBankAccountMutation.isPending ? 'Agregando...' : 'Agregar Cuenta Bancaria')
                            }
                          </Button>
                          {editingBankAccount && (
                            <Button
                              type="button"
                              variant="outline"
                              onClick={handleCancelEdit}
                            >
                              Cancelar
                            </Button>
                          )}
                        </div>
                      </form>
                    </>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    Gestiona tus cuentas bancarias para recibir pagos
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => setEditingBankAccount({} as BankAccount)}
                    size="sm"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Agregar Cuenta
                  </Button>
                </div>

                {/* Formulario para agregar/editar cuenta cuando hay cuentas existentes */}
                {editingBankAccount && (
                  <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                    <h4 className="font-medium">
                      {editingBankAccount.id ? 'Editar Cuenta Bancaria' : 'Nueva Cuenta Bancaria'}
                    </h4>

                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="accountHolderName">Nombre del Titular</Label>
                          <Input
                            id="accountHolderName"
                            {...form.register("accountHolderName")}
                            placeholder="Nombre completo"
                          />
                          {form.formState.errors.accountHolderName && (
                            <p className="text-xs text-red-500 mt-1">
                              {form.formState.errors.accountHolderName.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <Label htmlFor="routingNumber">CLABE Interbancaria</Label>
                          <Input
                            id="routingNumber"
                            {...form.register("routingNumber", {
                              onChange: (e) => {
                                // Solo permitir números
                                const value = e.target.value.replace(/\D/g, '');
                                form.setValue("routingNumber", value);
                              }
                            })}
                            placeholder="18 dígitos"
                            maxLength={18}
                          />
                          {form.formState.errors.routingNumber && (
                            <p className="text-xs text-red-500 mt-1">
                              {form.formState.errors.routingNumber.message}
                            </p>
                          )}
                          <p className="text-xs text-muted-foreground mt-1">
                            La CLABE debe tener exactamente 18 dígitos
                          </p>
                        </div>
                      </div>



                      <div>
                        <Label htmlFor="bankName">Banco</Label>
                        <Combobox
                          options={MEXICAN_BANKS}
                          value={form.watch("bankName")}
                          onValueChange={(value) => form.setValue("bankName", value)}
                          placeholder="Selecciona tu banco"
                          searchPlaceholder="Buscar banco..."
                          emptyMessage="No se encontró el banco"
                          className="w-full"
                        />
                        {form.formState.errors.bankName && (
                          <p className="text-xs text-red-500 mt-1">
                            {form.formState.errors.bankName.message}
                          </p>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button
                          type="submit"
                          disabled={addBankAccountMutation.isPending || updateBankAccountMutation.isPending}
                          className="flex-1"
                        >
                          {editingBankAccount.id
                            ? (updateBankAccountMutation.isPending ? 'Actualizando...' : 'Actualizar Cuenta')
                            : (addBankAccountMutation.isPending ? 'Agregando...' : 'Agregar Cuenta Bancaria')
                          }
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleCancelEdit}
                        >
                          Cancelar
                        </Button>
                      </div>
                    </form>
                  </div>
                )}

                <div className="space-y-2">
                  {bankAccounts.map((account) => (
                    <div key={account.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{account.metadata?.bank_name || account.bank_name || 'Banco'}</p>
                          <p className="text-sm text-muted-foreground">
                            ****{account.last4} • {account.account_holder_name}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {account.default_for_currency && (
                          <Badge variant="secondary">Por Defecto</Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditBankAccount(account)}
                          disabled={editingBankAccount?.id === account.id}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Editar
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveBankAccount(account)}
                          disabled={removeBankAccountMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Eliminar
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
