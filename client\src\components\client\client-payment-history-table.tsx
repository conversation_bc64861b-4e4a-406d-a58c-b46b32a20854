"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { type ColumnDef } from "@tanstack/react-table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, Search, Filter } from "lucide-react"
import { clientTransactionsApi, Transaction } from "@/lib/api/transactions.api"
import { DateTime } from "luxon"
import { DataTable } from "@/components/data-table/data-table"

// Funciones auxiliares para formateo
const getStatusBadge = (status: string) => {
  switch (status) {
    case 'succeeded':
      return <Badge variant="default" className="bg-green-100 text-green-800">Exitoso</Badge>;
    case 'pending':
      return <Badge variant="secondary">Pendiente</Badge>;
    case 'failed':
      return <Badge variant="destructive">Fallido</Badge>;
    case 'refunded':
      return <Badge variant="outline">Reembolsado</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

const formatPaymentMethod = (method?: string) => {
  if (!method) return 'N/A';
  switch (method.toLowerCase()) {
    case 'card':
      return 'Tarjeta';
    case 'bank_transfer':
      return 'Transferencia';
    default:
      return method;
  }
};

// Definición de columnas para la tabla
const columns: ColumnDef<Transaction>[] = [
  {
    accessorKey: "vehicle",
    header: "Vehículo",
    cell: ({ row }) => {
      const transaction = row.original;
      return (
        <div>
          <p className="font-medium">
            {transaction.reservation.vehicle.make} {transaction.reservation.vehicle.model}
          </p>
          <p className="text-sm text-muted-foreground">
            {transaction.reservation.vehicle.year}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: "amount",
    header: "Monto",
    cell: ({ row }) => {
      const amount = row.getValue("amount") as number;
      return (
        <span className="font-medium">
          ${amount.toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
        </span>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Estado",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return getStatusBadge(status);
    },
  },
  {
    accessorKey: "paymentMethod",
    header: "Método",
    cell: ({ row }) => {
      const method = row.getValue("paymentMethod") as string;
      return formatPaymentMethod(method);
    },
  },
  {
    accessorKey: "date",
    header: "Fecha",
    cell: ({ row }) => {
      const transaction = row.original;
      return DateTime.fromISO(transaction.paidAt || transaction.createdAt)
        .setLocale('es')
        .toFormat('dd/MM/yyyy');
    },
  },
  {
    id: "actions",
    header: "Acciones",
    cell: () => {
      return (
        <Button variant="ghost" size="sm">
          <Download className="h-4 w-4 mr-1" />
          Factura
        </Button>
      );
    },
  },
];

// Componente de la tabla separado
function ClientPaymentHistoryDataTable({
  transactions,
  isLoading,
  pagination,
  currentPage,
  onPageChange
}: {
  transactions: Transaction[];
  isLoading: boolean;
  pagination: any;
  currentPage: number;
  onPageChange: (page: number) => void;
}) {
  return (
    <DataTable
      columns={columns}
      data={transactions}
      rowCount={pagination.total}
      pageSize={10}
      pageIndex={currentPage - 1}
      onPageChange={onPageChange}
      isLoading={isLoading}
    />
  );
}

export function ClientPaymentHistoryTable() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const limit = 10;

  // React Query para obtener transacciones
  const { data, isLoading, /* error */ } = useQuery({
    queryKey: ['client-transactions', currentPage, statusFilter, limit],
    queryFn: async () => {
      const offset = (currentPage - 1) * limit;
      const response = await clientTransactionsApi.getTransactions({
        limit,
        offset,
        status: statusFilter !== "all" ? statusFilter : undefined
      });

      if (!response.success) {
        throw new Error(response.error || 'Error al obtener transacciones');
      }

      return response.data;
    },
    staleTime: 60 * 1000, // 1 minuto
  });

  const transactions = data?.transactions || [];
  const pagination = data?.pagination || {
    total: 0,
    currentPage: 1,
    totalPages: 1,
    from: 1,
    to: 1
  };



  const filteredTransactions = transactions.filter(transaction =>
    transaction.reservation.vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.reservation.vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.amount.toString().includes(searchTerm)
  );

  // Reset to page 1 when status filter changes
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  if (isLoading && transactions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Historial de Pagos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-muted animate-pulse rounded" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Pagos</CardTitle>
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar por vehículo o monto..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filtrar por estado" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos los estados</SelectItem>
              <SelectItem value="succeeded">Exitoso</SelectItem>
              <SelectItem value="pending">Pendiente</SelectItem>
              <SelectItem value="failed">Fallido</SelectItem>
              <SelectItem value="refunded">Reembolsado</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {filteredTransactions.length === 0 && !isLoading ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No se encontraron transacciones</p>
            <p className="text-sm">Intenta ajustar los filtros de búsqueda</p>
          </div>
        ) : (
          <ClientPaymentHistoryDataTable
            transactions={filteredTransactions}
            isLoading={isLoading}
            pagination={pagination}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        )}
      </CardContent>
    </Card>
  )
}
