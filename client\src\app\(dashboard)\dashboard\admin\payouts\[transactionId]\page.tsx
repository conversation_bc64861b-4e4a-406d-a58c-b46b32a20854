"use client"

import { useState } from "react"
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { useParams, useRouter } from "next/navigation"
import { ArrowLeft, CreditCard, User, Car, Calendar, /* DollarSign, */ Building2, CheckCircle, Eye, EyeOff } from "lucide-react"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Card, CardContent, /* CardDescription, */ CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { adminTransactionsApi } from "@/lib/api/transactions.api"
import toast from "react-hot-toast"
import Swal from "sweetalert2"

// Estados y etiquetas para transacciones
const statusColors = {
  succeeded: "bg-green-100 text-green-800",
  pending: "bg-yellow-100 text-yellow-800",
  processing: "bg-blue-100 text-blue-800",
  failed: "bg-red-100 text-red-800",
  refunded: "bg-gray-100 text-gray-800",
}

const statusLabels = {
  succeeded: "Completado",
  pending: "Pendiente",
  processing: "Procesando",
  failed: "Fallido",
  refunded: "Reembolsado",
}

export default function TransactionDetailPage() {
  const params = useParams()
  const router = useRouter()
  const queryClient = useQueryClient()
  const transactionId = params.transactionId as string
  const [showBankingInfo, setShowBankingInfo] = useState(false)

  // Query para obtener detalle de la transacción
  const { data: transactionData, isLoading: transactionLoading } = useQuery({
    queryKey: ['admin-transaction', transactionId],
    queryFn: async () => {
      const response = await adminTransactionsApi.getTransaction(transactionId)
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener transacción')
      }
      return response.data
    },
    enabled: !!transactionId,
  })

  // Query para obtener información bancaria del host (solo cuando se necesite)
  const { data: bankingResponse, isLoading: bankingLoading, error: bankingError } = useQuery({
    queryKey: ['admin-transaction-banking', transactionId],
    queryFn: async () => {
      if (!transactionId) return null
      const response = await adminTransactionsApi.getHostBankingInfo(transactionId)
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener información bancaria')
      }
      return response
    },
    enabled: !!transactionId && showBankingInfo,
  })

  const bankingData = bankingResponse?.data?.data





  // Mutación para marcar como transferida
  const markAsTransferredMutation = useMutation({
    mutationFn: async () => {
      const response = await adminTransactionsApi.markAsTransferred(transactionId)
      if (!response.success) {
        throw new Error(response.error || 'Error al marcar como transferida')
      }
      return response.data
    },
    onSuccess: () => {
      toast.success('Transacción marcada como transferida')
      queryClient.invalidateQueries({ queryKey: ['admin-transaction', transactionId] })
      queryClient.invalidateQueries({ queryKey: ['admin-transactions'] })
    },
    onError: () => {
      toast.error('Error al marcar transacción como transferida')
    }
  })

  const handleMarkAsTransferred = async () => {
    const result = await Swal.fire({
      title: '¿Confirmar transferencia?',
      text: 'Esta acción marcará la transacción como transferida al host.',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, marcar como transferida',
      cancelButtonText: 'Cancelar'
    })

    if (result.isConfirmed) {
      markAsTransferredMutation.mutate()
    }
  }

  const transaction = transactionData?.data
  const reservation = transaction?.reservation
  const vehicle = reservation?.vehicle
  const host = vehicle?.host
  const client = reservation?.user

  if (transactionLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Button>
        </div>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Cargando detalle de transacción...</p>
        </div>
      </div>
    )
  }

  if (!transaction || !reservation || !vehicle || !host || !client) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Button>
        </div>
        <div className="text-center py-8">
          <p className="text-muted-foreground">Transacción no encontrada o datos incompletos</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Detalle de Transacción</h1>
            <p className="text-muted-foreground">ID: {transaction.id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={statusColors[transaction.status as keyof typeof statusColors]}>
            {statusLabels[transaction.status as keyof typeof statusLabels]}
          </Badge>
          {transaction.status === 'succeeded' && !transaction.transferredAt && (
            <Button
              onClick={handleMarkAsTransferred}
              disabled={markAsTransferredMutation.isPending}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              {markAsTransferredMutation.isPending ? 'Procesando...' : 'Marcar como Transferida'}
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Información de la Transacción */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Información de Pago
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Monto Total</p>
                <p className="text-2xl font-bold">${transaction.amount.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Comisión ({(transaction.platformFeeRate * 100).toFixed(1)}%)</p>
                <p className="text-xl font-semibold text-red-600">${transaction.platformFee.toLocaleString()}</p>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Ganancia del Host</p>
              <p className="text-2xl font-bold text-green-600">${transaction.hostEarnings.toLocaleString()}</p>
            </div>
            <Separator />
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Stripe Payment ID:</span>
                <span className="text-sm font-mono">{transaction.stripePaymentIntentId}</span>
              </div>
              {transaction.stripeTransferId && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Stripe Transfer ID:</span>
                  <span className="text-sm font-mono">{transaction.stripeTransferId}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Fecha de Pago:</span>
                <span className="text-sm">{transaction.paidAt ? new Date(transaction.paidAt).toLocaleString() : 'N/A'}</span>
              </div>
              {transaction.transferredAt && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Fecha de Transferencia:</span>
                  <span className="text-sm">{new Date(transaction.transferredAt).toLocaleString()}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Información de la Reserva */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Información de Reserva
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">ID de Reserva</p>
              <p className="font-mono">{reservation.id}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Fecha de Inicio</p>
                <p>{new Date(reservation.startDate).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Fecha de Fin</p>
                <p>{new Date(reservation.endDate).toLocaleDateString()}</p>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Precio Total de Reserva</p>
              <p className="text-xl font-semibold">${reservation.totalPrice.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Estado de Reserva</p>
              <Badge variant="outline">{reservation.status}</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Información del Vehículo */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Car className="h-5 w-5" />
              Vehículo
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-lg font-semibold">{vehicle.year} {vehicle.make} {vehicle.model}</p>
            </div>
            {vehicle.images && vehicle.images.length > 0 && (
              <div className="aspect-video relative rounded-lg overflow-hidden">
                <Image
                  src={`/api/v1/files/download?key=${vehicle.images[0]}`}
                  alt={`${vehicle.make} ${vehicle.model}`}
                  className="object-cover"
                  fill
                  unoptimized={true}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Información del Host */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Anfitrión
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Avatar>
                <AvatarImage src={host?.image} />
                <AvatarFallback>
                  {host?.name ? host.name.split(' ').map(n => n[0]).join('') : 'H'}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{host?.name || 'Host'}</p>
                <p className="text-sm text-muted-foreground">{host?.email}</p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowBankingInfo(!showBankingInfo)}
              className="w-full"
            >
              <Building2 className="h-4 w-4 mr-2" />
              {showBankingInfo ? (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Ocultar Información Bancaria
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Ver Información Bancaria
                </>
              )}
            </Button>

            {showBankingInfo && (
              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="text-sm">Información Bancaria</CardTitle>
                </CardHeader>
                <CardContent>
                  {bankingLoading ? (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="mt-2 text-sm text-muted-foreground">Cargando información bancaria...</p>
                    </div>
                  ) : bankingError ? (
                    <div className="text-center py-4">
                      <p className="text-sm text-red-600">Error al cargar información bancaria</p>
                      <p className="text-xs text-muted-foreground mt-1">{bankingError.message}</p>
                    </div>
                  ) : bankingData ? (
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Titular</p>
                        <p className="font-medium">{bankingData.accountHolderName || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Banco</p>
                        <p className="font-medium">{bankingData.bankName || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">CLABE</p>
                        <p className="font-mono text-sm bg-gray-100 p-2 rounded">
                          {bankingData.routingNumber || 'N/A'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Tipo de Cuenta</p>
                        <p className="capitalize font-medium">
                          {bankingData.accountType === 'checking' ? 'Corriente' :
                            bankingData.accountType === 'savings' ? 'Ahorros' :
                              bankingData.accountType || 'N/A'}
                        </p>
                      </div>
                      {bankingData.stripeExternalAccountId && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">ID Stripe</p>
                          <p className="font-mono text-xs text-gray-600">{bankingData.stripeExternalAccountId}</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-muted-foreground">No hay información bancaria configurada para este host</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        El host debe configurar su información bancaria para recibir transferencias
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Información del Cliente */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Cliente
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Avatar>
                <AvatarImage src={client?.image} />
                <AvatarFallback>
                  {client?.name ? client.name.split(' ').map(n => n[0]).join('') : 'C'}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{client?.name || 'Cliente'}</p>
                <p className="text-sm text-muted-foreground">{client?.email}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
