import { HttpException } from '@/exceptions/HttpExceptions';
import { prisma } from "@/lib/prisma";

// Tipos para información bancaria
export interface BankAccountInfo {
  id: string;
  accountHolderName: string;
  routingNumber: string; // CLABE
  bankName: string;
  accountType: 'checking' | 'savings';
  stripeExternalAccountId?: string; // ID en Stripe Connect
  isDefault?: boolean;
  createdAt: string; // ISO date string para JSON
  updatedAt: string; // ISO date string para JSON
}

export interface CreateBankAccountData {
  accountHolderName: string;
  routingNumber: string;
  bankName: string;
  accountType: 'checking' | 'savings';
  stripeExternalAccountId?: string;
}

export interface UpdateBankAccountData extends CreateBankAccountData {
  id: string;
}

export class HostBankingService {
  /**
   * Obtener información bancaria de un host
   */
  static async getHostBankingInfo(hostId: string) {
    try {
      const bankingInfo = await prisma.hostBankingInfo.findUnique({
        where: { userId: hostId }
      });

      if (!bankingInfo) {
        return {
          bankAccounts: [],
          defaultAccountIndex: null
        };
      }

      const bankAccounts = Array.isArray(bankingInfo.bankAccounts)
        ? bankingInfo.bankAccounts
        : [];

      return {
        bankAccounts,
        defaultAccountIndex: bankingInfo.defaultAccountIndex
      };
    } catch (error) {
      console.error('Error getting host banking info:', error);
      throw HttpException.InternalServerError('Error al obtener información bancaria');
    }
  }

  /**
   * Obtener cuenta bancaria por defecto de un host
   */
  static async getDefaultBankAccount(hostId: string): Promise<BankAccountInfo | null> {
    try {
      const bankingInfo = await this.getHostBankingInfo(hostId);

      if (bankingInfo.bankAccounts.length === 0 || bankingInfo.defaultAccountIndex === null) {
        return null;
      }

      const defaultAccount = bankingInfo.bankAccounts[bankingInfo.defaultAccountIndex];
      return defaultAccount || null;
    } catch (error) {
      console.error('Error getting default bank account:', error);
      throw HttpException.InternalServerError('Error al obtener cuenta bancaria por defecto');
    }
  }

  /**
   * Agregar cuenta bancaria
   */
  static async addBankAccount(hostId: string, accountData: CreateBankAccountData) {
    try {
      // Verificar que el usuario existe
      const user = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!user) {
        throw HttpException.NotFound('Host no encontrado');
      }

      // Obtener información bancaria actual
      const currentInfo = await this.getHostBankingInfo(hostId);

      // Crear nueva cuenta con ID único
      const newAccount: BankAccountInfo = {
        id: `bank_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        ...accountData,
        isDefault: currentInfo.bankAccounts.length === 0, // Primera cuenta es por defecto
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const updatedAccounts = [...currentInfo.bankAccounts, newAccount];
      const defaultIndex = currentInfo.bankAccounts.length === 0 ? 0 : currentInfo.defaultAccountIndex;

      // Actualizar o crear registro
      await prisma.hostBankingInfo.upsert({
        where: { userId: hostId },
        update: {
          bankAccounts: updatedAccounts,
          defaultAccountIndex: defaultIndex
        },
        create: {
          userId: hostId,
          bankAccounts: updatedAccounts,
          defaultAccountIndex: defaultIndex
        }
      });

      return newAccount;
    } catch (error) {
      console.error('Error adding bank account:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw HttpException.InternalServerError('Error al agregar cuenta bancaria');
    }
  }

  /**
   * Actualizar cuenta bancaria
   */
  static async updateBankAccount(hostId: string, accountData: UpdateBankAccountData) {
    try {
      const currentInfo = await this.getHostBankingInfo(hostId);

      const accountIndex = currentInfo.bankAccounts.findIndex(acc => acc.id === accountData.id);
      if (accountIndex === -1) {
        throw HttpException.NotFound('Cuenta bancaria no encontrada');
      }

      // Actualizar cuenta
      const updatedAccount: BankAccountInfo = {
        ...currentInfo.bankAccounts[accountIndex],
        ...accountData,
        updatedAt: new Date().toISOString()
      };

      const updatedAccounts = [...currentInfo.bankAccounts];
      updatedAccounts[accountIndex] = updatedAccount;

      await prisma.hostBankingInfo.update({
        where: { userId: hostId },
        data: {
          bankAccounts: updatedAccounts
        }
      });

      return updatedAccount;
    } catch (error) {
      console.error('Error updating bank account:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw HttpException.InternalServerError('Error al actualizar cuenta bancaria');
    }
  }

  /**
   * Eliminar cuenta bancaria
   */
  static async removeBankAccount(hostId: string, accountId: string) {
    try {
      const currentInfo = await this.getHostBankingInfo(hostId);

      const accountIndex = currentInfo.bankAccounts.findIndex(acc => acc.id === accountId);
      if (accountIndex === -1) {
        throw HttpException.NotFound('Cuenta bancaria no encontrada');
      }

      const updatedAccounts = currentInfo.bankAccounts.filter(acc => acc.id !== accountId);

      // Ajustar índice por defecto si es necesario
      let newDefaultIndex = currentInfo.defaultAccountIndex;
      if (accountIndex === currentInfo.defaultAccountIndex) {
        // Si eliminamos la cuenta por defecto, hacer la primera cuenta como por defecto
        newDefaultIndex = updatedAccounts.length > 0 ? 0 : null;
      } else if (currentInfo.defaultAccountIndex !== null && accountIndex < currentInfo.defaultAccountIndex) {
        // Si eliminamos una cuenta antes de la por defecto, ajustar el índice
        newDefaultIndex = currentInfo.defaultAccountIndex - 1;
      }

      await prisma.hostBankingInfo.update({
        where: { userId: hostId },
        data: {
          bankAccounts: updatedAccounts,
          defaultAccountIndex: newDefaultIndex
        }
      });

      return { success: true };
    } catch (error) {
      console.error('Error removing bank account:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw HttpException.InternalServerError('Error al eliminar cuenta bancaria');
    }
  }

  /**
   * Establecer cuenta por defecto
   */
  static async setDefaultBankAccount(hostId: string, accountId: string) {
    try {
      const currentInfo = await this.getHostBankingInfo(hostId);

      const accountIndex = currentInfo.bankAccounts.findIndex(acc => acc.id === accountId);
      if (accountIndex === -1) {
        throw HttpException.NotFound('Cuenta bancaria no encontrada');
      }

      await prisma.hostBankingInfo.update({
        where: { userId: hostId },
        data: {
          defaultAccountIndex: accountIndex
        }
      });

      return { success: true };
    } catch (error) {
      console.error('Error setting default bank account:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw HttpException.InternalServerError('Error al establecer cuenta por defecto');
    }
  }
}
