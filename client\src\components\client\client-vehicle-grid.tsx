import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Heart, MapPin } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { useQuery } from "@tanstack/react-query"
import { PaginationControl } from "@/components/ui/pagination-control"
import { useSearchParams, useRouter } from "next/navigation"


// Mapeo de tipos de carrocería en español
const bodyTypeLabels: Record<string, string> = {
  sedan: "Sedan",
  suv: "SUV",
  hatchback: "Hatchback",
  pickup: "Pickup",
  coupe: "Coupe",
  convertible: "Convertible",
  wagon: "Wagon",
  van: "Van",
  minivan: "Minivan",
  targa: "Targa",
  doublecab: "Doble Cabina",
  truck: "Camioneta"
};

interface ClientVehicleGridProps {
  filters?: {
    location?: string;
    startDate?: string;
    endDate?: string;
    passengers?: number;
    bodyType?: string;
    priceMin?: number;
    priceMax?: number;
    transmission?: string;
    search?: string;
  };
}

export function ClientVehicleGrid({ filters = {} }: ClientVehicleGridProps) {
  const searchParams = useSearchParams()
  const router = useRouter()
  const page = Number(searchParams.get('page') || 1)
  const limit = Number(searchParams.get('limit') || 8)

  const { data, isLoading, error } = useQuery({
    queryKey: ['client-vehicles', page, limit, filters],
    queryFn: () => vehiclesApi.getAll({
      page,
      limit,
      ...filters
    }),
    staleTime: 60 * 1000, // 1 minuto
  })

  const vehicles = data?.data || []
  const pagination = data?.pagination

  const handlePageChange = (pageIndex: number) => {
    if (pageIndex !== page) {
      const params = new URLSearchParams(searchParams.toString())
      params.set('page', pageIndex.toString())
      router.push(`?${params.toString()}`)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="text-center text-red-500">
          No se pudieron cargar los vehículos. Por favor, intenta de nuevo más tarde.
        </div>
      </div>
    )
  }

  if (!vehicles || vehicles.length === 0) {
    return (
      <div className="space-y-4">
        <div className="text-center">
          No se encontraron vehículos con los filtros seleccionados.
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Vehículos disponibles</h3>
        <p className="text-sm text-muted-foreground">{pagination?.total || vehicles.length} vehículos encontrados</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {vehicles.map((vehicle: any) => (
          <Card key={vehicle.id} className="overflow-hidden hover:shadow-lg transition-shadow pt-0">
            <div className="relative">
              <Image
                src={Array.isArray(vehicle.images) && vehicle.images.length > 0 ? vehicle.images[0] : "/placeholder.svg"}
                alt={`${vehicle.make} ${vehicle.model}`}
                width={300}
                height={200}
                className="w-full h-48 object-cover"
              />
              <Button variant="ghost" size="icon" className="absolute top-2 right-2 bg-white/80 hover:bg-white">
                <Heart className="h-4 w-4" />
              </Button>
            </div>

            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-lg">{vehicle.make} {vehicle.model}</h4>
                <div className="text-right">
                  <p className="font-bold text-lg">${vehicle.price.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">por día</p>
                </div>
              </div>

              <div className="text-sm text-gray-600 mb-2">
                {vehicle.year} • {bodyTypeLabels[vehicle.bodyType] || vehicle.bodyType}
                {vehicle.transmission && ` • ${vehicle.transmission === 'automatic' ? 'Automático' : 'Manual'}`}
              </div>

              {vehicle.totalReviews > 0 && (
                <div className="flex items-center gap-2 mb-2">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{vehicle.averageRating}</span>
                  <span className="text-sm text-muted-foreground">({vehicle.totalReviews} reseñas)</span>
                </div>
              )}

              {vehicle.features?.location && (
                <div className="flex items-center mb-3">
                  <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                  <span className="text-sm text-muted-foreground">{vehicle.features.location}</span>
                </div>
              )}

              <div className="flex flex-wrap gap-1 mb-3">
                {vehicle.features?.fuelType && (
                  <Badge variant="secondary" className="text-xs">
                    {vehicle.features.fuelType}
                  </Badge>
                )}
                {vehicle.features?.seats && (
                  <Badge variant="secondary" className="text-xs">
                    {vehicle.features.seats} asientos
                  </Badge>
                )}
              </div>

              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">Anfitrión: {vehicle.host?.name}</p>
                <Link href={`/vehicles/${vehicle.id}`}>
                  <Button size="sm">Ver detalles</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {pagination && pagination.totalPages > 1 && (
        <PaginationControl
          currentPage={page}
          totalPages={pagination.totalPages}
          onPageChange={handlePageChange}
          className="mt-6"
        />
      )}
    </div>
  )
}
