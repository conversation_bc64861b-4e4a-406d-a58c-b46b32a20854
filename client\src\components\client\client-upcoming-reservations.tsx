"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"
import { reservationsApi, ReservationResponse } from "@/lib/api/reservations.api"
import { DateTime } from "luxon"

export function ClientUpcomingReservations() {
  const [reservations, setReservations] = useState<ReservationResponse[]>([]);
  const [loading, setLoading] = useState(true);

  console.log('reservations', reservations);

  useEffect(() => {
    const fetchReservations = async () => {
      try {
        const response = await reservationsApi.client.getReservations();
        if (response) {
          // Filtrar solo las reservas futuras y confirmadas/pendientes, ordenadas por fecha
          const upcomingReservations = response
            .filter(r =>
              (r.status === 'confirmed' || r.status === 'pending') &&
              new Date(r.startDate) > new Date()
            )
            .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime())
            .slice(0, 3); // Solo mostrar las próximas 3

          setReservations(upcomingReservations);
        }
      } catch (error) {
        console.error('Error fetching upcoming reservations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchReservations();
  }, []);

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = DateTime.fromISO(startDate);
    const end = DateTime.fromISO(endDate);
    const days = Math.ceil(end.diff(start, 'days').days);

    return {
      start: start.toFormat('dd MMM'),
      end: end.toFormat('dd MMM'),
      days
    };
  };

  // const getVehicleImage = (images?: string[]) => {
  //   if (images && images.length > 0) {
  //     return `/api/images/${images[0]}`;
  //   }
  //   return "/placeholder.svg?height=40&width=40";
  // };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Próximas Reservas</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/client/reservations">Ver Todas</Link>
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-4 border rounded-lg">
                <div className="h-12 w-12 bg-muted animate-pulse rounded" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-40 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        ) : reservations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No tienes próximas reservas</p>
            <Button variant="outline" size="sm" className="mt-2" asChild>
              <Link href="/dashboard/client/search">Buscar Vehículos</Link>
            </Button>
          </div>
        ) : (
              <div className="space-y-4">
                {reservations.map((reservation) => {
                  const dateRange = formatDateRange(reservation.startDate, reservation.endDate);
                  return (
                    <div key={reservation.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="h-12 w-12 rounded overflow-hidden">
                        <Image
                          // src={getVehicleImage(reservation.vehicle.images)}
                          src={reservation.vehicle.images?.[0] || "/placeholder.svg?height=40&width=40"}
                          alt={`${reservation.vehicle.make} ${reservation.vehicle.model}`}
                          width={48}
                          height={48}
                          className="h-full w-full object-cover"
                          unoptimized={true}
                        />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <p className="text-sm font-medium">{reservation.vehicle.make} {reservation.vehicle.model}</p>
                            <Badge variant={reservation.status === "confirmed" ? "default" : "secondary"} className="text-xs">
                              {reservation.status === "confirmed" ? "Confirmado" : "Pendiente"}
                            </Badge>
                          </div>
                          <span className="text-sm font-medium">
                            ${reservation.totalPrice.toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground mb-1">Anfitrión: {reservation.vehicle.host.name}</p>
                        <p className="text-xs text-muted-foreground mb-2">📍 {reservation.vehicle.features.location}</p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>
                            {dateRange.start} - {dateRange.end} ({dateRange.days} {dateRange.days === 1 ? 'día' : 'días'})
                          </span>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline" className="h-6 text-xs" asChild>
                              <Link href={`/dashboard/client/reservations/${reservation.id}`}>
                                Ver detalles
                              </Link>
                            </Button>
                            {reservation.status === "pending" && (
                              <Button size="sm" variant="destructive" className="h-6 text-xs">
                                Cancelar
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
        )}
      </CardContent>
    </Card>
  )
}
