import { Elysia, t } from "elysia";
import { InvoicesService } from "./invoices.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';

// Esquemas de validación
const createInvoiceSchema = t.Object({
  transactionId: t.String(),
  customerName: t.Optional(t.String()),
  customerEmail: t.Optional(t.String()),
  customerAddress: t.<PERSON>tional(t.Any()),
  customerTaxId: t.Optional(t.String()),
  providerId: t.Optional(t.String())
});

const invoiceFiltersSchema = t.Object({
  limit: t.Optional(t.Number()),
  offset: t.Optional(t.Number()),
  status: t.Optional(t.String())
});

// Controlador para administradores
export const adminInvoicesController = new Elysia()
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .group('/admin/invoices', (app) =>
    app
      // Crear factura manualmente
      .post('/', async ({ body }) => {
        try {
          const invoice = await InvoicesService.createInvoiceForTransaction(body.transactionId);
          return {
            success: true,
            data: invoice
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }, {
        body: t.Object({
          transactionId: t.String()
        })
      })

      // Obtener todas las facturas (admin)
      .get('/', async ({ query }) => {
        try {
          // Implementar lógica para obtener todas las facturas
          return {
            success: true,
            data: { invoices: [], pagination: {} }
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }, {
        query: invoiceFiltersSchema
      })

      // Obtener factura por ID
      .get('/:id', async ({ params }) => {
        try {
          const invoice = await InvoicesService.getInvoiceById(params.id);
          return {
            success: true,
            data: invoice
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      })
  );

// Controlador para hosts
export const hostInvoicesController = new Elysia()
  .use(authMiddleware)
  .derive(({ user }) => {
    if (!user || !user.availableUserTypes?.includes('host')) {
      // throw new Error('Acceso denegado: Se requieren permisos de host');
      throw HttpException.Forbidden('Acceso denegado: Se requieren permisos de host');
    }
  })
  .group('/host/invoices', (app) =>
    app
      // Obtener facturas del host (facturas de sus transacciones)
      .get('/', async ({ query, user }) => {
        try {
          // Implementar lógica para obtener facturas del host
          // Esto requeriría una consulta más compleja para obtener facturas
          // de transacciones relacionadas con vehículos del host
          return {
            success: true,
            data: { invoices: [], pagination: {} }
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }, {
        query: invoiceFiltersSchema
      })

      // Obtener factura específica del host
      .get('/:id', async ({ params, user }) => {
        try {
          const invoice = await InvoicesService.getInvoiceById(params.id);
          
          // Verificar que la factura pertenece a una transacción del host
          if (invoice.transaction.reservation.vehicle.hostId !== user.id) {
            throw new Error('No tienes permisos para ver esta factura');
          }

          return {
            success: true,
            data: invoice
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      })
  );

// Controlador para clientes
export const clientInvoicesController = new Elysia()
  .use(authMiddleware)
  .group('/client/invoices', (app) =>
    app
      // Obtener facturas del cliente
      .get('/', async ({ query, user }) => {
        try {
          const result = await InvoicesService.getInvoicesByUser(user.id, query);
          return {
            success: true,
            data: result
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }, {
        query: invoiceFiltersSchema
      })

      // Obtener factura específica del cliente
      .get('/:id', async ({ params, user }) => {
        try {
          const invoice = await InvoicesService.getInvoiceById(params.id);
          
          // Verificar que la factura pertenece al usuario
          if (invoice.transaction.reservation.userId !== user.id) {
            throw new Error('No tienes permisos para ver esta factura');
          }

          return {
            success: true,
            data: invoice
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      })

      // Descargar PDF de factura
      .get('/:id/pdf', async ({ params, user, set }) => {
        try {
          const invoice = await InvoicesService.getInvoiceById(params.id);
          
          // Verificar que la factura pertenece al usuario
          if (invoice.transaction.reservation.userId !== user.id) {
            throw new Error('No tienes permisos para descargar esta factura');
          }

          if (!invoice.pdfUrl) {
            throw new Error('PDF no disponible para esta factura');
          }

          // Redirigir al PDF
          set.redirect = invoice.pdfUrl;
          return;

        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      })
  );

// Controlador público (para webhooks, etc.)
export const publicInvoicesController = new Elysia()
  .group('/public/invoices', (app) =>
    app
      // Webhook para actualizaciones de proveedores externos
      .post('/webhook/:provider', async ({ params, body, headers }) => {
        try {
          // Implementar lógica de webhook según el proveedor
          console.log(`Webhook received from ${params.provider}:`, body);
          
          return {
            success: true,
            message: 'Webhook processed'
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      })
  );
