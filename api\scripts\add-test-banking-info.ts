import { prisma } from '../src/lib/prisma';

async function addTestBankingInfo() {
  try {
    // Buscar un host (usuario con vehículos)
    const host = await prisma.user.findFirst({
      where: {
        vehicles: {
          some: {}
        }
      },
      include: {
        vehicles: true
      }
    });

    if (!host) {
      console.log('No se encontró ningún host con vehículos');
      return;
    }

    console.log(`Agregando información bancaria para el host: ${host.name} (${host.email})`);

    // Crear información bancaria de prueba
    const testBankAccount = {
      id: `bank_${Date.now()}_test`,
      accountHolderName: host.name || 'Host Test',
      routingNumber: '012345678901234567', // CLABE de prueba
      bankName: 'Banco de Prueba',
      accountType: 'checking' as const,
      isDefault: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Crear o actualizar la información bancaria
    await prisma.hostBankingInfo.upsert({
      where: { userId: host.id },
      update: {
        bankAccounts: [testBankAccount],
        defaultAccountIndex: 0
      },
      create: {
        userId: host.id,
        bankAccounts: [testBankAccount],
        defaultAccountIndex: 0
      }
    });

    console.log('✅ Información bancaria agregada exitosamente');
    console.log('Datos agregados:', {
      hostId: host.id,
      hostName: host.name,
      bankAccount: testBankAccount
    });

  } catch (error) {
    console.error('❌ Error al agregar información bancaria:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Ejecutar el script
addTestBankingInfo();
