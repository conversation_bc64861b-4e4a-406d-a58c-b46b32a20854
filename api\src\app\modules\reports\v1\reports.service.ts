import { prisma } from "@/lib/prisma";
import { HttpException } from '@/exceptions/HttpExceptions';
import { DateTime } from "luxon";

export class ReportsService {

  /**
   * Generar reporte mensual para un host específico
   */
  static async generateHostMonthlyReport(hostId: string, year: number, month: number) {
    try {
      // Verificar que el host existe
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host) {
        throw HttpException.NotFound('Host no encontrado');
      }

      // Calcular fechas del mes
      const startDate = DateTime.fromObject({ year, month, day: 1 }).startOf('day').toJSDate();
      const endDate = DateTime.fromObject({ year, month, day: 1 }).endOf('month').toJSDate();

      // Obtener todas las transacciones del host en ese mes
      const transactions = await prisma.transaction.findMany({
        where: {
          reservation: {
            vehicle: {
              hostId
            }
          },
          paidAt: {
            gte: startDate,
            lte: endDate
          },
          status: {
            in: ['succeeded', 'transferred']
          }
        },
        include: {
          reservation: {
            include: {
              vehicle: {
                select: {
                  make: true,
                  model: true,
                  year: true
                }
              }
            }
          }
        }
      });

      // Calcular estadísticas
      const totalReservations = transactions.length;
      const totalGrossEarnings = transactions.reduce((sum, t) => sum + t.amount, 0);
      const totalPlatformFees = transactions.reduce((sum, t) => sum + t.platformFee, 0);
      const totalNetEarnings = transactions.reduce((sum, t) => sum + t.hostEarnings, 0);
      const totalTransferred = transactions
        .filter(t => t.status === 'transferred')
        .reduce((sum, t) => sum + t.hostEarnings, 0);

      // Verificar si ya existe un reporte para este mes
      const existingReport = await prisma.hostMonthlyReport.findUnique({
        where: {
          hostId_year_month: {
            hostId,
            year,
            month
          }
        }
      });

      let report;
      if (existingReport) {
        // Actualizar reporte existente
        report = await prisma.hostMonthlyReport.update({
          where: { id: existingReport.id },
          data: {
            totalReservations,
            totalGrossEarnings,
            totalPlatformFees,
            totalNetEarnings,
            totalTransferred,
            generatedAt: new Date()
          }
        });
      } else {
        // Crear nuevo reporte
        report = await prisma.hostMonthlyReport.create({
          data: {
            hostId,
            year,
            month,
            totalReservations,
            totalGrossEarnings,
            totalPlatformFees,
            totalNetEarnings,
            totalTransferred
          }
        });
      }

      return {
        report,
        transactions,
        period: {
          year,
          month,
          startDate,
          endDate
        }
      };
    } catch (error) {
      console.error('Error generating host monthly report:', error);
      throw HttpException.InternalServerError('Error al generar reporte mensual');
    }
  }

  /**
   * Obtener reportes mensuales de un host
   */
  static async getHostMonthlyReports(hostId: string, filters?: {
    year?: number;
    startYear?: number;
    endYear?: number;
    limit?: number;
    offset?: number;
  }) {
    try {
      const where: any = { hostId };

      if (filters?.year) {
        where.year = filters.year;
      } else if (filters?.startYear || filters?.endYear) {
        where.year = {};
        if (filters.startYear) where.year.gte = filters.startYear;
        if (filters.endYear) where.year.lte = filters.endYear;
      }

      const reports = await prisma.hostMonthlyReport.findMany({
        where,
        orderBy: [
          { year: 'desc' },
          { month: 'desc' }
        ],
        take: filters?.limit || 12,
        skip: filters?.offset || 0
      });

      // Calcular totales
      const totalEarnings = reports.reduce((sum, r) => sum + r.totalNetEarnings, 0);
      const totalReservations = reports.reduce((sum, r) => sum + r.totalReservations, 0);
      const totalPlatformFees = reports.reduce((sum, r) => sum + r.totalPlatformFees, 0);

      return {
        reports,
        summary: {
          totalEarnings,
          totalReservations,
          totalPlatformFees,
          averageMonthlyEarnings: reports.length > 0 ? totalEarnings / reports.length : 0
        }
      };
    } catch (error) {
      console.error('Error getting host monthly reports:', error);
      throw HttpException.InternalServerError('Error al obtener reportes mensuales');
    }
  }

  /**
   * Generar reportes mensuales para todos los hosts (cron job)
   */
  static async generateAllHostsMonthlyReports(year?: number, month?: number) {
    try {
      // Si no se especifica año/mes, usar el mes anterior
      const targetDate = year && month
        ? DateTime.fromObject({ year, month })
        : DateTime.now().minus({ months: 1 });

      const targetYear = targetDate.year;
      const targetMonth = targetDate.month;

      console.log(`Generando reportes mensuales para ${targetYear}-${targetMonth.toString().padStart(2, '0')}`);

      // Obtener todos los hosts que tuvieron transacciones en ese mes
      const startDate = targetDate.startOf('month').toJSDate();
      const endDate = targetDate.endOf('month').toJSDate();

      const hostsWithTransactions = await prisma.transaction.findMany({
        where: {
          paidAt: {
            gte: startDate,
            lte: endDate
          },
          status: {
            in: ['succeeded', 'transferred']
          }
        },
        select: {
          reservation: {
            select: {
              vehicle: {
                select: {
                  hostId: true
                }
              }
            }
          }
        },
        distinct: ['reservationId']
      });

      // Extraer IDs únicos de hosts
      const uniqueHostIds = [...new Set(
        hostsWithTransactions.map(t => t.reservation.vehicle.hostId)
      )];

      console.log(`Encontrados ${uniqueHostIds.length} hosts con transacciones`);

      const results = [];
      for (const hostId of uniqueHostIds) {
        try {
          const report = await this.generateHostMonthlyReport(hostId, targetYear, targetMonth);
          results.push({
            hostId,
            success: true,
            report: report.report
          });
        } catch (error) {
          console.error(`Error generando reporte para host ${hostId}:`, error);
          results.push({
            hostId,
            success: false,
            error: error.message
          });
        }
      }

      return {
        period: { year: targetYear, month: targetMonth },
        totalHosts: uniqueHostIds.length,
        successfulReports: results.filter(r => r.success).length,
        failedReports: results.filter(r => !r.success).length,
        results
      };
    } catch (error) {
      console.error('Error generating all hosts monthly reports:', error);
      throw HttpException.InternalServerError('Error al generar reportes mensuales para todos los hosts');
    }
  }

  /**
   * Obtener estadísticas de la plataforma
   */
  static async getPlatformStats(filters?: {
    startDate?: Date;
    endDate?: Date;
  }) {
    try {
      const where: any = {
        status: {
          in: ['succeeded', 'transferred']
        }
      };

      if (filters?.startDate || filters?.endDate) {
        where.paidAt = {};
        if (filters.startDate) where.paidAt.gte = filters.startDate;
        if (filters.endDate) where.paidAt.lte = filters.endDate;
      }

      // Estadísticas de transacciones
      const transactionStats = await prisma.transaction.aggregate({
        where,
        _sum: {
          amount: true,
          platformFee: true,
          hostEarnings: true
        },
        _count: {
          id: true
        }
      });

      // Estadísticas de hosts activos
      const activeHosts = await prisma.transaction.findMany({
        where,
        select: {
          reservation: {
            select: {
              vehicle: {
                select: {
                  hostId: true
                }
              }
            }
          }
        },
        distinct: ['reservationId']
      });

      const uniqueActiveHosts = new Set(activeHosts.map(t => t.reservation.vehicle.hostId)).size;

      // Estadísticas de clientes activos
      const activeClients = await prisma.transaction.findMany({
        where,
        select: {
          reservation: {
            select: {
              userId: true
            }
          }
        },
        distinct: ['reservationId']
      });

      const uniqueActiveClients = new Set(activeClients.map(t => t.reservation.userId)).size;

      return {
        totalTransactions: transactionStats._count.id || 0,
        totalRevenue: transactionStats._sum.amount || 0,
        totalPlatformFees: transactionStats._sum.platformFee || 0,
        totalHostEarnings: transactionStats._sum.hostEarnings || 0,
        activeHosts: uniqueActiveHosts,
        activeClients: uniqueActiveClients,
        averageTransactionValue: transactionStats._count.id > 0
          ? (transactionStats._sum.amount || 0) / transactionStats._count.id
          : 0
      };
    } catch (error) {
      console.error('Error getting platform stats:', error);
      throw HttpException.InternalServerError('Error al obtener estadísticas de la plataforma');
    }
  }
}
