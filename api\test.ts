import Stripe from 'stripe';
// const stripe = require('stripe')('sk_test_51RakGwD0nJh5Sj25KhvjZGwSn2lB3i2WVPscjet2hff5vMCl7PSdwNTrnZ7dOriemq0shKChAjOShq0ye1bl0AJK00vw8scNhN');
const secret = process.env.STRIPE_SECRET_KEY!;
console.log('Secret:', secret);
const stripe = new Stripe(secret!, {
  // apiVersion: '2024-08-20',
});

const deleted = await stripe.accounts.del('acct_1RiUy7Rlzti29rfG');

console.log('Deleted:', deleted);
