import { Elysia, t } from "elysia";
import { StripeConnectService } from "./stripe-connect.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";

// Esquemas de validación
const createAccountSchema = t.Object({
  email: t.String(),
  country: t.String(),
  type: t.Union([t.Literal('express'), t.Literal('standard'), t.Literal('custom')])
});

const bankAccountSchema = t.Object({
  routingNumber: t.String(), // CLABE en México
  accountHolderName: t.String(),
  accountType: t.Union([t.Literal('checking'), t.Literal('savings')]),
  bankName: t.Optional(t.String())
});

// Controlador para hosts
export const hostStripeConnectController = new Elysia()
  .use(authMiddleware)
  .post('/host/stripe-connect/create-account',
    async ({ body, user }) => {
      const result = await StripeConnectService.createConnectedAccount(user.id, body);
      return { success: true, data: result };
    },
    {
      body: createAccountSchema
    }
  )
  .post('/host/stripe-connect/auto-create-account',
    async ({ user }) => {
      const result = await StripeConnectService.createAccountForNewHost(user.id, user.email);
      return { success: true, data: result };
    }
  )
  .get('/host/stripe-connect/status',
    async ({ user }) => {
      const status = await StripeConnectService.getAccountStatus(user.id);
      return { success: true, data: status };

    }
  )
  .post('/host/stripe-connect/onboarding-link',
    async ({ user }) => {
      const status = await StripeConnectService.getAccountStatus(user.id);

      if (!status.hasAccount) {
        return { success: false, error: 'No tienes una cuenta conectada' };
      }

      const onboardingUrl = await StripeConnectService.createOnboardingLink(status.accountId!);
      return { success: true, data: { url: onboardingUrl } };
    }
  )
  .post('/host/stripe-connect/add-bank-account',
    async ({ body, user }) => {
      const result = await StripeConnectService.addBankAccount(user.id, body);
      return { success: true, data: result };
    },
    {
      body: bankAccountSchema
    }
  )
  .get('/host/stripe-connect/bank-accounts',
    async ({ user }) => {
      const result = await StripeConnectService.getBankAccounts(user.id);
      return { success: true, data: result };
    }
  )
  .delete('/host/stripe-connect/bank-account/:bankAccountId',
    async ({ params, user }) => {
      const result = await StripeConnectService.removeBankAccount(user.id, params.bankAccountId);
      return { success: true, data: result };
    }
  )
  .put('/host/stripe-connect/bank-account/:bankAccountId',
    async ({ params, body, user }) => {
      const result = await StripeConnectService.updateBankAccount(user.id, params.bankAccountId, body);
      return { success: true, data: result };
    },
    {
      body: bankAccountSchema
    }
  )
  .get('/host/stripe-connect/bank-accounts',
    async ({ user }) => {
      const accounts = await StripeConnectService.listBankAccounts(user.id);
      return { success: true, data: accounts };
    }
  )
  .post('/host/stripe-connect/dashboard-link',
    async ({ user }) => {
      const dashboardUrl = await StripeConnectService.createDashboardLink(user.id);
      return { success: true, data: { url: dashboardUrl } };
    }
  );
