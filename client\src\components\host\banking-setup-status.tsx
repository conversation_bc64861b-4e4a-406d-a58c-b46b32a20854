"use client"

import { useQuery } from "@tanstack/react-query"
import { CheckCircle, AlertCircle, Building2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { hostBankingApi } from "@/lib/api/host-banking.api"
import { useRouter } from "next/navigation"

interface BankingSetupStatusProps {
  showFullCard?: boolean
  autoRedirect?: boolean
}

export function BankingSetupStatus({
  showFullCard = false,
  autoRedirect = false
}: BankingSetupStatusProps) {
  const router = useRouter()

  // Query para obtener información bancaria
  const { data: bankingData, isLoading } = useQuery({
    queryKey: ['host-banking-info'],
    queryFn: async () => {
      const response = await hostBankingApi.getBankingInfo()
      return response.data
    },
    staleTime: 60 * 1000, // 1 minuto
  })

  if (isLoading) {
    return showFullCard ? (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
            <span className="text-sm text-muted-foreground">Verificando configuración bancaria...</span>
          </div>
        </CardContent>
      </Card>
    ) : (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Verificando configuración bancaria...
        </AlertDescription>
      </Alert>
    )
  }

  const hasBankAccounts = bankingData?.bankAccounts && bankingData.bankAccounts.length > 0
  const hasDefaultAccount = hasBankAccounts && bankingData.defaultAccountIndex !== null

  console.log('Banking Data:', bankingData);
  console.log('Has Bank Accounts:', hasBankAccounts);
  console.log('Has Default Account:', hasDefaultAccount);

  // Si ya está configurado, mostrar estado exitoso
  if (hasDefaultAccount) {
    if (!showFullCard) {
      return (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <span className="font-medium">Sistema de pagos configurado</span>
            <span className="ml-2">- Información bancaria registrada</span>
          </AlertDescription>
        </Alert>
      )
    }

    return (
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <CheckCircle className="h-5 w-5" />
            ¡Sistema de Pagos Configurado!
          </CardTitle>
          <CardDescription className="text-green-700">
            Tu información bancaria está registrada y lista para recibir transferencias.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm text-green-700">
              <CheckCircle className="h-4 w-4" />
              <span>Información bancaria configurada</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-green-700">
              <CheckCircle className="h-4 w-4" />
              <span>Cuenta por defecto establecida</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-green-700">
              <CheckCircle className="h-4 w-4" />
              <span>Listo para recibir transferencias manuales</span>
            </div>
          </div>
          <div className="mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/host/banking')}
              className="border-green-300 text-green-700 hover:bg-green-100"
            >
              <Building2 className="h-4 w-4 mr-2" />
              Gestionar Cuentas Bancarias
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Si no está configurado, mostrar alerta para configurar
  if (!showFullCard) {
    return (
      <Alert className="border-yellow-200 bg-yellow-50">
        <AlertCircle className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-800">
          <span className="font-medium">Configuración bancaria pendiente</span>
          <span className="ml-2">- Agrega tu información bancaria para recibir pagos</span>
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Card className="border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-yellow-800">
          <Building2 className="h-5 w-5" />
          Configurar Información Bancaria
        </CardTitle>
        <CardDescription className="text-yellow-700">
          Para recibir transferencias de tus ganancias, necesitas configurar tu información bancaria.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium text-yellow-800">¿Qué necesitas?</h4>
            <ul className="space-y-1 text-sm text-yellow-700">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-yellow-600 rounded-full"></div>
                <span>Nombre del titular de la cuenta</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-yellow-600 rounded-full"></div>
                <span>CLABE interbancaria (18 dígitos)</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-yellow-600 rounded-full"></div>
                <span>Número de cuenta</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-yellow-600 rounded-full"></div>
                <span>Banco</span>
              </li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-yellow-800">Beneficios</h4>
            <ul className="space-y-1 text-sm text-yellow-700">
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Recibe transferencias de tus ganancias</span>
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Gestiona múltiples cuentas bancarias</span>
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Información segura y encriptada</span>
              </li>
            </ul>
          </div>

          <div className="pt-2">
            <Button
              onClick={() => {
                if (autoRedirect) {
                  router.push('/dashboard/host/banking')
                } else {
                  router.push('/dashboard/host/banking')
                }
              }}
              className="w-full bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              <Building2 className="h-4 w-4 mr-2" />
              Configurar Información Bancaria
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
