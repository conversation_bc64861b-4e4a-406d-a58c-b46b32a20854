import { apiService } from '@/services/api';

// Tipos para transacciones
export interface Transaction {
  id: string;
  reservationId: string;
  stripePaymentIntentId: string;
  stripeTransferId?: string;
  amount: number;
  currency: string;
  platformFeeRate: number;
  platformFee: number;
  hostEarnings: number;
  status: string;
  paymentMethod?: string;
  description?: string;
  refundAmount?: number;
  refundReason?: string;
  paidAt?: string;
  transferredAt?: string;
  refundedAt?: string;
  createdAt: string;
  updatedAt: string;
  reservation: {
    id: string;
    startDate: string;
    endDate: string;
    totalPrice: number;
    status: string;
    vehicle: {
      id: string;
      make: string;
      model: string;
      year: number;
      images?: string[];
      hostId: string;
      host: {
        id: string;
        name: string;
        email: string;
        image?: string;
      };
    };
    user?: {
      id: string;
      name: string;
      email: string;
      image?: string;
    };
  };
}

export interface TransactionFilters {
  startDate?: string;
  endDate?: string;
  status?: string;
  limit?: number;
  offset?: number;
}

export interface TransactionStats {
  totalTransactions: number;
  totalAmount: number;
  totalPlatformFees: number;
  totalHostEarnings: number;
  totalTransferred: number;
  pendingTransfer: number;
}

// Tipo específico para las estadísticas del host que devuelve el endpoint /host/transactions/stats
export interface HostTransactionStats {
  totalEarnings: number;
  totalAmount: number;
  totalPlatformFees: number;
  totalTransferred: number;
  pendingTransfer: number;
  totalTransactions: number;
  averageCommissionRate: number;
}

export interface TransactionsPaginatedResponse {
  transactions: Transaction[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    pages: number;
    currentPage: number;
  };
  stats: TransactionStats;
}

export interface TransactionsResponse {
  success: boolean;
  data: {
    transactions: Transaction[];
    stats: TransactionStats;
    pagination?: Pagination;
  };
  error?: string;
}

// Tipos para crear transacciones
export interface CreateTransactionData {
  reservationId: string;
  stripePaymentIntentId: string;
  amount: number;
  currency: string;
  paymentMethod?: string;
  description?: string;
}

export interface UpdateStripeMetadataData {
  stripePaymentIntentId: string;
  reservationId: string;
  vehicleId: string;
  hostId: string;
  clientId: string;
}

// API para usuarios autenticados
export const userTransactionsApi = {
  /**
   * Crear una nueva transacción
   */
  createTransaction: async (data: CreateTransactionData) => {
    return apiService.post<{ success: boolean; data: Transaction; error?: string }>('/user/transactions', data);
  },

  /**
   * Actualizar metadatos de Stripe
   */
  updateStripeMetadata: async (data: UpdateStripeMetadataData) => {
    return apiService.post<{ success: boolean; error?: string }>('/user/transactions/update-stripe-metadata', data);
  }
};

// API para clientes
export const clientTransactionsApi = {
  /**
   * Obtener transacciones del cliente
   */
  getTransactions: async (filters?: TransactionFilters) => {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());

    const queryString = params.toString();
    const url = `/client/transactions${queryString ? `?${queryString}` : ''}`;

    // return apiService.get<TransactionsResponse>(url);
    const result = await apiService.get<{
      success: boolean;
      data: {
        transactions: Transaction[];
        stats: TransactionStats;
        pagination: Pagination;
      };
      error?: string;
    }>(url);

    if (!result.success) {
      throw new Error(result.error || 'Error al obtener transacciones');
    }
    console.log('Transactions from [getTransactions]:', result.data);
    return result.data;
  },

  /**
   * Obtener estadísticas de pagos del cliente
   */
  getPaymentStats: async (filters?: { startDate?: string; endDate?: string }) => {
    // return clientTransactionsApi.getTransactions(filters);
    const result = await apiService.get<{
      success: boolean;
      data: {
        stats: {
          totalSpent: number;
          totalTransactions: number;
          successfulPayments: number;
        };
      };
    }>('/client/transactions/stats', { params: filters });

    if (!result.success) {
      throw new Error(result.error || 'Error al obtener estadísticas');
    }
    console.log('Stats from [getPaymentStats]:', result.data);
    return result.data;
  }
};

// API para hosts
export const hostTransactionsApi = {
  /**
   * Obtener transacciones del host
   */
  getTransactions: async (filters?: TransactionFilters) => {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());

    const queryString = params.toString();
    const url = `/host/transactions${queryString ? `?${queryString}` : ''}`;

    return apiService.get<TransactionsResponse>(url);
  },

  /**
   * Obtener estadísticas de ganancias del host
   */
  getStats: async (filters?: { startDate?: string; endDate?: string; status?: string }) => {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    if (filters?.status) params.append('status', filters.status);

    const queryString = params.toString();
    const url = `/host/transactions/stats${queryString ? `?${queryString}` : ''}`;

    const response = await apiService.get<{
      success: boolean;
      data: HostTransactionStats;
      error?: string;
    }>(url);
    if (!response.success) {
      throw new Error(response.error || 'Error al obtener estadísticas');
    }

    return response.data.data;
  },

  /**
   * Obtener datos para gráficos del host
   */
  getChartData: async (filters?: { startDate?: string; endDate?: string; groupBy?: string }) => {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    if (filters?.groupBy) params.append('groupBy', filters.groupBy);

    const queryString = params.toString();
    const url = `/host/transactions/chart-data${queryString ? `?${queryString}` : ''}`;

    const response = await apiService.get<{
      success: boolean;
      data: {
        name: string;
        ingresos: number;
        comisiones: number;
        ganancias: number;
        transacciones: number;
      }[];
      error?: string;
    }>(url);
    if (!response.success) {
      throw new Error(response.error || 'Error al obtener datos del gráfico');
    }
    console.log('Chart data from [getChartData]:', response.data);
    return response.data.data;
  },

  /**
   * Obtener estadísticas de ganancias del host (método legacy)
   */
  getEarningsStats: async (filters?: { startDate?: string; endDate?: string }) => {
    return hostTransactionsApi.getStats(filters);
  }
};

// API para administradores
export const adminTransactionsApi = {
  /**
   * Obtener todas las transacciones (admin)
   */
  getTransactions: async (filters?: TransactionFilters) => {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());

    const queryString = params.toString();
    const url = `/admin/transactions${queryString ? `?${queryString}` : ''}`;

    return apiService.get<{ success: boolean; data: TransactionsPaginatedResponse }>(url);
  },

  /**
   * Obtener estadísticas de transacciones (admin)
   */
  getStats: async (filters?: TransactionFilters) => {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    if (filters?.status) params.append('status', filters.status);

    const queryString = params.toString();
    const url = `/admin/transactions/stats${queryString ? `?${queryString}` : ''}`;

    return apiService.get<{ success: boolean; data: TransactionStats }>(url);
  },

  /**
   * Obtener datos para gráficos (admin)
   */
  getChartData: async (filters?: { groupBy?: 'day' | 'week' | 'month'; startDate?: string; endDate?: string }) => {
    const params = new URLSearchParams();
    if (filters?.groupBy) params.append('groupBy', filters.groupBy);
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);

    const queryString = params.toString();
    const url = `/admin/transactions/chart-data${queryString ? `?${queryString}` : ''}`;

    return apiService.get<{ success: boolean; data: any[] }>(url);
  },

  /**
   * Obtener una transacción específica
   */
  getTransaction: async (transactionId: string) => {
    return apiService.get<{ success: boolean; data: Transaction; error?: string }>(`/admin/transactions/${transactionId}`);
  },

  /**
   * Obtener información bancaria del host de una transacción
   */
  getHostBankingInfo: async (transactionId: string) => {
    return apiService.get<{ success: boolean; data: any; error?: string }>(`/admin/transactions/${transactionId}/host-banking`);
  },

  /**
   * Marcar transacción como transferida
   */
  markAsTransferred: async (transactionId: string) => {
    return apiService.post<{ success: boolean; data: Transaction; error?: string }>(`/admin/transactions/${transactionId}/mark-transferred`, {});
  },

  /**
   * Procesar reembolso
   */
  processRefund: async (transactionId: string, refundData: { amount?: number; reason?: string }) => {
    return apiService.post<{ success: boolean; data?: Transaction; error?: string }>(`/admin/transactions/${transactionId}/refund`, refundData);
  },

  /**
   * Procesar transferencia manual al host
   */
  processTransfer: async (transactionId: string) => {
    return apiService.post<{ success: boolean; data?: Transaction; error?: string }>(`/admin/transactions/${transactionId}/transfer`, {});
  }
};
