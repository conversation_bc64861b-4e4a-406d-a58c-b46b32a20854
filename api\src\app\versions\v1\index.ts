import { <PERSON><PERSON>, t } from "elysia";
import { prisma } from "@/lib/prisma";
import { publicVehiclesController, hostVehiclesController, adminVehiclesController } from "../../modules/vehicles/v1/vehicles.controller";
import { adminStatesController, publicStatesController } from '@/app/modules/states/v1/states.controller';
import { 
  publicReservationsController, 
  adminReservationsController, 
  userReservationsController,
hostReservationsController 
} from '@/app/modules/reservations/v1/reservations.controller';
import { adminUsersController } from '@/app/modules/users/v1/users.controller';
import { paymentsRoute } from '@/app/modules/payments/v1/payments.controller';
import { filesController } from '@/app/modules/files/v1/files.controller';
import {
  adminTransactionsController,
  hostTransactionsController,
  clientTransactionsController,
  userTransactionsController
} from '@/app/modules/transactions/v1/transactions.controller';
import {
  adminReportsController,
  hostReportsController,
  publicReportsController
} from '@/app/modules/reports/v1/reports.controller';
import {
  adminJobsController,
  hostJobsController
} from '@/app/modules/jobs/v1/jobs.controller';
import {
  hostStripeConnectController
} from '@/app/modules/stripe-connect/v1/stripe-connect.controller';
import {
  publicReviewsController,
  userReviewsController,
  adminReviewsController
} from '@/app/modules/reviews/v1/reviews.controller';
import {
  adminInvoicesController,
  hostInvoicesController,
  clientInvoicesController,
  publicInvoicesController
} from '@/app/modules/invoices/v1/invoices.controller';
import { bullmqController } from '@/app/modules/bullmq/v1/bullmq.controller';
import { clientFavoritesController } from '@/app/modules/favorites/v1/favorites.controller';
import { adminUserVerificationController, userVerificationController } from '@/app/modules/hosts/v1/user-verification.controller';
import { switchRoleController } from '@/app/controllers/user/switch-role.controller';
import { requestAdditionalRoleController } from '@/app/controllers/user/request-additional-role.controller';
import { setUserTypeController } from '@/app/controllers/user/set-user-type.controller';
import { hostBankingController, adminBankingController } from '@/app/modules/host-banking/v1/host-banking.controller';

const apiV1 = new Elysia({ prefix: '/v1' })
  .get('/', () => 'Hello Autoop from v1')

  // Registrar los controladores de vehículos
  .use(publicVehiclesController)
  .use(hostVehiclesController)
  .use(adminVehiclesController)

  // Registrar los controladores de estados
  .use(publicStatesController)
  .use(adminStatesController)

  // Registrar los controladores de reservas
  .use(publicReservationsController)
  .use(userReservationsController)
  .use(hostReservationsController)
  .use(adminReservationsController)

  // Registrar los controladores de usuarios
  .use(adminUsersController)
  .use(switchRoleController)
  .use(requestAdditionalRoleController)
  .use(setUserTypeController)

  // Registrar los controladores de pagos
  .use(paymentsRoute)

  // Registrar los controladores de transacciones
  .use(adminTransactionsController)
  .use(hostTransactionsController)
  .use(clientTransactionsController)
  .use(userTransactionsController)

  // Registrar los controladores de reportes
  .use(adminReportsController)
  .use(hostReportsController)
  .use(publicReportsController)

  // Registrar los controladores de trabajos
  .use(adminJobsController)
  .use(hostJobsController)

  // // Registrar los controladores de Stripe Connect
  .use(hostStripeConnectController)

  // Registrar los controladores de archivos
  .use(filesController)

  // Registrar los controladores de favoritos
  .use(clientFavoritesController)

  // Registrar los controladores de verificación de usuarios
  .use(userVerificationController)
  .use(adminUserVerificationController)

  // Registrar los controladores de reseñas
  .use(publicReviewsController)
  .use(userReviewsController)
  .use(adminReviewsController)

  // Registrar los controladores de facturas
  .use(adminInvoicesController)
  .use(hostInvoicesController)
  .use(clientInvoicesController)
  .use(publicInvoicesController)

  // Registrar los controladores de BullMQ
  .use(bullmqController)

  // Registrar los controladores de información bancaria
  .use(hostBankingController)
  .use(adminBankingController);

export default apiV1;


