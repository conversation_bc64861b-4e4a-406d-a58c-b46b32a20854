import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
// import { Badge } from '@/components/ui/badge';
// import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Eye, Check, X, /* MoreHorizontal, */ FileText, CreditCard, User } from 'lucide-react';
import { DateTime } from 'luxon';
import Image from 'next/image';

// Tipo para las verificaciones
type UserVerification = {
  id: string;
  userId: string;
  status: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  idFront?: string;
  idBack?: string;
  driverLicense?: string;
  addressProof?: string;
  selfieWithId?: string;
};

interface AdminVerificationTableProps {
  verifications: UserVerification[];
  onApprove: (verification: UserVerification) => void;
  onReject: (verification: UserVerification) => void;
  onViewDocument: (url: string, title: string) => void;
}

export function AdminVerificationTable({
  verifications,
  onApprove,
  onReject,
  onViewDocument
}: AdminVerificationTableProps) {
  const [expandedUser, setExpandedUser] = useState<string | null>(null);

  const toggleExpand = (userId: string) => {
    setExpandedUser(expandedUser === userId ? null : userId);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return DateTime.fromISO(dateString).setLocale('es').toFormat('dd MMM yyyy, HH:mm');
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Anfitrión</TableHead>
          <TableHead>Correo</TableHead>
          <TableHead>Fecha de solicitud</TableHead>
          <TableHead className="text-right">Acciones</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {verifications.map((verification) => (
          <>
            <TableRow key={verification.id} className="cursor-pointer hover:bg-muted/50">
              <TableCell onClick={() => toggleExpand(verification.userId)}>
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src={verification.user.image || undefined} />
                    <AvatarFallback>{getInitials(verification.user.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{verification.user.name}</div>
                    <div className="text-sm text-muted-foreground">ID: {verification.userId.slice(0, 8)}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell onClick={() => toggleExpand(verification.userId)}>
                {verification.user.email}
              </TableCell>
              <TableCell onClick={() => toggleExpand(verification.userId)}>
                {formatDate(verification.createdAt)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleExpand(verification.userId)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Ver
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-green-600 hover:bg-green-700"
                    onClick={() => onApprove(verification)}
                  >
                    <Check className="h-4 w-4 mr-1" />
                    Aprobar
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => onReject(verification)}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Rechazar
                  </Button>
                </div>
              </TableCell>
            </TableRow>

            {/* Fila expandida con documentos */}
            {expandedUser === verification.userId && (
              <TableRow className="bg-muted/30">
                <TableCell colSpan={4} className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {verification.idFront && (
                      <div className="border rounded-md p-3 bg-white">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="font-medium">ID Frontal</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewDocument(verification.idFront!, 'ID Frontal')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="relative h-40 w-full overflow-hidden rounded-md bg-muted">
                          {/* <img
                            src={verification.idFront}
                            alt="ID Frontal"
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          /> */}
                          <Image
                            src={verification.idFront}
                            alt="ID Frontal"
                            fill
                            className="object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {verification.idBack && (
                      <div className="border rounded-md p-3 bg-white">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="font-medium">ID Posterior</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewDocument(verification.idBack!, 'ID Posterior')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="relative h-40 w-full overflow-hidden rounded-md bg-muted">
                          {/* <img
                            src={verification.idBack}
                            alt="ID Posterior"
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          /> */}
                          <Image
                            src={verification.idBack}
                            alt="ID Posterior"
                            fill
                            className="object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {verification.driverLicense && (
                      <div className="border rounded-md p-3 bg-white">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="font-medium">Licencia de Conducir</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewDocument(verification.driverLicense!, 'Licencia de Conducir')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="relative h-40 w-full overflow-hidden rounded-md bg-muted">
                          {/* <img
                            src={verification.driverLicense}
                            alt="Licencia de Conducir"
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          /> */}
                          <Image
                            src={verification.driverLicense}
                            alt="Licencia de Conducir"
                            fill
                            className="object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {verification.addressProof && (
                      <div className="border rounded-md p-3 bg-white">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="font-medium">Comprobante de Domicilio</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewDocument(verification.addressProof!, 'Comprobante de Domicilio')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="relative h-40 w-full overflow-hidden rounded-md bg-muted">
                          {/*  <img
                            src={verification.addressProof}
                            alt="Comprobante de Domicilio"
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          /> */}
                          <Image
                            src={verification.addressProof}
                            alt="Comprobante de Domicilio"
                            fill
                            className="object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {verification.selfieWithId && (
                      <div className="border rounded-md p-3 bg-white">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="font-medium">Selfie con ID</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewDocument(verification.selfieWithId!, 'Selfie con ID')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="relative h-40 w-full overflow-hidden rounded-md bg-muted">
                          {/* <img
                            src={verification.selfieWithId}
                            alt="Selfie con ID"
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          /> */}
                          <Image
                            src={verification.selfieWithId}
                            alt="Selfie con ID"
                            fill
                            className="object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder.svg';
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            )}
          </>
        ))}
      </TableBody>
    </Table>
  );
}