import { apiService } from '@/services/api';

// Tipos para facturas
export interface Invoice {
  id: string;
  transactionId: string;
  providerId: string;
  invoiceNumber: string;
  externalId?: string;
  customerName: string;
  customerEmail: string;
  customerAddress?: any;
  customerTaxId?: string;
  issuerName: string;
  issuerAddress: any;
  issuerTaxId: string;
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  status: string;
  issuedAt: string;
  dueDate?: string;
  paidAt?: string;
  cancelledAt?: string;
  pdfUrl?: string;
  xmlUrl?: string;
  publicUrl?: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
  items: InvoiceItem[];
  provider: InvoiceProvider;
  transaction: {
    id: string;
    amount: number;
    status: string;
    reservation: {
      id: string;
      startDate: string;
      endDate: string;
      vehicle: {
        make: string;
        model: string;
        year: number;
        images?: string[];
      };
    };
  };
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  productCode?: string;
  unitCode?: string;
  taxRate: number;
  taxAmount: number;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceProvider {
  id: string;
  name: string;
  displayName: string;
  isActive: boolean;
  isDefault: boolean;
}

export interface InvoiceFilters {
  limit?: number;
  offset?: number;
  status?: string;
}

export interface InvoicesResponse {
  invoices: Invoice[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    pages: number;
  };
}

// API para clientes
export const clientInvoicesApi = {
  /**
   * Obtener facturas del cliente
   */
  getInvoices: async (filters?: InvoiceFilters) => {
    const params = new URLSearchParams();
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());
    if (filters?.status) params.append('status', filters.status);

    const queryString = params.toString();
    const url = `/client/invoices${queryString ? `?${queryString}` : ''}`;

    // return apiService.get<{ success: boolean; data: InvoicesResponse; error?: string }>(url);
    const response = await apiService.get<{ success: boolean; data: InvoicesResponse; error?: string }>(url);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }  
  },

  /**
   * Obtener factura específica
   */
  getInvoice: async (invoiceId: string) => {
    return apiService.get<{ success: boolean; data: Invoice; error?: string }>(`/client/invoices/${invoiceId}`);
  },

  /**
   * Descargar PDF de factura
   */
  downloadPdf: async (invoiceId: string) => {
    return apiService.get<{ success: boolean; data?: any; error?: string }>(`/client/invoices/${invoiceId}/pdf`);
  }
};

// API para hosts
export const hostInvoicesApi = {
  /**
   * Obtener facturas del host
   */
  getInvoices: async (filters?: InvoiceFilters) => {
    const params = new URLSearchParams();
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());
    if (filters?.status) params.append('status', filters.status);

    const queryString = params.toString();
    const url = `/host/invoices${queryString ? `?${queryString}` : ''}`;

    // return apiService.get<{ success: boolean; data: InvoicesResponse; error?: string }>(url);
    const response = await apiService.get<{ success: boolean; data: InvoicesResponse; error?: string }>(url);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }  
  },

  /**
   * Obtener factura específica
   */
  getInvoice: async (invoiceId: string) => {
    return apiService.get<{ success: boolean; data: Invoice; error?: string }>(`/host/invoices/${invoiceId}`);
  }
};

// API para administradores
export const adminInvoicesApi = {
  /**
   * Crear factura para transacción
   */
  createInvoice: async (transactionId: string) => {
    return apiService.post<{ success: boolean; data: Invoice; error?: string }>('/admin/invoices', {
      transactionId
    });
  },

  /**
   * Obtener todas las facturas
   */
  getInvoices: async (filters?: InvoiceFilters) => {
    const params = new URLSearchParams();
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());
    if (filters?.status) params.append('status', filters.status);

    const queryString = params.toString();
    const url = `/admin/invoices${queryString ? `?${queryString}` : ''}`;

    return apiService.get<{ success: boolean; data: InvoicesResponse; error?: string }>(url);
  },

  /**
   * Obtener factura específica
   */
  getInvoice: async (invoiceId: string) => {
    return apiService.get<{ success: boolean; data: Invoice; error?: string }>(`/admin/invoices/${invoiceId}`);
  }
};
