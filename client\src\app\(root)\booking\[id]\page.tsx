import React from 'react'
import Booking<PERSON><PERSON> from './client'
import { UserProvider } from '@/context/user-context'
import { getServerSession } from '@/actions/getSession'

export default async function BookingPage({ params: paramsProp }: { params: Promise<{ id: string }> }) {
  const params = await paramsProp
  const session = await getServerSession(undefined/* , { shouldRedirect: false } */)

  return (
    <>
      <UserProvider
        session={session}
      >
        <BookingClient params={params} />
      </UserProvider>
    </>
  )
}
