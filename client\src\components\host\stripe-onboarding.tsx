"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  CreditCard,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Clock,
  Shield,
  Banknote,
  ArrowRight
} from "lucide-react"
import { useStripeOnboarding } from "@/hooks/use-stripe-onboarding"

interface StripeOnboardingProps {
  showFullCard?: boolean
  autoRedirect?: boolean
}

export function StripeOnboarding({ showFullCard = true, /* autoRedirect = false */ }: StripeOnboardingProps) {
  const {
    onboardingStatus,
    isLoading,
    isHost,
    startOnboarding,
    isCreatingAccount,
    isCreatingOnboarding,
    goToBankingSetup
  } = useStripeOnboarding()

  // No mostrar nada si no es host
  if (!isHost) {
    return null
  }

  // Mostrar loading
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span className="text-sm text-muted-foreground">Verificando configuración de pagos...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Si ya está completamente configurado, mostrar estado exitoso
  if (onboardingStatus.isComplete) {
    if (!showFullCard) {
      return (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <span className="font-medium">Sistema de pagos configurado</span>
            <span className="ml-2">- Puedes recibir transferencias automáticas</span>
          </AlertDescription>
        </Alert>
      )
    }

    return (
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <CardTitle className="text-green-800">Sistema de Pagos Configurado</CardTitle>
          </div>
          <CardDescription className="text-green-700">
            Tu cuenta está lista para recibir transferencias automáticas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Badge variant="default" className="bg-green-100 text-green-800">
                Verificado
              </Badge>
              <span className="text-sm text-green-700">Transferencias habilitadas</span>
            </div>
            <Button variant="outline" onClick={goToBankingSetup} className="border-green-300 text-green-700 hover:bg-green-100">
              <CreditCard className="h-4 w-4 mr-2" />
              Ver Configuración
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calcular progreso
  const getProgress = () => {
    if (!onboardingStatus.hasAccount) return 0
    if (onboardingStatus.hasAccount && !onboardingStatus.canReceivePayments) return 50
    return 100
  }

  const getProgressText = () => {
    if (!onboardingStatus.hasAccount) return "Cuenta no creada"
    if (onboardingStatus.hasAccount && !onboardingStatus.canReceivePayments) return "Verificación pendiente"
    return "Configuración completa"
  }

  // Versión compacta para alertas
  if (!showFullCard) {
    return (
      <Alert className="border-orange-200 bg-orange-50">
        <AlertCircle className="h-4 w-4 text-orange-600" />
        <AlertDescription className="flex items-center justify-between w-full">
          <div className="flex-1">
            <span className="font-medium text-orange-800">
              {!onboardingStatus.hasAccount
                ? "Configuración de pagos pendiente"
                : "Verificación de cuenta pendiente"
              }
            </span>
            <p className="text-sm text-orange-700 mt-1">
              {!onboardingStatus.hasAccount
                ? "Para recibir pagos automáticos, necesitas configurar tu cuenta."
                : "Tu cuenta necesita completar la verificación para recibir pagos."
              }
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="ml-4 border-orange-300 text-orange-700 hover:bg-orange-100"
            onClick={startOnboarding}
            disabled={isCreatingAccount || isCreatingOnboarding}
          >
            {isCreatingAccount || isCreatingOnboarding ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600"></div>
            ) : (
              <>
                <CreditCard className="h-4 w-4 mr-2" />
                Configurar Ahora
              </>
            )}
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  // Versión completa del card
  return (
    <Card className="w-full border-orange-200">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <CreditCard className="h-5 w-5 text-orange-600" />
          <CardTitle className="text-orange-800">Configuración de Pagos</CardTitle>
        </div>
        <CardDescription>
          Configura tu cuenta para recibir transferencias automáticas de tus ganancias
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progreso */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progreso de configuración</span>
            <span className="font-medium">{getProgress()}%</span>
          </div>
          <Progress value={getProgress()} className="h-2" />
          <p className="text-xs text-muted-foreground">{getProgressText()}</p>
        </div>

        {/* Beneficios */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2 p-3 rounded-lg bg-blue-50 border border-blue-200">
            <Banknote className="h-4 w-4 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-blue-800">Pagos Automáticos</p>
              <p className="text-xs text-blue-600">Transferencias cada 2 días</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 p-3 rounded-lg bg-green-50 border border-green-200">
            <Shield className="h-4 w-4 text-green-600" />
            <div>
              <p className="text-sm font-medium text-green-800">Seguro y Confiable</p>
              <p className="text-xs text-green-600">Protegido por Stripe</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 p-3 rounded-lg bg-purple-50 border border-purple-200">
            <Clock className="h-4 w-4 text-purple-600" />
            <div>
              <p className="text-sm font-medium text-purple-800">Configuración Rápida</p>
              <p className="text-xs text-purple-600">Solo 5 minutos</p>
            </div>
          </div>
        </div>

        {/* Estado actual */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                {!onboardingStatus.hasAccount
                  ? "Paso 1: Crear cuenta de pagos"
                  : "Paso 2: Completar verificación"
                }
              </p>
              <p className="text-sm text-muted-foreground">
                {!onboardingStatus.hasAccount
                  ? "Crearemos tu cuenta de pagos automáticamente y te guiaremos a través del proceso de verificación."
                  : "Tu cuenta está creada pero necesita completar la verificación de identidad para recibir pagos."
                }
              </p>
            </div>
          </AlertDescription>
        </Alert>

        {/* Botón de acción */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={startOnboarding}
            disabled={isCreatingAccount || isCreatingOnboarding}
            className="flex-1"
          >
            {isCreatingAccount || isCreatingOnboarding ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <ExternalLink className="h-4 w-4 mr-2" />
            )}
            {!onboardingStatus.hasAccount
              ? "Crear Cuenta de Pagos"
              : "Completar Verificación"
            }
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
          <Button variant="outline" onClick={goToBankingSetup}>
            Ver Detalles
          </Button>
        </div>

        {/* Información adicional */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• El proceso se abre en una nueva ventana de Stripe</p>
          <p>• Necesitarás tu identificación oficial y comprobante de domicilio</p>
          <p>• La verificación puede tomar de 1-3 días hábiles</p>
        </div>
      </CardContent>
    </Card>
  )
}
