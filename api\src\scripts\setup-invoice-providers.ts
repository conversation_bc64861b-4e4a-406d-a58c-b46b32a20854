// import { prisma } from "@/lib/prisma";

// /**
//  * Script para configurar los proveedores de facturación iniciales
//  */
// async function setupInvoiceProviders() {
//   try {
//     console.log('🔧 Configurando proveedores de facturación...');

//     // Configurar proveedor de Stripe
//     const stripeProvider = await prisma.invoiceProvider.upsert({
//       where: { name: 'stripe' },
//       update: {
//         displayName: 'Stripe Invoicing',
//         isActive: true,
//         isDefault: true,
//         config: {
//           secretKey: process.env.STRIPE_SECRET_KEY || '',
//           publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
//           webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || ''
//         }
//       },
//       create: {
//         name: 'stripe',
//         displayName: 'Stripe Invoicing',
//         isActive: true,
//         isDefault: true,
//         config: {
//           secretKey: process.env.STRIPE_SECRET_KEY || '',
//           publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
//           webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || ''
//         }
//       }
//     });

//     console.log('✅ Proveedor Stripe configurado:', stripeProvider.id);

//     // Configurar proveedores futuros (comentados por ahora)
//     /*
//     const facturapiProvider = await prisma.invoiceProvider.upsert({
//       where: { name: 'facturapi' },
//       update: {
//         displayName: 'Facturapi',
//         isActive: false,
//         isDefault: false,
//         config: {
//           apiKey: process.env.FACTURAPI_API_KEY || '',
//           environment: process.env.FACTURAPI_ENVIRONMENT || 'sandbox'
//         }
//       },
//       create: {
//         name: 'facturapi',
//         displayName: 'Facturapi',
//         isActive: false,
//         isDefault: false,
//         config: {
//           apiKey: process.env.FACTURAPI_API_KEY || '',
//           environment: process.env.FACTURAPI_ENVIRONMENT || 'sandbox'
//         }
//       }
//     });

//     console.log('✅ Proveedor Facturapi configurado:', facturapiProvider.id);

//     const facturamaProvider = await prisma.invoiceProvider.upsert({
//       where: { name: 'facturama' },
//       update: {
//         displayName: 'Facturama',
//         isActive: false,
//         isDefault: false,
//         config: {
//           username: process.env.FACTURAMA_USERNAME || '',
//           password: process.env.FACTURAMA_PASSWORD || '',
//           environment: process.env.FACTURAMA_ENVIRONMENT || 'sandbox'
//         }
//       },
//       create: {
//         name: 'facturama',
//         displayName: 'Facturama',
//         isActive: false,
//         isDefault: false,
//         config: {
//           username: process.env.FACTURAMA_USERNAME || '',
//           password: process.env.FACTURAMA_PASSWORD || '',
//           environment: process.env.FACTURAMA_ENVIRONMENT || 'sandbox'
//         }
//       }
//     });

//     console.log('✅ Proveedor Facturama configurado:', facturamaProvider.id);
//     */

//     console.log('🎉 Configuración de proveedores de facturación completada');

//   } catch (error) {
//     console.error('❌ Error configurando proveedores de facturación:', error);
//     throw error;
//   }
// }

// // Ejecutar si se llama directamente
// if (require.main === module) {
//   setupInvoiceProviders()
//     .then(() => {
//       console.log('✅ Script completado exitosamente');
//       process.exit(0);
//     })
//     .catch((error) => {
//       console.error('❌ Error ejecutando script:', error);
//       process.exit(1);
//     });
// }

// export { setupInvoiceProviders };
