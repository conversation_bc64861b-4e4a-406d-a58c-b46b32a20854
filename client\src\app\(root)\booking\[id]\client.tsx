"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import BookingSteps from "@/components/booking/booking-steps"
import BookingDates from "@/components/booking/booking-dates"
import BookingReview from "@/components/booking/booking-review"
import BookingPayment from "@/components/booking/booking-payment"
import BookingConfirmation from "@/components/booking/booking-confirmation"
// import { useBookingStore } from "@/store/booking-store"
import { useUser } from "@/context/user-context"
import { Button } from "@/components/ui/button"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useBookingStoreForVehicle } from '@/hooks/use-booking-store-for-vehicle'

export default function BookingClient({ params }: { params: { id: string } }) {
  const { currentStep } = useBookingStoreForVehicle(params.id)
  const { user } = useUser()
  const router = useRouter()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Verificar si el usuario está autenticado y es de tipo cliente
    // También establecer loading a false si user es null (no autenticado)
    if (user !== undefined) {
      setLoading(false)
    }
  }, [user, router])

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Si el usuario no está autenticado, mostrar mensaje de autenticación requerida
  if (user === null) {
    return (
      <div className='min-h-[90vh] flex items-center justify-center'>
        <div className="container mx-auto py-8 px-4">
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Autenticación requerida</AlertTitle>
            <AlertDescription>
              Debes iniciar sesión para realizar una reservación.
            </AlertDescription>
          </Alert>

          <div className="flex justify-center mt-6 gap-4">
            <Button onClick={() => router.push("/auth/login")}>
              Iniciar Sesión
            </Button>
            <Button variant="outline" onClick={() => router.push("/vehicles")}>
              Volver a vehículos
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Si el usuario no es de tipo cliente, mostrar mensaje de error
  if (user && user.userType !== "client") {
    return (
      <div className='min-h-[90vh] flex items-center justify-center'>
        <div className="container mx-auto py-8 px-4">
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No puedes realizar una reservación</AlertTitle>
            <AlertDescription>
              {user.userType === "host"
                ? "No puedes realizar una reservación como usuario Host. Por favor, inicia sesión con una cuenta de cliente."
                : "No puedes realizar una reservación como Administrador. Por favor, inicia sesión con una cuenta de cliente."}
            </AlertDescription>
          </Alert>

          <div className="flex justify-center mt-6">
            <Button onClick={() => router.push("/vehicles")}>
              Volver a vehículos
            </Button>
          </div>
        </div>
      </div>

    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <div className="container mx-auto py-8 px-4">
        <BookingSteps currentStep={currentStep} />

        <div className="mt-8">
          {currentStep === 1 && <BookingDates vehicleId={params.id} />}
          {currentStep === 2 && <BookingReview vehicleId={params.id} />}
          {currentStep === 3 && <BookingPayment vehicleId={params.id} />}
          {currentStep === 4 && <BookingConfirmation />}
        </div>
      </div>
    </div>
  )
}
