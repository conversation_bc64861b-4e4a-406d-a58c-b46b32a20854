generator client {
  provider = "prisma-client-js"
}

generator json {
  provider = "prisma-json-types-generator"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
} 

model User {
  id            String   @id @default(cuid())
  name          String
  email         String   @unique
  phone         String?
  emailVerified Boolean  @default(false)
  image         String?
  role          String   @default("user")
  userType      String?
  mainUserType  String?  // Tipo de usuario original con el que se registró (nunca cambia)

  // Tipos de usuario disponibles para este usuario
  availableUserTypes String[] @default([])

  // Campo para verificación de host
  isHostVerified <PERSON>ole<PERSON> @default(false)

  // Campo para banear usuario
  banned        Boolean @default(false)

  // Razón del ban
  banReason     String?

  // Fecha de expiración del ban
  banExpires    DateTime?

  // Nuevos campos de ubicación
  country       String? @default("Mexico")
  state         String?
  timezone      String? @default("America/Mexico_City")
  
  stripeCustomerId String? // Para recibir pagos como cliente
  stripeConnectedAccountId String? // Para recibir transferencias como host

  // Relaciones existentes
  accounts      Account[]
  sessions      Session[]
  vehicles      Vehicle[]
  historyMovements HistoryMovement[]
  
  // Información adicional del usuario
  userVerification UserVerification?
  
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Soft delete
  deletedAt     BigInt?   // Unix timestamp para soft delete

  // Relación con reservas
  reservations  VehicleReservation[]

  // Relación con favoritos
  favorites     VehicleFavorite[]

  // Relación con reseñas
  vehicleReviews VehicleReview[]

  // Relación con reportes mensuales (solo para hosts)
  monthlyReports HostMonthlyReport[]

  // Relación con información bancaria (solo para hosts)
  hostBankingInfo HostBankingInfo?

  // Índice compuesto para optimizar consultas con soft delete
  @@index([id, deletedAt])
  @@map("user")
}

model Session {
  id                   String   @id @default(cuid()) // Eliminamos @map("_id")
  userId               String
  token                String   @unique
  expiresAt            DateTime
  ipAddress            String?
  userAgent            String?
  impersonatedBy       String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Soft delete
  deletedAt            BigInt?  // Unix timestamp para soft delete

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // onDelete: Cascade means that if the user is deleted, all sessions associated with that user will also be deleted

  @@index([id, deletedAt])
  @@map("session")
}

model Account {
  id                    String    @id @default(cuid()) // Eliminamos @map("_id")
  userId                String
  accountId             String
  providerId            String
  accessToken           String?
  refreshToken          String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now()) @map("created_at") // Mapeamos a snake_case
  updatedAt             DateTime  @updatedAt @map("updated_at") // Mapeamos a snake_case
  idToken               String?

  // Soft delete
  deletedAt             BigInt?   // Unix timestamp para soft delete

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Eliminamos @@map("account"), ya que no es necesario en PostgreSQL
  @@index([id, deletedAt])
  @@map("account")
}

model Verification {
  id         String   @id @default(cuid()) // Eliminamos @map("_id")
  identifier String   @map("identifier") // Mapeamos a snake_case (aunque no cambia en este caso)
  value      String   @map("value") // Mapeamos a snake_case (aunque no cambia en este caso)
  expiresAt  DateTime @map("expires_at") // Mapeamos a snake_case
  createdAt  DateTime @default(now()) @map("created_at") // Mapeamos a snake_case
  updatedAt  DateTime @updatedAt @map("updated_at") // Mapeamos a snake_case

  // Soft delete
  deletedAt  BigInt?  // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@map("verification")
}

// Modelo de vehículos
model Vehicle {
  id            String    @id @default(cuid())
  make          String    // Marca
  model         String    // Modelo
  year          Int       // Año
  color         String    // Color
  vin           String    @unique // Número de identificación del vehículo
  plate         String    @unique // Placas
  state_code    String    // Código de estado (para placa)
  country_code  String    // Código de país (para placa)
  price         Float     // Precio por día
  description   String    // Descripción
  
  // Nuevos campos estructurados
  engineSize    Float     // Motorización (litros del motor)
  transmission  String    // Transmisión (manual, automatic)
  trim          String    // Versión / Nivel de equipamiento
  bodyType      String    // Tipo de carrocería
  
  // Campos existentes
  features      Json      // Características adicionales
  amenities     String[]      // Comodidades
  images        String[]  // URLs de imágenes
  status        String    @default("pending") // pending, active, maintenance, inactive
  averageRating Float     @default(0)
  totalReviews  Int       @default(0)
  
  // Relación con el usuario (host)
  hostId        String
  host          User      @relation(fields: [hostId], references: [id], onDelete: Cascade)
  
  // Fechas de disponibilidad
  unavailableDates Json?
  approvalHistory Json[]  @default([])
  
  // Campos de auditoría
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Soft delete
  deletedAt     BigInt?   // Unix timestamp para soft delete

  // Relación con reservas
  reservations  VehicleReservation[]

  // Relación con disponibilidad
  availability          VehicleAvailability?

  // Relación con documentos
  documents             VehicleDocument?

  // Relación con favoritos
  favorites             VehicleFavorite[]

  // Relación con reseñas
  vehicleReviews        VehicleReview[]

  @@index([id, deletedAt])
  @@index([hostId, deletedAt]) // Para consultas por host
  @@index([status, deletedAt]) // Para consultas por estado
  @@map("vehicle")
}

model States {
  id            String    @id @default(cuid())
  name          String
  code          String    @unique @default("cdmx")
  countryCode   String    @default("mx")
  timezone      String?   @default("America/Mexico_City")

  active        Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Soft delete
  deletedAt     BigInt?   // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@map("states")
}

// Modelo para las reservas de vehículos
model VehicleReservation {
  id            String    @id @default(cuid())
  vehicleId     String
  vehicle       Vehicle   @relation(fields: [vehicleId], references: [id])
  userId        String
  user          User      @relation(fields: [userId], references: [id])

  hostVerification Boolean @default(false)
  hostVerificationDate DateTime?
  hostVerificationNotes String?

  startDate     DateTime
  endDate       DateTime
  totalPrice    Float
  status        String    @default("pending") // pending, confirmed, cancelled, completed

  // Campos de pago
  paymentStatus String    @default("pending") // pending, paid, refunded, failed

  /* Define quién creó la reserva/bloqueo: cliente, anfitrión o administrador */
  by            String    @default("client") // client, host, admin
  reason        String?   // Razón del bloqueo (para host/admin): mantenimiento, reparación, etc.

  // Información de contacto del cliente
  contactName   String?
  contactEmail  String?
  contactPhone  String?

  // Campos de auditoría
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Soft delete
  deletedAt     BigInt?   // Unix timestamp para soft delete

  // Relación con reseñas
  vehicleReview VehicleReview?

  // Relación con transacción
  transaction   Transaction?

  @@index([id, deletedAt])
  @@index([userId, deletedAt]) // Para consultas por usuario
  @@index([vehicleId, deletedAt]) // Para consultas por vehículo
  @@index([status, deletedAt]) // Para consultas por estado
  @@map("vehicle_reservation")
}

// Modelo para la disponibilidad de vehículos
model VehicleAvailability {
  id                    String    @id @default(cuid())
  vehicleId             String    @unique
  vehicle               Vehicle   @relation(fields: [vehicleId], references: [id])
  
  defaultCheckInTime    String    @default("14:00") // Hora predeterminada de entrega (formato 24h)
  defaultCheckOutTime   String    @default("12:00") // Hora predeterminada de devolución (formato 24h)
  minimumRentalNights   Int       @default(1)       // Mínimo de días para rentar
  maximumRentalNights   Int       @default(30)      // Máximo de días para rentar
  advanceBookingPeriod  Int       @default(5)       // Días de anticipación para reservar
  instantBooking        Boolean   @default(false)    // Si permite reserva instantánea
  allowSameDayBooking   Boolean   @default(false)   // Si permite reservar el mismo día
  cancellationPolicy    String    @default("flexible") // flexible, moderate, strict
  
  // Días disponibles para reservar
  customAvailableDays   Boolean   @default(false)   // Si usa configuración personalizada de días
  mondayAvailable       Boolean   @default(true)
  tuesdayAvailable      Boolean   @default(true)
  wednesdayAvailable    Boolean   @default(true)
  thursdayAvailable     Boolean   @default(true)
  fridayAvailable       Boolean   @default(true)
  saturdayAvailable     Boolean   @default(true)
  sundayAvailable       Boolean   @default(true)
  
  // Fechas bloqueadas manualmente
  
  // Campos de auditoría
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Soft delete
  deletedAt             BigInt?   // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@index([vehicleId, deletedAt])
  @@map("vehicle_availability")
}

// Modelo para documentos de vehículos
model VehicleDocument {
  id                    String    @id @default(cuid())
  vehicleId             String   @unique
  vehicle               Vehicle   @relation(fields: [vehicleId], references: [id], onDelete: Cascade) // Relación con el vehículo de 1 a 1

  
  plateDocument      String?
  vinDocument        String?
  registrationDocument String?
  insurancePolicyDocument String?
  
  // Campos de auditoría
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Soft delete
  deletedAt             BigInt?   // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@index([vehicleId, deletedAt])
  @@map("vehicle_document")
}

// Modelo para verificación de usuarios
model UserVerification {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Documentos de verificación
  idFront           String?  // URL del documento de identidad frontal
  idBack            String?  // URL del documento de identidad trasero
  driverLicense     String?  // URL de la licencia de conducir
  addressProof      String?  // URL del comprobante de domicilio
  selfieWithId      String?  // URL de selfie con ID

  status            String   @default("pending") // pending, approved, rejected
  notes             String?  // Notas del admin (razón de rechazo, etc.)

  // Historial de verificación
  verificationHistory Json[] @default([])

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Soft delete
  deletedAt         BigInt?  // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@index([userId, deletedAt])
  @@map("user_verification")
}

// Modelo de favoritos de vehículos
model VehicleFavorite {
  id        String   @id @default(cuid())

  // Relación con el usuario
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relación con el vehículo
  vehicleId String
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id], onDelete: Cascade)

  // Campos de auditoría
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Soft delete
  deletedAt BigInt?  // Unix timestamp para soft delete

  // Índice único para evitar duplicados
  @@unique([userId, vehicleId])
  @@index([id, deletedAt])
  @@index([userId, deletedAt])
  @@index([vehicleId, deletedAt])
  @@map("vehicle_favorite")
}

// Modelo para reseñas de vehículos
model VehicleReview {
  id            String   @id @default(cuid())

  // Relación con el usuario que hace la reseña
  userId        String
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relación con el vehículo reseñado
  vehicleId     String
  vehicle       Vehicle  @relation(fields: [vehicleId], references: [id], onDelete: Cascade)

  // Relación con la reserva específica
  reservationId String   @unique
  reservation   VehicleReservation @relation(fields: [reservationId], references: [id], onDelete: Cascade)

  // Contenido de la reseña
  rating        Int      // 1-5 estrellas (requerido)
  comment       String?  // Comentario (opcional)

  // Campos de auditoría
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Soft delete
  deletedAt     BigInt?  // Unix timestamp para soft delete

  // Índice único para evitar múltiples reseñas por reserva
  @@unique([userId, reservationId])
  @@index([id, deletedAt])
  @@index([userId, deletedAt])
  @@index([vehicleId, deletedAt])
  @@index([reservationId, deletedAt])
  @@map("vehicle_review")
}

// Modelo para configuración de la plataforma
model PlatformSettings {
  id                String   @id @default(cuid())
  commissionRate    Float    @default(0.12) // 12% comisión por defecto
  currency          String   @default("mxn")

  // Configuraciones de pago
  transferDelayHours Int     @default(24) // Horas después del check-in para transferir al host

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Soft delete
  deletedAt         BigInt?  // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@map("platform_settings")
}

// Modelo para transacciones de pago
model Transaction {
  id                    String   @id @default(cuid())

  // Relación con reserva
  reservationId         String   @unique
  reservation           VehicleReservation @relation(fields: [reservationId], references: [id], onDelete: Cascade)

  // Información del pago en Stripe
  stripePaymentIntentId String   @unique
  stripeTransferId      String?  // ID de la transferencia al host

  // Montos y cálculos
  amount                Float    // Monto total pagado por el cliente
  currency              String   @default("mxn")

  // Snapshot de comisiones (preserva valores históricos)
  platformFeeRate       Float    // Ej: 0.12 (12% al momento de la transacción)
  platformFee           Float    // Monto de comisión: amount * platformFeeRate
  hostEarnings          Float    // Lo que recibe el host: amount - platformFee

  // Estados y metadatos
  status                String   // succeeded, pending, failed, refunded, transferred
  paymentMethod         String?  // card, bank_transfer, etc.
  description           String?

  // Información de reembolsos
  refundAmount          Float?   // Monto reembolsado (si aplica)
  refundReason          String?  // Razón del reembolso

  // Fechas importantes
  paidAt                DateTime?
  transferredAt         DateTime? // Cuando se transfirió al host
  refundedAt            DateTime?

  // Campos de auditoría
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Soft delete
  deletedAt             BigInt?  // Unix timestamp para soft delete

  // Relación con factura
  invoice               Invoice?

  @@index([id, deletedAt])
  @@index([reservationId, deletedAt])
  @@index([status, deletedAt])
  @@map("transaction")
}

// Modelo para reportes mensuales de hosts
model HostMonthlyReport {
  id                String   @id @default(cuid())

  // Relación con el host
  hostId            String
  host              User     @relation(fields: [hostId], references: [id], onDelete: Cascade)

  // Período del reporte
  year              Int
  month             Int      // 1-12

  // Estadísticas del mes
  totalReservations Int      @default(0)
  totalGrossEarnings Float   @default(0) // Ingresos brutos (antes de comisión)
  totalPlatformFees Float    @default(0) // Total de comisiones pagadas
  totalNetEarnings  Float    @default(0) // Ingresos netos (después de comisión)
  totalTransferred  Float    @default(0) // Total transferido al host

  // Metadatos
  generatedAt       DateTime @default(now())

  // Soft delete
  deletedAt         BigInt?  // Unix timestamp para soft delete

  // Índice único para evitar duplicados
  @@unique([hostId, year, month])
  @@index([id, deletedAt])
  @@index([hostId, deletedAt])
  @@map("host_monthly_report")
}

// Modelo para configuración de proveedores de facturación
model InvoiceProvider {
  id                String   @id @default(cuid())

  // Información del proveedor
  name              String   // stripe, facturapi, facturama, etc.
  displayName       String   // Nombre para mostrar en UI
  isActive          Boolean  @default(true)
  isDefault         Boolean  @default(false)

  // Configuración específica del proveedor (JSON)
  config            Json     // API keys, endpoints, etc.

  // Campos de auditoría
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Soft delete
  deletedAt         BigInt?  // Unix timestamp para soft delete

  // Relación con facturas
  invoices          Invoice[]

  @@index([id, deletedAt])
  @@map("invoice_provider")
}

// Modelo para información bancaria de hosts
model HostBankingInfo {
  id                String   @id @default(cuid())

  // Relación con usuario (host)
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Array de cuentas bancarias
  /// [BankAccountInfo]
  bankAccounts      Json     // Array de objetos con información bancaria

  // Índice de la cuenta por defecto en el array
  defaultAccountIndex Int?   @default(0)

  // Campos de auditoría
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Soft delete
  deletedAt         BigInt?  // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@index([userId, deletedAt])
  @@map("host_banking_info")
}

// Modelo principal de facturas
model Invoice {
  id                String   @id @default(cuid())

  // Relación con transacción
  transactionId     String   @unique
  transaction       Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  // Relación con proveedor de facturación
  providerId        String
  provider          InvoiceProvider @relation(fields: [providerId], references: [id])

  // Información de la factura
  invoiceNumber     String   @unique // Número de factura generado por el proveedor
  externalId        String?  // ID en el sistema externo (Stripe, Facturapi, etc.)

  // Datos del cliente (snapshot al momento de la factura)
  customerName      String
  customerEmail     String
  customerAddress   Json?    // Dirección completa como JSON
  customerTaxId     String?  // RFC u otro identificador fiscal

  // Datos del emisor (snapshot al momento de la factura)
  issuerName        String
  issuerAddress     Json
  issuerTaxId       String

  // Montos y cálculos
  subtotal          Float    // Subtotal antes de impuestos
  taxAmount         Float    // Monto de impuestos
  totalAmount       Float    // Total final
  currency          String   @default("mxn")

  // Estados y fechas
  status            String   // draft, sent, paid, cancelled, refunded
  issuedAt          DateTime
  dueDate           DateTime?
  paidAt            DateTime?
  cancelledAt       DateTime?

  // URLs y archivos
  pdfUrl            String?  // URL del PDF de la factura
  xmlUrl            String?  // URL del XML (para México)
  publicUrl         String?  // URL pública para ver la factura

  // Metadatos adicionales
  metadata          Json?    // Información adicional específica del proveedor

  // Campos de auditoría
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Soft delete
  deletedAt         BigInt?  // Unix timestamp para soft delete

  // Relación con items de factura
  items             InvoiceItem[]

  @@index([id, deletedAt])
  @@index([transactionId, deletedAt])
  @@index([providerId, deletedAt])
  @@index([status, deletedAt])
  @@map("invoice")
}

// Modelo para items/líneas de factura
model InvoiceItem {
  id                String   @id @default(cuid())

  // Relación con factura
  invoiceId         String
  invoice           Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  // Información del item
  description       String   // Descripción del servicio/producto
  quantity          Float    @default(1)
  unitPrice         Float    // Precio unitario
  totalPrice        Float    // Precio total (quantity * unitPrice)

  // Clasificación fiscal (para México)
  productCode       String?  // Código de producto SAT
  unitCode          String?  // Código de unidad SAT

  // Impuestos
  taxRate           Float    @default(0) // Tasa de impuesto (ej: 0.16 para IVA 16%)
  taxAmount         Float    @default(0) // Monto de impuesto

  // Campos de auditoría
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Soft delete
  deletedAt         BigInt?  // Unix timestamp para soft delete

  @@index([id, deletedAt])
  @@index([invoiceId, deletedAt])
  @@map("invoice_item")
}


model HistoryMovement {
  id          String   @id @default(uuid())
  entityType  String   // Nombre de la tabla (ej: "Vehicle", "User", "Booking")
  entityId    String   // ID del registro afectado
  action      String   // "create", "update", "delete"
  userId      String   // ID del usuario que realizó la acción
  timestamp   DateTime @default(now())
  changes     Json?    // Cambios realizados (opcional)
  
  user        User     @relation(fields: [userId], references: [id])
}