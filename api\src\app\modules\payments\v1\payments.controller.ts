import { Elysia, t } from 'elysia'
import Stripe from 'stripe'
import { prisma } from "@/lib/prisma";
import { authMiddleware } from '@/app/middlewares/auth.middleware';
import { TransactionsService } from '../../transactions/v1/transactions.service';

// Inicializar <PERSON>e con la clave secreta
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  // apiVersion: '2024-11-20.acacia',
})

export const paymentsRoute = new Elysia()
  .use(authMiddleware)
  .post('/payments',
    async ({ body }) => {
      const {
        amount,
        currency,
        customer_email,
        customer_name,
        payment_method_id,
        description,
        reservationId
      } = body

      try {
        // Buscar o crear un cliente en Stripe
        let customer
        const existingCustomers = await stripe.customers.list({
          email: customer_email,
          limit: 1,
        })

        if (existingCustomers.data.length > 0) {
          customer = existingCustomers.data[0]
        } else {
          // Crear un nuevo cliente
          customer = await stripe.customers.create({
            email: customer_email,
            name: customer_name,
            payment_method: payment_method_id,
          })

          // Opcional: Guardar el ID del cliente en tu base de datos
          // Buscar si el usuario existe en la base de datos
          const user = await prisma.user.findUnique({
            where: { email: customer_email },
          })

          if (user) {
            // Actualizar el usuario con el ID de Stripe
            await prisma.user.update({
              where: { id: user.id },
              data: {
                stripeCustomerId: customer.id
              },
            })
          }
        }

        /* 
            const payment = await stripe.paymentIntents.create({
      amount: amountWithTwoZeros,
      currency: 'mxn',
      description,
      payment_method: id,
      confirm: true,
      customer: clientId,
      payment_method_types: [
        'card',
      ],
    });
        */

        // Crear un PaymentIntent
        const paymentIntent = await stripe.paymentIntents.create({
          amount,
          currency,
          customer: customer.id,
          payment_method: payment_method_id,
          description,
          confirm: true,
          payment_method_types: [
            'card',
          ],
        })

        // Manejar diferentes estados del PaymentIntent
        if (
          paymentIntent.status === 'requires_action' &&
          paymentIntent.next_action
        ) {
          // El pago requiere autenticación adicional
          return {
            success: true,
            requires_action: true,
            client_secret: paymentIntent.client_secret,
          }
        } else if (paymentIntent.status === 'succeeded') {
          // El pago se completó exitosamente

          // Crear registro de transacción en nuestra base de datos
          if (reservationId) {
            try {
              await TransactionsService.createTransaction({
                reservationId,
                stripePaymentIntentId: paymentIntent.id,
                amount: amount / 100, // Convertir de centavos a pesos
                currency,
                paymentMethod: paymentIntent.payment_method_types[0],
                description
              });
            } catch (transactionError) {
              console.error('Error creating transaction record:', transactionError);
              // No fallar el pago por error en el registro, pero loggearlo
            }
          }

          return {
            success: true,
            status: paymentIntent.status,
            payment_intent_id: paymentIntent.id,
          }
        } else {
          // Otro estado
          return {
            success: true,
            status: paymentIntent.status,
            payment_intent_id: paymentIntent.id,
          }
        }
      } catch (error) {
        console.error('Error processing payment:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Error processing payment'
        }
      }
    },
    {
      body: t.Object({
        amount: t.Number(),
        currency: t.String(),
        customer_email: t.String(),
        customer_name: t.String(),
        payment_method_id: t.String(),
        description: t.Optional(t.String()),
        reservationId: t.Optional(t.String())
      })
    }
  )