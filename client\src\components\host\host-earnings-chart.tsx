"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "lucide-react"
import { AdminDateRangeModal } from "@/components/admin/AdminDateRangeModal"
import { hostTransactionsApi } from "@/lib/api/transactions.api"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import {
  LineChart,
  BarChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts"

interface DateRange {
  from: Date
  to: Date
}

// Función para obtener el primer y último día de la semana actual
function getCurrentWeekRange() {
  const now = new Date()
  const firstDay = new Date(now)
  const day = now.getDay() // 0 = domingo, 1 = lunes, etc.
  const diff = now.getDate() - day + (day === 0 ? -6 : 1) // Ajustar cuando es domingo
  firstDay.setDate(diff)
  firstDay.setHours(0, 0, 0, 0)

  const lastDay = new Date(firstDay)
  lastDay.setDate(firstDay.getDate() + 6)
  lastDay.setHours(23, 59, 59, 999)

  return { from: firstDay, to: lastDay }
}

// Función para obtener el primer y último día del mes actual
function getCurrentMonthRange() {
  const now = new Date()
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)

  return { from: firstDay, to: lastDay }
}

export function HostEarningsChart() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: getCurrentMonthRange().from,
    to: getCurrentMonthRange().to,
  })
  const [tab, setTab] = useState("month")
  const [chartType, setChartType] = useState("line")
  const [showDatePicker, setShowDatePicker] = useState(false)

  // Actualizar el rango de fechas cuando cambia la pestaña
  const handleTabChange = (value: string) => {
    setTab(value)
    if (value === "week") {
      setDateRange(getCurrentWeekRange())
    } else if (value === "month") {
      setDateRange(getCurrentMonthRange())
    }
  }

  // Manejar selección de rango de fechas
  const handleDateRangeSelect = (ranges: any) => {
    const { startDate, endDate } = ranges.selection
    if (startDate && endDate) {
      setDateRange({ from: startDate, to: endDate })
      setShowDatePicker(false)
    }
  }

  // Query para obtener datos del gráfico
  const { data: chartData, isLoading, /* error */ } = useQuery({
    queryKey: ['host-chart-data', dateRange, tab],
    queryFn: async () => {
      if (!dateRange?.from || !dateRange?.to) return []

      const groupBy = tab === 'week' ? 'day' : tab === 'month' ? 'day' : 'week'
      const response = await hostTransactionsApi.getChartData({
        groupBy,
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
      })
      return response;
    },
    staleTime: 30 * 1000, // 30 segundos
  })


  return (
    <Card>
      <CardHeader className="space-y-4">
        <div>
          <CardTitle>Ganancias Mensuales</CardTitle>
          <CardDescription>Tus ingresos por renta de vehículos en el período seleccionado</CardDescription>
        </div>

        <div className="flex flex-col gap-3">
          <div className="flex items-center gap-2">
            <Select value={tab} onValueChange={handleTabChange}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Esta Semana</SelectItem>
                <SelectItem value="month">Este Mes</SelectItem>
                <SelectItem value="custom">Personalizado</SelectItem>
              </SelectContent>
            </Select>

            <Select value={chartType} onValueChange={setChartType}>
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">Líneas</SelectItem>
                <SelectItem value="bar">Barras</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDatePicker(true)}
            className="w-fit"
          >
            <Calendar className="mr-2 h-4 w-4" />
            {dateRange?.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, "dd/MM/yyyy", { locale: es })} -{" "}
                  {format(dateRange.to, "dd/MM/yyyy", { locale: es })}
                </>
              ) : (
                format(dateRange.from, "dd/MM/yyyy", { locale: es })
              )
            ) : (
              <span>Seleccionar fechas</span>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-muted-foreground">Cargando datos...</div>
            </div>
          ) : chartData && chartData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              {chartType === "line" ? (
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    tickFormatter={(value) => {
                      const date = new Date(value)
                      return format(date, "dd/MM", { locale: es })
                    }}
                  />
                  <YAxis
                    tickFormatter={(value) => `$${value.toLocaleString()}`}
                  />
                  <Tooltip
                    formatter={(value: number, name: string) => {
                      let displayName = name;
                      if (name === 'ganancias') displayName = 'Ganancias';
                      else if (name === 'ingresos') displayName = 'Ingresos Totales';
                      else if (name === 'comisiones') displayName = 'Comisiones';

                      return [`$${value.toLocaleString()}`, displayName];
                    }}
                    labelFormatter={(label) => {
                      const date = new Date(label)
                      return format(date, "dd/MM/yyyy", { locale: es })
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="ganancias"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Ganancias"
                  />
                  <Line
                    type="monotone"
                    dataKey="ingresos"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    name="Ingresos Totales"
                  />
                  <Line
                    type="monotone"
                    dataKey="comisiones"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="Comisiones"
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    tickFormatter={(value) => {
                      const date = new Date(value)
                      return format(date, "dd/MM", { locale: es })
                    }}
                  />
                  <YAxis
                    tickFormatter={(value) => `$${value.toLocaleString()}`}
                  />
                  <Tooltip
                    formatter={(value: number, name: string) => {
                      let displayName = name;
                      if (name === 'ganancias') displayName = 'Ganancias';
                      else if (name === 'ingresos') displayName = 'Ingresos Totales';
                      else if (name === 'comisiones') displayName = 'Comisiones';

                      return [`$${value.toLocaleString()}`, displayName];
                    }}
                    labelFormatter={(label) => {
                      const date = new Date(label)
                      return format(date, "dd/MM/yyyy", { locale: es })
                    }}
                  />
                  <Legend />
                  <Bar dataKey="ganancias" fill="#10b981" name="Ganancias" />
                  <Bar dataKey="ingresos" fill="#3b82f6" name="Ingresos Totales" />
                  <Bar dataKey="comisiones" fill="#f59e0b" name="Comisiones" />
                </BarChart>
              )}
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-muted-foreground">
                    <p>No hay datos disponibles para el período seleccionado</p>
                  </div>
                </div>
          )}
        </div>
      </CardContent>

      {/* Modal de Selección de Fechas */}
      <AdminDateRangeModal
        open={showDatePicker}
        onOpenChange={setShowDatePicker}
        onDateRangeSelect={handleDateRangeSelect}
        initialRange={dateRange ? {
          startDate: dateRange.from,
          endDate: dateRange.to,
          key: 'selection'
        } : undefined}
      />
    </Card>
  )
}
