"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar as CalendarIcon, Download } from "lucide-react"
import { DateRange } from "react-day-picker"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { AdminDateRangeModal } from "@/components/admin/AdminDateRangeModal"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { adminTransactionsApi } from "@/lib/api/transactions.api"
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import {
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  Line,
  Bar,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts"

// Función para obtener el primer y último día de la semana actual
function getCurrentWeekRange() {
  const now = new Date()
  const firstDay = new Date(now)
  const day = now.getDay() // 0 = domingo, 1 = lunes, etc.
  const diff = now.getDate() - day + (day === 0 ? -6 : 1) // Ajustar cuando es domingo
  firstDay.setDate(diff)
  firstDay.setHours(0, 0, 0, 0)

  const lastDay = new Date(firstDay)
  lastDay.setDate(lastDay.getDate() + 6)
  lastDay.setHours(23, 59, 59, 999)

  return { from: firstDay, to: lastDay }
}

// Función para obtener el primer y último día del mes actual
function getCurrentMonthRange() {
  const now = new Date()
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  lastDay.setHours(23, 59, 59, 999)

  return { from: firstDay, to: lastDay }
}

export default function AdminEarningsPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: getCurrentWeekRange().from,
    to: getCurrentWeekRange().to,
  })
  const [tab, setTab] = useState("week")
  const [chartType, setChartType] = useState("line")
  const [showDatePicker, setShowDatePicker] = useState(false)

  // Actualizar el rango de fechas cuando cambia la pestaña
  const handleTabChange = (value: string) => {
    setTab(value)
    if (value === "week") {
      setDateRange(getCurrentWeekRange())
    } else if (value === "month") {
      setDateRange(getCurrentMonthRange())
    }
  }

  // Manejar selección de rango de fechas
  const handleDateRangeSelect = (ranges: any) => {
    const { startDate, endDate } = ranges.selection
    if (startDate && endDate) {
      setDateRange({ from: startDate, to: endDate })
      setShowDatePicker(false)
    }
  }

  // Función para exportar datos de ganancias
  const handleExportData = async () => {
    try {
      if (!dateRange?.from || !dateRange?.to) {
        console.error('Rango de fechas no seleccionado')
        return
      }

      // Obtener datos de estadísticas
      const statsResponse = await adminTransactionsApi.getStats({
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
      })

      // Obtener datos de gráficos
      const chartResponse = await adminTransactionsApi.getChartData({
        groupBy: tab === 'week' ? 'day' : 'week',
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
      })

      if (!statsResponse.success || !chartResponse.success) {
        throw new Error('Error al obtener datos para exportar')
      }

      const stats = statsResponse.data?.data || {}
      const chartData = chartResponse.data?.data || []

      // Crear workbook
      const wb = XLSX.utils.book_new()

      // Hoja 1: Resumen de estadísticas
      const statsData = [
        ['Métrica', 'Valor'],
        ['Total de Transacciones', stats.totalTransactions || 0],
        ['Total de Ingresos', `$${(stats.totalAmount || 0).toLocaleString()}`],
        ['Total de Comisiones', `$${(stats.totalPlatformFees || 0).toLocaleString()}`],
        ['Ganancias de Hosts', `$${(stats.totalHostEarnings || 0).toLocaleString()}`],
        ['Total Transferido', `$${(stats.totalTransferred || 0).toLocaleString()}`],
        ['Pendiente de Transferir', `$${(stats.pendingTransfer || 0).toLocaleString()}`],
        ['Período', `${format(dateRange.from, "dd/MM/yyyy", { locale: es })} - ${format(dateRange.to, "dd/MM/yyyy", { locale: es })}`]
      ]

      const statsWs = XLSX.utils.aoa_to_sheet(statsData)
      statsWs['!cols'] = [{ wch: 25 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(wb, statsWs, 'Resumen')

      // Hoja 2: Datos del gráfico
      if (chartData.length > 0) {
        const chartDataFormatted = chartData.map((item: any) => ({
          'Período': item.name,
          'Ingresos': `$${(item.ingresos || 0).toLocaleString()}`,
          'Comisiones': `$${(item.comisiones || 0).toLocaleString()}`,
          'Transacciones': item.transacciones || 0
        }))

        const chartWs = XLSX.utils.json_to_sheet(chartDataFormatted)
        chartWs['!cols'] = [{ wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 }]
        XLSX.utils.book_append_sheet(wb, chartWs, 'Datos por Período')
      }

      // Generar archivo y descargar
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

      const fileName = `ganancias_${format(dateRange.from, "yyyy-MM-dd")}_${format(dateRange.to, "yyyy-MM-dd")}.xlsx`
      saveAs(data, fileName)

    } catch (error) {
      console.error('Error al exportar datos:', error)
    }
  }

  // Query para obtener estadísticas
  const { data: statsData, isLoading } = useQuery({
    queryKey: ['admin-earnings-stats', dateRange?.from?.toISOString(), dateRange?.to?.toISOString()],
    queryFn: async () => {
      if (!dateRange?.from || !dateRange?.to) return null

      const response = await adminTransactionsApi.getStats({
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
      })

      if (!response.success) {
        throw new Error(response.error || 'Error al obtener estadísticas')
      }

      return response.data
    },
    enabled: !!dateRange?.from && !!dateRange?.to,
    staleTime: 60 * 1000, // 1 minuto
  })

  // Query para obtener datos de gráficos
  const { data: chartData, isLoading: chartLoading } = useQuery({
    queryKey: ['admin-earnings-chart', tab, dateRange?.from?.toISOString(), dateRange?.to?.toISOString()],
    queryFn: async () => {
      if (!dateRange?.from || !dateRange?.to) return null

      const groupBy = tab === 'week' ? 'day' : 'week'
      const response = await adminTransactionsApi.getChartData({
        groupBy,
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
      })

      if (!response.success) {
        throw new Error(response.error || 'Error al obtener datos del gráfico')
      }

      return response.data
    },
    enabled: !!dateRange?.from && !!dateRange?.to,
    staleTime: 60 * 1000, // 1 minuto
  })

  // Datos reales para los gráficos
  const realChartData = chartData?.data || []

  // Datos por defecto cuando no hay datos reales
  const defaultWeeklyData = [
    { name: 'Lunes', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Martes', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Miércoles', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Jueves', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Viernes', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Sábado', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Domingo', ingresos: 0, comisiones: 0, transacciones: 0 },
  ]

  const defaultMonthlyData = [
    { name: 'Semana 1', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Semana 2', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Semana 3', ingresos: 0, comisiones: 0, transacciones: 0 },
    { name: 'Semana 4', ingresos: 0, comisiones: 0, transacciones: 0 },
  ]

  // Usar datos reales si están disponibles, sino usar datos por defecto
  const weeklyData = realChartData.length > 0 && tab === 'week' ? realChartData : defaultWeeklyData
  const monthlyData = realChartData.length > 0 && tab === 'month' ? realChartData : defaultMonthlyData

  // Estadísticas generales
  const stats = statsData?.data || {
    totalTransactions: 0,
    totalAmount: 0,
    totalPlatformFees: 0,
    totalHostEarnings: 0,
    totalTransferred: 0,
    pendingTransfer: 0
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Análisis de Ganancias</h1>
        <Button variant="outline" onClick={handleExportData}>
          <Download className="mr-2 h-4 w-4" />
          Exportar Datos
        </Button>
      </div>

      <Tabs defaultValue="week" value={tab} onValueChange={handleTabChange}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="week">Esta Semana</TabsTrigger>
            <TabsTrigger value="month">Este Mes</TabsTrigger>
            <TabsTrigger value="custom">Personalizado</TabsTrigger>
          </TabsList>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className={cn(
                "justify-start text-left font-normal",
                !dateRange && "text-muted-foreground"
              )}
              onClick={() => setShowDatePicker(true)}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, "dd/MM/yyyy", { locale: es })} -{" "}
                    {format(dateRange.to, "dd/MM/yyyy", { locale: es })}
                  </>
                ) : (
                  format(dateRange.from, "dd/MM/yyyy", { locale: es })
                )
              ) : (
                <span>Seleccionar fechas</span>
              )}
            </Button>

            <Select value={chartType} onValueChange={setChartType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Tipo de gráfico" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">Líneas</SelectItem>
                <SelectItem value="bar">Barras</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Ingresos</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-8 bg-gray-200 animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-bold">${(stats.totalAmount || 0).toLocaleString()}</div>
              )}
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Comisiones</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  <div className="h-8 bg-gray-200 animate-pulse rounded"></div>
                  <div className="h-4 bg-gray-200 animate-pulse rounded w-20"></div>
                </div>
              ) : (
                <>
                  <div className="text-2xl font-bold">${(stats.totalPlatformFees || 0).toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.totalAmount ? Math.round(((stats.totalPlatformFees || 0) / stats.totalAmount) * 100) : 0}% del total
                  </p>
                </>
              )}
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Transacciones</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-8 bg-gray-200 animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-bold">{stats.totalTransactions || 0}</div>
              )}
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pendientes de Transferir</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-8 bg-gray-200 animate-pulse rounded"></div>
              ) : (
                <div className="text-2xl font-bold">${(stats.pendingTransfer || 0).toLocaleString()}</div>
              )}
            </CardContent>
          </Card>
        </div>

        <TabsContent value="week" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Ganancias de la Semana</CardTitle>
              <CardDescription>
                Ingresos y comisiones del {format(dateRange?.from || new Date(), "dd/MM/yyyy")} al {format(dateRange?.to || new Date(), "dd/MM/yyyy")}
                {realChartData.length === 0 && !chartLoading && (
                  <span className="block text-amber-600 mt-1">
                    No hay transacciones en este período
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {chartLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="mt-2 text-sm text-muted-foreground">Cargando datos del gráfico...</p>
                    </div>
                  </div>
                ) : chartType === "line" ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={weeklyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Line type="monotone" dataKey="ingresos" stroke="#8884d8" name="Ingresos Totales" />
                      <Line type="monotone" dataKey="comisiones" stroke="#82ca9d" name="Comisiones" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={weeklyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Bar dataKey="ingresos" fill="#8884d8" name="Ingresos Totales" />
                      <Bar dataKey="comisiones" fill="#82ca9d" name="Comisiones" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="month" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Ganancias del Mes</CardTitle>
              <CardDescription>
                Ingresos y comisiones del {format(dateRange?.from || new Date(), "dd/MM/yyyy")} al {format(dateRange?.to || new Date(), "dd/MM/yyyy")}
                {realChartData.length === 0 && !chartLoading && (
                  <span className="block text-amber-600 mt-1">
                    No hay transacciones en este período
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {chartLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="mt-2 text-sm text-muted-foreground">Cargando datos del gráfico...</p>
                    </div>
                  </div>
                ) : chartType === "line" ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Line type="monotone" dataKey="ingresos" stroke="#8884d8" name="Ingresos Totales" />
                      <Line type="monotone" dataKey="comisiones" stroke="#82ca9d" name="Comisiones" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Bar dataKey="ingresos" fill="#8884d8" name="Ingresos Totales" />
                      <Bar dataKey="comisiones" fill="#82ca9d" name="Comisiones" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Ganancias Personalizadas</CardTitle>
              <CardDescription>
                Ingresos y comisiones del {format(dateRange?.from || new Date(), "dd/MM/yyyy")} al {format(dateRange?.to || new Date(), "dd/MM/yyyy")}
                {realChartData.length === 0 && !chartLoading && (
                  <span className="block text-amber-600 mt-1">
                    No hay transacciones en este período
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {chartLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                      <p className="mt-2 text-sm text-muted-foreground">Cargando datos del gráfico...</p>
                    </div>
                  </div>
                ) : chartType === "line" ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={realChartData.length > 0 ? realChartData : monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Line type="monotone" dataKey="ingresos" stroke="#8884d8" name="Ingresos Totales" />
                      <Line type="monotone" dataKey="comisiones" stroke="#82ca9d" name="Comisiones" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={realChartData.length > 0 ? realChartData : monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, '']} />
                      <Legend />
                      <Bar dataKey="ingresos" fill="#8884d8" name="Ingresos Totales" />
                      <Bar dataKey="comisiones" fill="#82ca9d" name="Comisiones" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal de Selección de Fechas */}
      <AdminDateRangeModal
        open={showDatePicker}
        onOpenChange={setShowDatePicker}
        onDateRangeSelect={handleDateRangeSelect}
        initialRange={dateRange ? {
          startDate: dateRange.from!,
          endDate: dateRange.to!,
          key: 'selection'
        } : undefined}
      />
    </div>
  )
}
