"use client"

import { useState } from "react"
// import { useRouter } from "next/navigation"
import { /* Search, */ /* <PERSON>, */ Menu, Sun, Moon } from "lucide-react"
import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { She<PERSON>, SheetContent, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { AdminSidebar } from "./admin-sidebar"
import { HostSidebar } from "./host-sidebar"
import { ClientSidebar } from "./client-sidebar"
import { useUser } from '@/context/user-context'

export function TopBar() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { setTheme } = useTheme()
  const { user } = useUser()


  const getSidebarComponent = () => {

    // check first if it's admin, then check usertype
    if (user.role === "admin") {
      return <AdminSidebar />
    } else if (user.userType === "host") {
      return <HostSidebar />
    } else if (user.userType === "client") {
      return <ClientSidebar />
    }
  }

  // const getSearchPlaceholder = () => {

  //   if (user.role === "admin") {
  //     return "Buscar usuarios, vehículos..."
  //   } else if (user.userType === "host") {
  //     return "Buscar en mis vehículos..."
  //   } else if (user.userType === "client") {
  //     return "Buscar vehículos..."
  //   }
  // }

  return (
    <header className="bg-background border-b border-border px-4 py-3 flex items-center justify-between">
      <div className="flex items-center gap-4">
        {/* Mobile Sidebar - solo visible en mobile */}
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetTitle className="sr-only">Menú</SheetTitle>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="md:hidden">
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-72 border-none text-white">
            {getSidebarComponent()}
          </SheetContent>
        </Sheet>

        {/* Search */}
        {/* <div className="relative hidden md:flex items-center">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder={getSearchPlaceholder()}
            className="pl-8 w-[300px] bg-background"
          />
        </div> */}
      </div>

      <div className="flex items-center gap-3">
        {/* Theme Toggle */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setTheme("light")}>Light</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("dark")}>Dark</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("system")}>System</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Notifications */}
        {/* <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-primary" />
          <span className="sr-only">Notifications</span>
        </Button> */}

      </div>
    </header>
  )
}
