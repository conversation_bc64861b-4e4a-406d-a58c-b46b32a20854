import { Elysia, t } from "elysia";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';
import { prisma } from "@/lib/prisma";
import {
  scheduleHostTransfer,
  scheduleMonthlyReports,
  processPendingTransfers
} from "../../../jobs/payment-jobs";
import { ReservationsService } from "../../reservations/v1/reservations.service";

// Controlador para administradores - gestión de trabajos
export const adminJobsController = new Elysia()
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .post('/admin/jobs/process-pending-transfers',
    async () => {
      const result = await processPendingTransfers();
      return { success: true, data: result };
    }
  )
  .post('/admin/jobs/generate-monthly-reports',
    async ({ body }) => {
      const job = await scheduleMonthlyReports(body.year, body.month);

      return {
        success: true,
        data: {
          jobId: job.id,
          message: 'Generación de reportes mensuales programada'
        }
      };

    },
    {
      body: t.Object({
        year: t.Optional(t.Number()),
        month: t.Optional(t.Number())
      })
    }
  )
  .post('/admin/jobs/schedule-transfer/:reservationId',
    async ({ params }) => {
      const result = await ReservationsService.scheduleTransferForReservation(params.reservationId);
      return { success: true, data: result };
    },
    {
      params: t.Object({
        reservationId: t.String()
      })
    }
  )
  .get('/admin/jobs/queue-status',
    async () => {
      try {
        // Aquí podrías obtener el estado de las colas de BullMQ
        // Por ahora devolvemos información básica
        return {
          success: true,
          data: {
            transferQueue: {
              name: 'host-transfers',
              status: 'active'
            },
            reportsQueue: {
              name: 'monthly-reports',
              status: 'active'
            }
          }
        };
      } catch (error) {
        console.error('Error getting queue status:', error);
        return { success: false, error: 'Error al obtener estado de las colas' };
      }
    }
  );

// Controlador para hosts - trabajos relacionados con sus datos
export const hostJobsController = new Elysia()
  .use(authMiddleware)
  .post('/host/jobs/request-transfer/:reservationId',
    async ({ params, user }) => {
      // Verificar que la reserva pertenece al host
      const reservation = await prisma.vehicleReservation.findFirst({
        where: {
          id: params.reservationId,
          vehicle: {
            hostId: user.id
          }
        },
        include: {
          transaction: true
        }
      });

      if (!reservation) {
        return { success: false, error: 'Reserva no encontrada o no autorizada' };
      }

      if (!reservation.transaction) {
        return { success: false, error: 'La reserva no tiene una transacción asociada' };
      }

      if (reservation.transaction.status !== 'succeeded') {
        if (reservation.transaction.status === 'transferred') {
          return { success: false, error: 'La transferencia ya fue procesada' };
        }
        return { success: false, error: 'La transacción no está en estado válido para transferir' };
      }


      const result = await ReservationsService.scheduleTransferForReservation(params.reservationId);
      return { success: true, data: result };

    },
    {
      params: t.Object({
        reservationId: t.String()
      })
    }
  );
