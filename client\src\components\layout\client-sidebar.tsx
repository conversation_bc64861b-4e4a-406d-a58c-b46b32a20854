"use client"

import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import {
  LayoutDashboard,
  Search,
  CalendarClock,
  CreditCard,
  Heart,
  // FileText,
  // MessageSquare,
  // Settings,
  // History,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { UserProfileSidebar } from "./user-profile-sidebar"

const menuItems = [
  {
    title: "PRINCIPAL",
    items: [
      {
        name: "Dashboard",
        href: "/dashboard/client",
        icon: LayoutDashboard,
      },
      {
        name: "Buscar Vehículos",
        href: "/dashboard/client/search",
        icon: Search,
      },
      {
        name: "Mis Reservas",
        href: "/dashboard/client/reservations",
        icon: CalendarClock,
        badge: undefined,
      },
      // {
      //   name: "Historial",
      //   href: "/dashboard/client/history",
      //   icon: History,
      // },
    ],
  },
  {
    title: "GESTIÓN",
    items: [
      {
        name: "Pagos",
        href: "/dashboard/client/payments",
        icon: CreditCard,
      },
      // {
      //   name: "<PERSON>act<PERSON><PERSON>",
      //   href: "/dashboard/client/invoices",
      //   icon: FileText,
      // },
      {
        name: "Favoritos",
        href: "/dashboard/client/favorites",
        icon: Heart,
      },
      // {
      //   name: "Mensajes",
      //   href: "/dashboard/client/messages",
      //   icon: MessageSquare,
      // },
    ],
  },
  // {
  //   title: "CONFIGURACIÓN",
  //   items: [
  //     {
  //       name: "Configuración",
  //       href: "/dashboard/client/settings",
  //       icon: Settings,
  //     },
  //   ],
  // },
]


export function ClientSidebar() {
  const pathname = usePathname()

  return (
    <div className="w-full bg-[#0f2a5c] dark:bg-gray-900 text-white flex flex-col h-screen">
      {/* Header */}
      <div className="p-4 flex items-center border-b border-white/10">
        <div className="flex items-center">
          <div className="bg-white rounded-md p-1 mr-2">
            <Image src="/placeholder.svg?height=24&width=24" alt="Autoop Logo" width={24} height={24} />
          </div>
          <span className="text-xl font-bold">Autoop</span>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-4">
        {menuItems.map((section, i) => (
          <div key={i} className="px-4 py-2">
            <div className="text-xs font-semibold text-gray-400 mb-2">{section.title}</div>
            <ul className="space-y-1">
              {section.items.map((item, j) => (
                <li key={j}>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                      pathname === item.href
                        ? "bg-white/10 text-white"
                        : "text-gray-300 hover:bg-white/5 hover:text-white",
                    )}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    <span>{item.name}</span>
                    {item.badge && (
                      <span className="ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* User Profile - Usando el componente reutilizable */}
      <UserProfileSidebar />
    </div>
  )
}
