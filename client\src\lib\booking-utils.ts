/**
 * Utilidades para el manejo del localStorage del sistema de booking
 * Soporta múltiples procesos de reserva simultáneos por vehículo
 */

export interface BookingStorageData {
  bookingId: string
  expirationDate: string
  createdAt: string
  vehicleId: string
}

export interface VehicleBookingStorage {
  [vehicleId: string]: BookingStorageData
}

/**
 * Obtiene todas las reservas almacenadas en localStorage
 * @returns Objeto con todas las reservas por vehículo
 */
function getAllBookingsFromStorage(): VehicleBookingStorage {
  try {
    const stored = localStorage.getItem('autoop-vehicle-bookings')
    if (!stored) return {}

    const bookings: VehicleBookingStorage = JSON.parse(stored)
    return bookings || {}
  } catch (error) {
    console.error('Error al leer bookings del localStorage:', error)
    return {}
  }
}

/**
 * Guarda todas las reservas en localStorage
 * @param bookings - Objeto con todas las reservas por vehículo
 */
function saveAllBookingsToStorage(bookings: VehicleBookingStorage): void {
  try {
    localStorage.setItem('autoop-vehicle-bookings', JSON.stringify(bookings))
  } catch (error) {
    console.error('Error al guardar bookings en localStorage:', error)
  }
}

/**
 * Guarda el ID de reserva completada en localStorage con fecha de expiración para un vehículo específico
 * SOLO se debe llamar cuando la reserva se haya completado exitosamente
 * @param vehicleId - ID del vehículo
 * @param bookingId - ID de la reserva
 * @param expirationDays - Días hasta la expiración (por defecto 2)
 */
export function saveBookingToStorage(vehicleId: string, bookingId: string, expirationDays: number = 2): void {
  const now = new Date()
  const expirationDate = new Date()
  expirationDate.setDate(expirationDate.getDate() + expirationDays)

  const bookingData: BookingStorageData = {
    vehicleId: vehicleId,
    bookingId: bookingId,
    expirationDate: expirationDate.toISOString(),
    createdAt: now.toISOString() // Este es el momento cuando se COMPLETÓ la reserva
  }

  const allBookings = getAllBookingsFromStorage()
  allBookings[vehicleId] = bookingData
  saveAllBookingsToStorage(allBookings)

  // Limpiar el estado de proceso ya que la reserva se completó
  clearBookingStateFromStorage(vehicleId)

  console.log(`Reserva completada para vehículo ${vehicleId}, expira en ${expirationDays} días`)
}

/**
 * Recupera el ID de reserva del localStorage para un vehículo específico validando la expiración
 * @param vehicleId - ID del vehículo
 * @returns El ID de reserva si es válido, null si no existe o ha expirado
 */
export function getValidBookingFromStorage(vehicleId: string): string | null {
  try {
    // Ejecutar limpieza automática antes de verificar
    autoCleanExpiredData()

    const allBookings = getAllBookingsFromStorage()
    const bookingData = allBookings[vehicleId]

    if (!bookingData) {
      return null
    }

    if (!bookingData.bookingId || !bookingData.expirationDate) {
      // Formato inválido, limpiar esta reserva específica
      clearBookingFromStorage(vehicleId)
      return null
    }

    const now = new Date().getTime()
    const expirationTime = new Date(bookingData.expirationDate).getTime()

    if (now < expirationTime) {
      // La reserva aún es válida
      return bookingData.bookingId
    } else {
      // La reserva ha expirado, limpiar esta reserva específica
      clearBookingFromStorage(vehicleId)
      console.log(`Reserva del vehículo ${vehicleId} expirada, limpiando localStorage`)
      return null
    }
  } catch (error) {
    console.error('Error al obtener booking del localStorage:', error)
    return null
  }
}

/**
 * Migra datos del formato antiguo al nuevo formato por vehículo
 * @param vehicleId - ID del vehículo actual
 */
function migrateOldBookingFormat(vehicleId: string): void {
  try {
    const oldData = localStorage.getItem('lastBookingId')

    if (oldData && typeof oldData === 'string') {
      try {
        // Intentar parsear como JSON del formato anterior
        const parsed = JSON.parse(oldData)

        if (parsed.bookingId && !parsed.vehicleId) {
          // Es formato anterior sin vehicleId, migrar
          console.log('Migrando formato anterior de booking al nuevo formato')

          const bookingData: BookingStorageData = {
            vehicleId: vehicleId,
            bookingId: parsed.bookingId,
            expirationDate: parsed.expirationDate || new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
            createdAt: parsed.createdAt || new Date().toISOString()
          }

          const allBookings = getAllBookingsFromStorage()
          allBookings[vehicleId] = bookingData
          saveAllBookingsToStorage(allBookings)

          // Limpiar formato anterior
          localStorage.removeItem('lastBookingId')
        }
      } catch (parseError: any) {
        // Si no se puede parsear, asumir que es un string simple (muy antiguo)
        console.log('Formato muy antiguo detectado, limpiando', parseError)
        localStorage.removeItem('lastBookingId')
      }
    }
  } catch (error) {
    console.error('Error durante migración:', error)
  }
}

/**
 * Limpia el localStorage de booking para un vehículo específico o todos
 * @param vehicleId - ID del vehículo (opcional, si no se proporciona limpia todo)
 */
export function clearBookingFromStorage(vehicleId?: string): void {
  if (vehicleId) {
    // Limpiar solo la reserva de un vehículo específico
    const allBookings = getAllBookingsFromStorage()
    delete allBookings[vehicleId]
    saveAllBookingsToStorage(allBookings)
  } else {
    // Limpiar todas las reservas
    localStorage.removeItem('autoop-vehicle-bookings')
    // También limpiar formato anterior por compatibilidad
    localStorage.removeItem('lastBookingId')
  }
}

/**
 * Verifica si existe una reserva válida en localStorage para un vehículo específico
 * @param vehicleId - ID del vehículo
 * @returns true si existe una reserva válida, false en caso contrario
 */
export function hasValidBookingInStorage(vehicleId: string): boolean {
  // Primero intentar migrar datos antiguos
  migrateOldBookingFormat(vehicleId)
  return getValidBookingFromStorage(vehicleId) !== null
}

/**
 * Obtiene información detallada de la reserva almacenada para un vehículo específico
 * @param vehicleId - ID del vehículo
 * @returns Los datos completos de la reserva o null si no existe/ha expirado
 */
export function getBookingStorageInfo(vehicleId: string): BookingStorageData | null {
  try {
    // Primero intentar migrar datos antiguos
    migrateOldBookingFormat(vehicleId)

    const allBookings = getAllBookingsFromStorage()
    const bookingData = allBookings[vehicleId]

    if (!bookingData) {
      return null
    }

    if (!bookingData.bookingId || !bookingData.expirationDate) {
      clearBookingFromStorage(vehicleId)
      return null
    }

    const now = new Date().getTime()
    const expirationTime = new Date(bookingData.expirationDate).getTime()

    if (now < expirationTime) {
      return bookingData
    } else {
      clearBookingFromStorage(vehicleId)
      return null
    }
  } catch (error) {
    console.error('Error al obtener info de booking:', error)
    clearBookingFromStorage(vehicleId)
    return null
  }
}

/**
 * Obtiene todas las reservas válidas (no expiradas)
 * @returns Objeto con todas las reservas válidas por vehículo
 */
export function getAllValidBookings(): VehicleBookingStorage {
  const allBookings = getAllBookingsFromStorage()
  const validBookings: VehicleBookingStorage = {}
  const now = new Date().getTime()

  for (const [vehicleId, bookingData] of Object.entries(allBookings)) {
    if (bookingData.expirationDate) {
      const expirationTime = new Date(bookingData.expirationDate).getTime()

      if (now < expirationTime) {
        validBookings[vehicleId] = bookingData
      } else {
        // Limpiar reserva expirada
        clearBookingFromStorage(vehicleId)
      }
    }
  }

  return validBookings
}

/**
 * Limpia todas las reservas expiradas del localStorage
 * Esta función se ejecuta automáticamente al verificar reservas
 */
export function cleanExpiredBookings(): void {
  const allBookings = getAllBookingsFromStorage()
  const now = new Date().getTime()
  let hasExpired = false

  for (const [vehicleId, bookingData] of Object.entries(allBookings)) {
    if (bookingData.expirationDate) {
      const expirationTime = new Date(bookingData.expirationDate).getTime()

      if (now >= expirationTime) {
        delete allBookings[vehicleId]
        hasExpired = true
        console.log(`Reserva expirada del vehículo ${vehicleId} eliminada automáticamente`)
      }
    }
  }

  if (hasExpired) {
    saveAllBookingsToStorage(allBookings)
  }
}

/**
 * Función que se ejecuta automáticamente para limpiar datos expirados
 * Se debe llamar en puntos estratégicos de la aplicación
 */
export function autoCleanExpiredData(): void {
  // Limpiar reservas completadas expiradas
  cleanExpiredBookings()

  // Opcional: Limpiar estados de proceso muy antiguos (más de 7 días sin actualizar)
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
  const sevenDaysAgoTime = sevenDaysAgo.getTime()

  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)

      if (key && key.startsWith('autoop-booking-state-')) {
        const stored = localStorage.getItem(key)

        if (stored) {
          const state = JSON.parse(stored)
          const lastUpdated = state.lastUpdated ? new Date(state.lastUpdated).getTime() : 0

          if (lastUpdated > 0 && lastUpdated < sevenDaysAgoTime) {
            const vehicleId = key.replace('autoop-booking-state-', '')
            localStorage.removeItem(key)
            console.log(`Estado de proceso antiguo eliminado para vehículo ${vehicleId}`)
          }
        }
      }
    }
  } catch (error) {
    console.error('Error al limpiar estados antiguos:', error)
  }
}

/**
 * Guarda el estado de booking de Zustand para un vehículo específico (proceso en progreso)
 * @param vehicleId - ID del vehículo
 * @param bookingState - Estado completo del booking
 */
export function saveBookingStateToStorage(vehicleId: string, bookingState: any): void {
  try {
    const key = `autoop-booking-state-${vehicleId}`
    const stateToSave = {
      ...bookingState,
      vehicleId: vehicleId,
      lastUpdated: new Date().toISOString()
    }
    localStorage.setItem(key, JSON.stringify(stateToSave))
  } catch (error) {
    console.error('Error al guardar estado de booking:', error)
  }
}

/**
 * Recupera el estado de booking de Zustand para un vehículo específico
 * @param vehicleId - ID del vehículo
 * @returns El estado de booking o null si no existe
 */
export function getBookingStateFromStorage(vehicleId: string): any | null {
  try {
    const key = `autoop-booking-state-${vehicleId}`
    const stored = localStorage.getItem(key)

    if (!stored) return null

    const state = JSON.parse(stored)

    // Verificar que el estado corresponde al vehículo correcto
    if (state.vehicleId !== vehicleId) {
      return null
    }

    return state
  } catch (error) {
    console.error('Error al recuperar estado de booking:', error)
    return null
  }
}

/**
 * Limpia el estado de booking de Zustand para un vehículo específico
 * @param vehicleId - ID del vehículo
 */
export function clearBookingStateFromStorage(vehicleId: string): void {
  try {
    const key = `autoop-booking-state-${vehicleId}`
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error al limpiar estado de booking:', error)
  }
}

/**
 * Obtiene todos los estados de booking guardados
 * @returns Array con información de todos los vehículos con estado guardado
 */
export function getAllBookingStates(): Array<{ vehicleId: string, savedAt: string }> {
  const states: Array<{ vehicleId: string, savedAt: string }> = []

  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)

      if (key && key.startsWith('autoop-booking-state-')) {
        const vehicleId = key.replace('autoop-booking-state-', '')
        const stored = localStorage.getItem(key)

        if (stored) {
          const state = JSON.parse(stored)
          states.push({
            vehicleId: vehicleId,
            savedAt: state.savedAt || 'Unknown'
          })
        }
      }
    }
  } catch (error) {
    console.error('Error al obtener estados de booking:', error)
  }

  return states
}

/**
 * Limpia las keys del sistema anterior que ya no se usan
 * Se debe ejecutar una vez para migrar completamente al nuevo sistema
 */
export function cleanOldBookingKeys(): void {
  try {
    // Limpiar keys del sistema anterior
    localStorage.removeItem('autoop-booking-storage')

    // Limpiar keys con formato autoop-booking-storage-{id}
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)

      if (key && key.startsWith('autoop-booking-storage-') && key !== 'autoop-booking-storage') {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      console.log(`Key antigua eliminada: ${key}`)
    })

    if (keysToRemove.length > 0) {
      console.log(`Se eliminaron ${keysToRemove.length} keys del sistema anterior`)
    }
  } catch (error) {
    console.error('Error al limpiar keys antiguas:', error)
  }
}

/**
 * Función de inicialización que se debe ejecutar al cargar la aplicación
 * Limpia datos antiguos y ejecuta mantenimiento
 */
export function initializeBookingSystem(): void {
  // Limpiar keys del sistema anterior
  cleanOldBookingKeys()

  // Ejecutar limpieza automática
  autoCleanExpiredData()

  console.log('Sistema de booking inicializado y limpiado')
}
