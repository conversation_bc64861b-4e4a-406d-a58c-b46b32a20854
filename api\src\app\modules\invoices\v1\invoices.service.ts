import { prisma } from "@/lib/prisma";
import { InvoiceProviderService } from "./providers/invoice-provider.service";
import { StripeInvoiceProvider } from "./providers/stripe-invoice.provider";
import { HttpException } from '@/exceptions/HttpExceptions';

export interface CreateInvoiceData {
  transactionId: string;
  customerName: string;
  customerEmail: string;
  customerAddress?: any;
  customerTaxId?: string;
  providerId?: string; // Si no se especifica, usa el proveedor por defecto
}

export interface InvoiceItemData {
  description: string;
  quantity: number;
  unitPrice: number;
  productCode?: string;
  unitCode?: string;
  taxRate?: number;
}

export class InvoicesService {
  private static providerServices: Map<string, InvoiceProviderService> = new Map();
  private static initialized = false;

  /**
   * Inicializar proveedores de facturación
   */
  static async initializeProviders() {
    if (this.initialized) return;

    // Registrar proveedor de Stripe
    this.providerServices.set('stripe', new StripeInvoiceProvider());

    // Aquí se pueden agregar más proveedores en el futuro
    // this.providerServices.set('facturapi', new FacturapiInvoiceProvider());
    // this.providerServices.set('facturama', new FacturamaInvoiceProvider());

    this.initialized = true;
  }

  /**
   * Crear factura automáticamente para una transacción
   */
  static async createInvoiceForTransaction(transactionId: string) {
    try {
      // Verificar si ya existe una factura para esta transacción
      const existingInvoice = await prisma.invoice.findUnique({
        where: { transactionId }
      });

      if (existingInvoice) {
        throw HttpException.BadRequest('Ya existe una factura para esta transacción');
      }

      // Obtener la transacción con datos relacionados
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId },
        include: {
          reservation: {
            include: {
              user: true,
              vehicle: {
                include: {
                  host: true
                }
              }
            }
          }
        }
      });

      if (!transaction) {
        throw HttpException.NotFound('Transacción no encontrada');
      }

      if (transaction.status !== 'succeeded') {
        throw HttpException.BadRequest('La transacción debe estar en estado succeeded para generar factura');
      }

      // Obtener proveedor por defecto
      const provider = await prisma.invoiceProvider.findFirst({
        where: { isDefault: true, isActive: true }
      });

      if (!provider) {
        throw HttpException.InternalServerError('No hay proveedor de facturación configurado');
      }

      // Preparar datos de la factura
      const invoiceData: CreateInvoiceData = {
        transactionId,
        customerName: transaction.reservation.user.name,
        customerEmail: transaction.reservation.user.email,
        customerTaxId: undefined, // Se puede obtener de perfil del usuario si está disponible
        providerId: provider.id
      };

      // Crear items de la factura
      const items: InvoiceItemData[] = [
        {
          description: `Renta de vehículo ${transaction.reservation.vehicle.make} ${transaction.reservation.vehicle.model} ${transaction.reservation.vehicle.year}`,
          quantity: 1,
          unitPrice: transaction.amount,
          productCode: undefined,
          unitCode: undefined,
          taxRate: 0.16 // IVA 16% para México
        }
      ];

      return await this.createInvoice(invoiceData, items);

    } catch (error) {
      console.error('Error creating invoice for transaction:', error);
      throw error;
    }
  }

  /**
   * Crear factura con datos específicos
   */
  static async createInvoice(data: CreateInvoiceData, items: InvoiceItemData[]) {
    try {
      // Inicializar proveedores si no están inicializados
      await this.initializeProviders();

      // Obtener proveedor
      const providerId = data.providerId || await this.getDefaultProviderId();
      const provider = await prisma.invoiceProvider.findUnique({
        where: { id: providerId }
      });

      if (!provider) {
        throw HttpException.NotFound('Proveedor de facturación no encontrado');
      }

      // Obtener servicio del proveedor
      const providerService = this.providerServices.get(provider.name);
      if (!providerService) {
        throw HttpException.InternalServerError(`Servicio del proveedor ${provider.name} no disponible`);
      }

      // Calcular totales
      const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
      const taxAmount = items.reduce((sum, item) => {
        const itemTax = (item.quantity * item.unitPrice) * (item.taxRate || 0);
        return sum + itemTax;
      }, 0);
      const totalAmount = subtotal + taxAmount;

      // Generar número de factura único
      const invoiceNumber = await this.generateInvoiceNumber();

      // Crear factura en el proveedor externo
      const externalInvoice = await providerService.createInvoice({
        invoiceNumber,
        customerName: data.customerName,
        customerEmail: data.customerEmail,
        customerAddress: data.customerAddress,
        customerTaxId: data.customerTaxId,
        items: items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.quantity * item.unitPrice,
          taxRate: item.taxRate || 0,
          taxAmount: (item.quantity * item.unitPrice) * (item.taxRate || 0),
          productCode: item.productCode,
          unitCode: item.unitCode
        })),
        subtotal,
        taxAmount,
        totalAmount
      });

      // Crear factura en la base de datos
      const invoice = await prisma.invoice.create({
        data: {
          transactionId: data.transactionId,
          providerId: provider.id,
          invoiceNumber,
          externalId: externalInvoice.id,
          customerName: data.customerName,
          customerEmail: data.customerEmail,
          customerAddress: data.customerAddress || {},
          customerTaxId: data.customerTaxId,
          issuerName: "Autoop", // Esto debería venir de configuración
          issuerAddress: {}, // Esto debería venir de configuración
          issuerTaxId: "AUTOOP123456", // Esto debería venir de configuración
          subtotal,
          taxAmount,
          totalAmount,
          currency: "mxn",
          status: "draft",
          issuedAt: new Date(),
          pdfUrl: externalInvoice.pdfUrl,
          xmlUrl: externalInvoice.xmlUrl,
          publicUrl: externalInvoice.publicUrl,
          metadata: externalInvoice.metadata,
          items: {
            create: items.map(item => ({
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.quantity * item.unitPrice,
              productCode: item.productCode,
              unitCode: item.unitCode,
              taxRate: item.taxRate || 0,
              taxAmount: (item.quantity * item.unitPrice) * (item.taxRate || 0)
            }))
          }
        },
        include: {
          items: true,
          provider: true,
          transaction: {
            include: {
              reservation: {
                include: {
                  user: true,
                  vehicle: true
                }
              }
            }
          }
        }
      });

      return invoice;

    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  /**
   * Obtener factura por ID
   */
  static async getInvoiceById(invoiceId: string) {
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        items: true,
        provider: true,
        transaction: {
          include: {
            reservation: {
              include: {
                user: true,
                vehicle: true
              }
            }
          }
        }
      }
    });

    if (!invoice) {
      throw HttpException.NotFound('Factura no encontrada');
    }

    return invoice;
  }

  /**
   * Obtener facturas de un usuario
   */
  static async getInvoicesByUser(userId: string, filters?: {
    limit?: number;
    offset?: number;
    status?: string;
  }) {
    const { limit = 10, offset = 0, status } = filters || {};

    const where: any = {
      transaction: {
        reservation: {
          userId
        }
      }
    };

    if (status) {
      where.status = status;
    }

    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        include: {
          items: true,
          provider: true,
          transaction: {
            include: {
              reservation: {
                include: {
                  vehicle: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      }),
      prisma.invoice.count({ where })
    ]);

    return {
      invoices,
      pagination: {
        total,
        limit,
        offset,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Generar número de factura único
   */
  private static async generateInvoiceNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const count = await prisma.invoice.count({
      where: {
        invoiceNumber: {
          startsWith: `INV-${year}-`
        }
      }
    });

    return `INV-${year}-${String(count + 1).padStart(6, '0')}`;
  }

  /**
   * Obtener ID del proveedor por defecto
   */
  private static async getDefaultProviderId(): Promise<string> {
    const provider = await prisma.invoiceProvider.findFirst({
      where: { isDefault: true, isActive: true }
    });

    if (!provider) {
      throw HttpException.InternalServerError('No hay proveedor de facturación por defecto configurado');
    }

    return provider.id;
  }
}
