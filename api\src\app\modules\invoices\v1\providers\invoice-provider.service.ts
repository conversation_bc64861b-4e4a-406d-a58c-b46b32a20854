export interface InvoiceItemInput {
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate: number;
  taxAmount: number;
  productCode?: string;
  unitCode?: string;
}

export interface CreateInvoiceInput {
  invoiceNumber: string;
  customerName: string;
  customerEmail: string;
  customerAddress?: any;
  customerTaxId?: string;
  items: InvoiceItemInput[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  currency?: string;
}

export interface ExternalInvoiceResponse {
  id: string;
  invoiceNumber: string;
  status: string;
  pdfUrl?: string;
  xmlUrl?: string;
  publicUrl?: string;
  metadata?: any;
}

/**
 * Interfaz base para todos los proveedores de facturación
 */
export abstract class InvoiceProviderService {
  /**
   * Crear una factura en el proveedor externo
   */
  abstract createInvoice(data: CreateInvoiceInput): Promise<ExternalInvoiceResponse>;

  /**
   * Obtener una factura del proveedor externo
   */
  abstract getInvoice(externalId: string): Promise<ExternalInvoiceResponse>;

  /**
   * Cancelar una factura en el proveedor externo
   */
  abstract cancelInvoice(externalId: string): Promise<void>;

  /**
   * Enviar factura por email
   */
  abstract sendInvoice(externalId: string, email: string): Promise<void>;

  /**
   * Obtener URL de descarga del PDF
   */
  abstract getPdfUrl(externalId: string): Promise<string>;

  /**
   * Validar configuración del proveedor
   */
  abstract validateConfig(config: any): boolean;

  /**
   * Obtener nombre del proveedor
   */
  abstract getProviderName(): string;
}
