FROM oven/bun:latest AS build

LABEL fly_launch_runtime="Bun"

WORKDIR /app


ARG DATABASE_URL
ENV DATABASE_URL=$DATABASE_URL
ENV NODE_ENV="production"

COPY package.json bun.lockb ./
COPY prisma ./prisma

RUN bun install --production
RUN bun install -D prisma-json-types-generator


RUN bun prisma generate 
# --schema ./prisma/schema
RUN bun prisma db push --accept-data-loss

COPY . .

RUN bun run build
# RUN bun run build:workers

# Instalar dos2unix y convertir el script a formato LF
RUN apt-get update && apt-get install -y dos2unix && \
    dos2unix docker-entrypoint.sh && \
    chmod +x docker-entrypoint.sh

FROM debian:bullseye-slim

WORKDIR /app

# # COPY PRISMA MODULES
# COPY --from=build /app/node_modules/.prisma /app/node_modules/.prisma
# COPY --from=build /app/node_modules/@prisma /app/node_modules/@prisma

# # COPY SHARP MODULES
# COPY --from=build /app/node_modules/sharp /app/node_modules/sharp

# COPY NODE MODULES
COPY --from=build /app/node_modules node_modules

COPY --from=build /app/server server
# COPY --from=build /app/worker worker
COPY --from=build /app/docker-entrypoint.sh docker-entrypoint.sh
COPY --from=build /app/prisma prisma

# Instalar dependencias y dos2unix
RUN apt-get update && apt-get install -y --no-install-recommends \
    # Instala dependencias nativas necesarias para compilar sharp
    libvips42 \
    libvips-tools \
    libvips-dev \
    build-essential \
    # Instala dependencias adicionales
    git \
    curl \
    ca-certificates \
    procps \
    coreutils \
    nano \
    dos2unix && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Asegurar que el script tiene formato LF y permisos de ejecución
RUN dos2unix docker-entrypoint.sh && \
    chmod +x docker-entrypoint.sh

ENV NODE_ENV=production

EXPOSE 3000
EXPOSE 8080

# ENTRYPOINT como wrapper
ENTRYPOINT ["./docker-entrypoint.sh"]
# CMD por defecto (fallback local / Fly override)
CMD ["./server"]
