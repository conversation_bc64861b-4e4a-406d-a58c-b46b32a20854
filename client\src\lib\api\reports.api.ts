// import { apiService } from './api-service';
import { apiService } from '@/services/api';

// Tipos para reportes
export interface HostMonthlyReport {
  id: string;
  hostId: string;
  year: number;
  month: number;
  totalReservations: number;
  totalGrossEarnings: number;
  totalPlatformFees: number;
  totalNetEarnings: number;
  totalTransferred: number;
  generatedAt: string;
}

export interface ReportSummary {
  totalEarnings: number;
  totalReservations: number;
  totalPlatformFees: number;
  averageMonthlyEarnings: number;
}

export interface HostReportsResponse {
  success: boolean;
  data: {
    reports: HostMonthlyReport[];
    summary: ReportSummary;
  };
  error?: string;
}

export interface PlatformStats {
  totalTransactions: number;
  totalRevenue: number;
  totalPlatformFees: number;
  totalHostEarnings: number;
  activeHosts: number;
  activeClients: number;
  averageTransactionValue: number;
}

export interface PlatformStatsResponse {
  success: boolean;
  data: PlatformStats;
  error?: string;
}

export interface ReportFilters {
  year?: number;
  startYear?: number;
  endYear?: number;
  limit?: number;
  offset?: number;
}

export interface PlatformStatsFilters {
  startDate?: string;
  endDate?: string;
}

// API para hosts
export const hostReportsApi = {
  /**
   * Obtener reportes mensuales del host
   */
  getMonthlyReports: async (filters?: ReportFilters): Promise<HostReportsResponse> => {
    const params = new URLSearchParams();
    if (filters?.year) params.append('year', filters.year.toString());
    if (filters?.startYear) params.append('startYear', filters.startYear.toString());
    if (filters?.endYear) params.append('endYear', filters.endYear.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());

    const queryString = params.toString();
    const url = `/host/reports/monthly${queryString ? `?${queryString}` : ''}`;
    
    // return apiService.get<HostReportsResponse>(url);
    const response = await apiService.get<HostReportsResponse>(url);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }
  },

  /**
   * Generar reporte para un mes específico
   */
  generateReport: async (year: number, month: number): Promise<{ success: boolean; data?: any; error?: string }> => {
    // return apiService.post<{ success: boolean; data?: any; error?: string }>(`/host/reports/generate/${year}/${month}`, {});
    const response = await apiService.post<{ success: boolean; data?: any; error?: string }>(`/host/reports/generate/${year}/${month}`, {});
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }
  },
  /**
   * Obtener reporte del mes actual
   */
  getCurrentMonthReport: async (): Promise<{ success: boolean; data?: any; error?: string }> => {
    // return apiService.get<{ success: boolean; data?: any; error?: string }>('/host/reports/current-month');
    const response = await apiService.get<{ success: boolean; data?: any; error?: string }>('/host/reports/current-month');
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }  
  },

  /**
   * Obtener reporte del mes pasado
   */
  getLastMonthReport: async (): Promise<{ success: boolean; data?: any; error?: string }> => {
    // return apiService.get<{ success: boolean; data?: any; error?: string }>('/host/reports/last-month');
    const response = await apiService.get<{ success: boolean; data?: any; error?: string }>('/host/reports/last-month');
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }  
  }
};

// API para administradores
export const adminReportsApi = {
  /**
   * Generar reportes mensuales para todos los hosts
   */
  generateMonthlyReports: async (year?: number, month?: number): Promise<{ success: boolean; data?: any; error?: string }> => {
    // return apiService.post<{ success: boolean; data?: any; error?: string }>('/admin/reports/generate-monthly', { year, month });
    const response = await apiService.post<{ success: boolean; data?: any; error?: string }>('/admin/reports/generate-monthly', { year, month });
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }   
  },

  /**
   * Obtener estadísticas de la plataforma
   */
  getPlatformStats: async (filters?: PlatformStatsFilters): Promise<PlatformStatsResponse> => {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);

    const queryString = params.toString();
    const url = `/admin/reports/platform-stats${queryString ? `?${queryString}` : ''}`;
    
    // return apiService.get<PlatformStatsResponse>(url);
    const response = await apiService.get<PlatformStatsResponse>(url);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }  
  },

  /**
   * Obtener reportes mensuales de un host específico (admin)
   */
  getHostMonthlyReports: async (hostId: string, filters?: ReportFilters): Promise<HostReportsResponse> => {
    const params = new URLSearchParams();
    if (filters?.year) params.append('year', filters.year.toString());
    if (filters?.startYear) params.append('startYear', filters.startYear.toString());
    if (filters?.endYear) params.append('endYear', filters.endYear.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.offset) params.append('offset', filters.offset.toString());

    const queryString = params.toString();
    const url = `/admin/reports/host/${hostId}/monthly${queryString ? `?${queryString}` : ''}`;
    
    // return apiService.get<HostReportsResponse>(url);
    const response = await apiService.get<HostReportsResponse>(url);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }
  }
};

// API pública
export const publicReportsApi = {
  /**
   * Obtener estadísticas públicas de la plataforma
   */
  getPlatformOverview: async (): Promise<{ success: boolean; data?: any; error?: string }> => {
    // return apiService.get<{ success: boolean; data?: any; error?: string }>('/public/reports/platform-overview');
    const response = await apiService.get<{ success: boolean; data?: any; error?: string }>('/public/reports/platform-overview');
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error);
    }  
  }
};
