import { apiService } from '@/services/api';

// Tipos para Stripe Connect
export interface ConnectedAccountStatus {
  hasAccount: boolean;
  isComplete: boolean;
  canReceivePayments: boolean;
  requirements?: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
    disabled_reason?: string;
    current_deadline?: string | null;
    alternatives: any[];
    errors: any[];
  };
  accountId?: string;
}

export interface BankAccount {
  id: string;
  object: string;
  account_holder_name?: string;
  account_holder_type?: string;
  account_number?: string;
  routing_number?: string;
  bank_name?: string;
  country: string;
  currency: string;
  last4: string;
  metadata?: {
    bank_name?: string;
    added_via?: string;
    original_account_number?: string;
    clabe?: string;
  };
  // Campos adicionales de Stripe
  fingerprint?: string;
  status?: string;
  default_for_currency?: boolean;
}

export interface CreateAccountData {
  email: string;
  country: string;
  type: 'express' | 'standard' | 'custom';
}

export interface BankAccountData {
  routingNumber: string; // CLABE en México
  accountHolderName: string;
  accountType: 'checking' | 'savings';
  bankName?: string; // Nombre del banco
}

// API para hosts
export const stripeConnectApi = {
  /**
   * Crear cuenta conectada de Stripe
   */
  createAccount: async (data: CreateAccountData) => {
    const response = await apiService.post<{ success: boolean; data?: any; error?: string }>('/host/stripe-connect/create-account', data);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Crear cuenta automáticamente para nuevo host
   */
  autoCreateAccount: async () => {
    const response = await apiService.post<{ success: boolean; data?: any; error?: string }>('/host/stripe-connect/auto-create-account', {});
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Obtener estado de la cuenta conectada
   */
  getAccountStatus: async () => {
    // return apiService.get<{ success: boolean; data: ConnectedAccountStatus; error?: string }>('/host/stripe-connect/status');
    const response = await apiService.get<{ success: boolean; data: ConnectedAccountStatus; error?: string }>('/host/stripe-connect/status');
    console.log('Account Status Response:', response);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Crear enlace de onboarding
   */
  createOnboardingLink: async () => {
    const response = await apiService.post<{ success: boolean; data?: { url: string }; error?: string }>('/host/stripe-connect/onboarding-link', {});
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Agregar cuenta bancaria
   */
  addBankAccount: async (data: BankAccountData) => {
    // return apiService.post<{ success: boolean; data?: any; error?: string }>('/host/stripe-connect/add-bank-account', data);
    const response = await apiService.post<{ success: boolean; data?: any; error?: string }>('/host/stripe-connect/add-bank-account', data);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Listar cuentas bancarias
   */
  getBankAccounts: async () => {
    const response = await apiService.get<{ success: boolean; data: BankAccount[]; error?: string }>('/host/stripe-connect/bank-accounts');
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Eliminar cuenta bancaria
   */
  removeBankAccount: async (bankAccountId: string) => {
    const response = await apiService.delete<{ success: boolean; data?: any; error?: string }>(`/host/stripe-connect/bank-account/${bankAccountId}`);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Actualizar cuenta bancaria
   */
  updateBankAccount: async (bankAccountId: string, data: BankAccountData) => {
    const response = await apiService.put<{ success: boolean; data?: any; error?: string }>(`/host/stripe-connect/bank-account/${bankAccountId}`, data);
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Crear enlace al dashboard de Stripe
   */
  createDashboardLink: async () => {
    // return apiService.post<{ success: boolean; data?: { url: string }; error?: string }>('/host/stripe-connect/dashboard-link', {});
    const response = await apiService.post<{ success: boolean; data?: { url: string }; error?: string }>('/host/stripe-connect/dashboard-link', {});
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  }
};
