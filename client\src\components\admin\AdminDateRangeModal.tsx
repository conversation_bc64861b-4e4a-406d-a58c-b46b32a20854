"use client"

import { useState } from "react"
import { DateRange } from "react-date-range"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"

interface AdminDateRangeModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onDateRangeSelect: (ranges: any) => void
  initialRange?: {
    startDate: Date
    endDate: Date
    key: string
  }
}

export function AdminDateRangeModal({
  open,
  onOpenChange,
  onDateRangeSelect,
  initialRange
}: AdminDateRangeModalProps) {
  const [selection, setSelection] = useState({
    startDate: initialRange?.startDate || new Date(),
    endDate: initialRange?.endDate || new Date(),
    key: 'selection'
  })

  const handleSelect = (ranges: any) => {
    setSelection(ranges.selection)
  }

  const handleApply = () => {
    onDateRangeSelect({ selection })
    onOpenChange(false)
  }

  const handleCancel = () => {
    // Restaurar valores iniciales
    if (initialRange) {
      setSelection({
        startDate: initialRange.startDate,
        endDate: initialRange.endDate,
        key: 'selection'
      })
    }
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Seleccionar Rango de Fechas</DialogTitle>
        </DialogHeader>

        <div className="flex justify-center py-4">
          <DateRange
            ranges={[selection]}
            onChange={handleSelect}
            locale={es}
            months={1}
            direction="horizontal"
            showDateDisplay={false}
            rangeColors={['#3b82f6']}
            color="#3b82f6"
          />
        </div>

        <div className="text-center text-sm text-muted-foreground">
          {selection.startDate && selection.endDate && (
            <p>
              Seleccionado: {format(selection.startDate, "dd/MM/yyyy", { locale: es })} - {format(selection.endDate, "dd/MM/yyyy", { locale: es })}
            </p>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancelar
          </Button>
          <Button onClick={handleApply}>
            Aplicar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
