"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from 'next/navigation';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { toast } from "react-hot-toast";
import { authClient } from '@/auth-client';

const signInSchema = z.object({
  email: z.string().email({ message: "Correo electrónico inválido" }),
  password: z.string().min(1, { message: "La contraseña es requerida" }),
  rememberMe: z.boolean().optional(),
});

interface LoginFormProps {
  onSuccess?: () => void
  redirectAfterLogin?: boolean
  onRegisterClick?: () => void
  callbackUrl?: string
}

type SignInFormValues = z.infer<typeof signInSchema>;

export function LoginForm({ onSuccess, onRegisterClick, redirectAfterLogin = true }: LoginFormProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const form = useForm<SignInFormValues>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = async (data: SignInFormValues) => {
    try {
      await authClient.signIn.email(
        {
          email: data.email,
          password: data.password,
          callbackURL: window.location.origin + "/dashboard"

        },
        {
          onRequest: () => {
            setLoading(true);
          },
          onResponse: () => {
            setLoading(false);
          },
          onError: (ctx) => {
            console.log('Error: ', ctx);
            toast.error(ctx.error.message || "Error al iniciar sesión");
          },
          onSuccess: async () => {

            // router.push("/dashboard");
            if (onSuccess) {
              onSuccess()
            }

            if (redirectAfterLogin) {
              toast.success("Inicio de sesión exitoso. Redirigiendo al dashboard...")
              // Redirigir a la página de reserva si hay una redirección pendiente
              router.push('/dashboard')
            }
          },
        },
      );
    } catch (error) {
      setLoading(false);
      toast.error("Error al iniciar sesión");
      console.log('error: ', error)
    }
  };

  return (
    <>
      <Card className="z-50 rounded-md max-w-md w-full">
        <CardHeader>
          <CardTitle className="text-lg md:text-xl">Iniciar Sesión</CardTitle>
          <CardDescription className="text-xs md:text-sm">
            Ingresa tus credenciales para acceder a Autoop
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Correo electrónico</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Contraseña</FormLabel>
                      <Link
                        href="/forgot-password"
                        className="text-xs text-primary hover:underline"
                      >
                        ¿Olvidaste tu contraseña?
                      </Link>
                    </div>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Contraseña"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-normal">Recordarme</FormLabel>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  "Iniciar Sesión"
                )}
              </Button>
            </form>
          </Form>

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t"></span>
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">O continúa con</span>
            </div>
          </div>

          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full gap-2"
              disabled={loading}
              onClick={async () => {
                try {
                  await authClient.signIn.social(
                    {
                      provider: "google",
                      callbackURL: window.location.origin + "/dashboard"
                    },
                    {
                      onRequest: () => {
                        setLoading(true);
                      },
                      onResponse: () => {
                        setLoading(false);
                      },
                      onError: (ctx) => {
                        toast.error(ctx.error.message || "Error al iniciar sesión con Google");
                      },
                    },
                  );
                } catch (error) {
                  setLoading(false);
                  toast.error("Error al iniciar sesión con Google");
                  console.log('error: ', error)
                }
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="0.98em" height="1em" viewBox="0 0 256 262">
                <path fill="#4285F4" d="M255.878 133.451c0-10.734-.871-18.567-2.756-26.69H130.55v48.448h71.947c-1.45 12.04-9.283 30.172-26.69 42.356l-.244 1.622l38.755 30.023l2.685.268c24.659-22.774 38.875-56.282 38.875-96.027"></path>
                <path fill="#34A853" d="M130.55 261.1c35.248 0 64.839-11.605 86.453-31.622l-41.196-31.913c-11.024 7.688-25.82 13.055-45.257 13.055c-34.523 0-63.824-22.773-74.269-54.25l-1.531.13l-40.298 31.187l-.527 1.465C35.393 231.798 79.49 261.1 130.55 261.1"></path>
                <path fill="#FBBC05" d="M56.281 156.37c-2.756-8.123-4.351-16.827-4.351-25.82c0-8.994 1.595-17.697 4.206-25.82l-.073-1.73L15.26 71.312l-1.335.635C5.077 89.644 0 109.517 0 130.55s5.077 40.905 13.925 58.602z"></path>
                <path fill="#EB4335" d="M130.55 50.479c24.514 0 41.05 10.589 50.479 19.438l36.844-35.974C195.245 12.91 165.798 0 130.55 0C79.49 0 35.393 29.301 13.925 71.947l42.211 32.783c10.59-31.477 39.891-54.251 74.414-54.251"></path>
              </svg>
              Iniciar con Google
            </Button>
            <Button
              variant="outline"
              className="w-full gap-2"
              disabled={loading}
              onClick={async () => {
                try {
                  await authClient.signIn.social(
                    {
                      provider: "facebook",
                      callbackURL: "/dashboard"
                    },
                    {
                      onRequest: () => {
                        setLoading(true);
                      },
                      onResponse: () => {
                        setLoading(false);
                      },
                      onError: (ctx) => {
                        toast.error(ctx.error.message || "Error al iniciar sesión con Facebook");
                      },
                    },
                  );
                } catch (error) {
                  setLoading(false);
                  toast.error("Error al iniciar sesión con Facebook");
                  console.log('error: ', error)
                }
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24"
              >
                <path
                  d="M20 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h8.615v-6.96h-2.338v-2.725h2.338v-2c0-2.325 1.42-3.592 3.5-3.592c.699-.002 1.399.034 2.095.107v2.42h-1.435c-1.128 0-1.348.538-1.348 1.325v1.735h2.697l-.35 2.725h-2.348V21H20a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1z"
                  fill="currentColor"
                ></path>
              </svg>
              Iniciar con Facebook
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <div className="flex justify-center w-full">
            <p className="text-center text-xs text-neutral-500">
              ¿No tienes una cuenta?{" "}

              {
                onRegisterClick ? (
                  <Button
                    variant="link"
                    className="p-0"
                    onClick={onRegisterClick}
                  >
                    Crear Cuenta
                  </Button>
                ) : (
                    <Link
                      href="/sign-up"
                      className="underline"
                    >
                      Crear Cuenta
                    </Link>
                )
              }
            </p>
          </div>
        </CardFooter>
      </Card>
    </>
  )
}