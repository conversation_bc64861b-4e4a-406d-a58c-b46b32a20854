// This file must be a module, so we include an empty export.
export { };

declare global {
  namespace PrismaJson {
    // Define a type for bank account information stored in JSON
    type BankAccountInfo = {
      id: string;
      accountHolderName: string;
      routingNumber: string; // CLABE
      bankName: string;
      accountType: 'checking' | 'savings';
      stripeExternalAccountId?: string;
      isDefault?: boolean;
      createdAt: string; // ISO date string
      updatedAt: string; // ISO date string
    }[];
  }
}
