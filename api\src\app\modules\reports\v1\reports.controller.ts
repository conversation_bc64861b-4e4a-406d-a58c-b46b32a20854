import { Elysia, t } from "elysia";
import { ReportsService } from "./reports.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';

// Esquemas de validación
const monthlyReportSchema = t.Object({
  year: t.Number(),
  month: t.Number()
});

const reportsFiltersSchema = t.Object({
  year: t.Optional(t.Number()),
  startYear: t.Optional(t.Number()),
  endYear: t.Optional(t.Number()),
  limit: t.Optional(t.Number()),
  offset: t.Optional(t.Number())
});

const platformStatsFiltersSchema = t.Object({
  startDate: t.Optional(t.String()),
  endDate: t.Optional(t.String())
});

// Controlador para administradores
export const adminReportsController = new Elysia()
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .post('/admin/reports/generate-monthly',
    async ({ body }) => {
      try {
        const result = await ReportsService.generateAllHostsMonthlyReports(body.year, body.month);
        return { success: true, data: result };
      } catch (error) {
        console.error('Error generating monthly reports:', error);
        return { success: false, error: error.message || 'Error al generar reportes mensuales' };
      }
    },
    {
      body: t.Object({
        year: t.Optional(t.Number()),
        month: t.Optional(t.Number())
      })
    }
  )
  .get('/admin/reports/platform-stats',
    async ({ query }) => {
      try {
        const filters: any = {};

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);

        const stats = await ReportsService.getPlatformStats(filters);
        return { success: true, data: stats };
      } catch (error) {
        console.error('Error getting platform stats:', error);
        return { success: false, error: 'Error al obtener estadísticas de la plataforma' };
      }
    },
    {
      query: platformStatsFiltersSchema
    }
  )
  .get('/admin/reports/host/:hostId/monthly',
    async ({ params, query }) => {
      try {
        const filters: any = {};

        if (query.year) filters.year = parseInt(query.year.toString());
        if (query.startYear) filters.startYear = parseInt(query.startYear.toString());
        if (query.endYear) filters.endYear = parseInt(query.endYear.toString());
        if (query.limit) filters.limit = parseInt(query.limit.toString());
        if (query.offset) filters.offset = parseInt(query.offset.toString());

        const reports = await ReportsService.getHostMonthlyReports(params.hostId, filters);
        return { success: true, data: reports };
      } catch (error) {
        console.error('Error getting host monthly reports:', error);
        return { success: false, error: 'Error al obtener reportes mensuales del host' };
      }
    },
    {
      params: t.Object({
        hostId: t.String()
      }),
      query: reportsFiltersSchema
    }
  );

// Controlador para hosts
export const hostReportsController = new Elysia()
  .use(authMiddleware)
  .get('/host/reports/monthly',
    async ({ query, user }) => {
      try {
        const filters: any = {};

        if (query.year) filters.year = parseInt(query.year.toString());
        if (query.startYear) filters.startYear = parseInt(query.startYear.toString());
        if (query.endYear) filters.endYear = parseInt(query.endYear.toString());
        if (query.limit) filters.limit = parseInt(query.limit.toString());
        if (query.offset) filters.offset = parseInt(query.offset.toString());

        const reports = await ReportsService.getHostMonthlyReports(user.id, filters);
        return { success: true, data: reports };
      } catch (error) {
        console.error('Error getting host monthly reports:', error);
        return { success: false, error: 'Error al obtener reportes mensuales' };
      }
    },
    {
      query: reportsFiltersSchema
    }
  )
  .post('/host/reports/generate/:year/:month',
    async ({ params, user }) => {
      try {
        const year = parseInt(params.year);
        const month = parseInt(params.month);

        if (year < 2020 || year > 2030 || month < 1 || month > 12) {
          return { success: false, error: 'Año o mes inválido' };
        }

        const result = await ReportsService.generateHostMonthlyReport(user.id, year, month);
        return { success: true, data: result };
      } catch (error) {
        console.error('Error generating host monthly report:', error);
        return { success: false, error: error.message || 'Error al generar reporte mensual' };
      }
    },
    {
      params: t.Object({
        year: t.String(),
        month: t.String()
      })
    }
  )
  .get('/host/reports/current-month',
    async ({ user }) => {
      try {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth() + 1;

        const result = await ReportsService.generateHostMonthlyReport(user.id, year, month);
        return { success: true, data: result };
      } catch (error) {
        console.error('Error getting current month report:', error);
        return { success: false, error: 'Error al obtener reporte del mes actual' };
      }
    }
  )
  .get('/host/reports/last-month',
    async ({ user }) => {
      try {
        const now = new Date();
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const year = lastMonth.getFullYear();
        const month = lastMonth.getMonth() + 1;

        const result = await ReportsService.generateHostMonthlyReport(user.id, year, month);
        return { success: true, data: result };
      } catch (error) {
        console.error('Error getting last month report:', error);
        return { success: false, error: 'Error al obtener reporte del mes pasado' };
      }
    }
  );

// Controlador público para estadísticas básicas
export const publicReportsController = new Elysia()
  .get('/public/reports/platform-overview',
    async () => {
      try {
        // Estadísticas públicas básicas (sin datos sensibles)
        const stats = await ReportsService.getPlatformStats();

        // Solo devolver datos no sensibles
        return {
          success: true,
          data: {
            totalTransactions: stats.totalTransactions,
            activeHosts: stats.activeHosts,
            activeClients: stats.activeClients,
            // No incluir ingresos específicos
          }
        };
      } catch (error) {
        console.error('Error getting public platform stats:', error);
        return { success: false, error: 'Error al obtener estadísticas públicas' };
      }
    }
  );
