"use client"

import { useQuery } from "@tanstack/react-query"
import { usersApi } from "@/lib/api/users.api"
import { Users, Shield, UserX, Activity, TrendingUp, TrendingDown } from "lucide-react"

interface UserStats {
  totalUsers: number
  totalAdmins: number
  totalBanned: number
  totalActiveSessions: number
}

export function UserStatsCards() {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ['user-stats'],
    queryFn: () => usersApi.getUserStats(),
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchInterval: 30 * 1000, // Refrescar cada 30 segundos
  })

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-card text-card-foreground rounded-lg border p-4">
            <div className="text-sm font-medium text-red-500">Error al cargar</div>
            <div className="text-2xl font-bold">-</div>
          </div>
        ))}
      </div>
    )
  }

  const statsCards = [
    {
      title: "Total Usuarios",
      value: stats?.totalUsers || 0,
      description: "Todos los usuarios registrados",
      icon: Users,
      color: "bg-blue-500",
      lightColor: "bg-blue-50",
      textColor: "text-blue-600",
      trend: null
    },
    {
      title: "Administradores",
      value: stats?.totalAdmins || 0,
      description: "Usuarios con rol admin",
      icon: Shield,
      color: "bg-purple-500",
      lightColor: "bg-purple-50",
      textColor: "text-purple-600",
      trend: null
    },
    {
      title: "Usuarios Baneados",
      value: stats?.totalBanned || 0,
      description: "Usuarios actualmente baneados",
      icon: UserX,
      color: "bg-red-500",
      lightColor: "bg-red-50",
      textColor: "text-red-600",
      trend: stats?.totalBanned ? (stats.totalBanned > 0 ? 'down' : null) : null
    },
    {
      title: "Sesiones Activas",
      value: stats?.totalActiveSessions || 0,
      description: "Total de sesiones activas",
      icon: Activity,
      color: "bg-green-500",
      lightColor: "bg-green-50",
      textColor: "text-green-600",
      trend: stats?.totalActiveSessions ? (stats.totalActiveSessions > 0 ? 'up' : null) : null
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statsCards.map((card, index) => {
        const IconComponent = card.icon
        const isLoading = !stats

        return (
          <div
            key={index}
            className={`relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all duration-200 hover:shadow-md hover:scale-105`}
          >
            {/* Background decoration */}
            <div className={`absolute top-0 right-0 w-20 h-20 ${card.lightColor} rounded-full -mr-10 -mt-10 opacity-50`} />

            {/* Icon */}
            <div className={`inline-flex items-center justify-center w-12 h-12 ${card.color} rounded-lg mb-4 shadow-lg`}>
              <IconComponent className="w-6 h-6 text-white" />
            </div>

            {/* Content */}
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-600">
                {card.title}
              </div>

              <div className="flex items-center space-x-2">
                {isLoading ? (
                  <div className="animate-pulse">
                    <div className="h-8 w-16 bg-gray-200 rounded"></div>
                  </div>
                ) : (
                  <>
                    <div className={`text-3xl font-bold ${card.textColor}`}>
                      {card.value.toLocaleString()}
                    </div>
                    {card.trend && (
                      <div className={`flex items-center ${card.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                        {card.trend === 'up' ? (
                          <TrendingUp className="w-4 h-4" />
                        ) : (
                          <TrendingDown className="w-4 h-4" />
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>

              <div className="text-xs text-gray-500">
                {card.description}
              </div>
            </div>

            {/* Progress bar for visual appeal */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div
                  className={`h-1 rounded-full ${card.color} transition-all duration-1000 ease-out`}
                  style={{
                    width: isLoading ? '0%' : `${Math.min((card.value / Math.max(stats?.totalUsers || 1, 1)) * 100, 100)}%`
                  }}
                />
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
