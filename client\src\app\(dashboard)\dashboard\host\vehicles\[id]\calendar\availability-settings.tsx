import { useState, useEffect } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import toast from 'react-hot-toast'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
// import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface TimeSlot {
  start: string;
  end: string;
}

interface AvailabilitySettings {
  defaultCheckInTime: string;
  defaultCheckOutTime: string;
  minimumRentalDays: number;
  maximumRentalDays: number;
  // bufferTimeBetweenRentals: number;
  advanceBookingPeriod: number;
  instantBooking: boolean;
  allowSameDayBooking: boolean;
  cancellationPolicy: "flexible" | "moderate" | "strict";
  availableDays: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
  };
  customTimeSlots: boolean;
  timeSlots: {
    monday: TimeSlot[];
    tuesday: TimeSlot[];
    wednesday: TimeSlot[];
    thursday: TimeSlot[];
    friday: TimeSlot[];
    saturday: TimeSlot[];
    sunday: TimeSlot[];
  };
}

// Estado inicial vacío para la configuración
const emptyAvailabilitySettings: AvailabilitySettings = {
  defaultCheckInTime: "",
  defaultCheckOutTime: "",
  minimumRentalDays: 0,
  maximumRentalDays: 0,
  // bufferTimeBetweenRentals: 0,
  advanceBookingPeriod: 0,
  instantBooking: false,
  allowSameDayBooking: false,
  cancellationPolicy: "flexible",
  availableDays: {
    monday: false,
    tuesday: false,
    wednesday: false,
    thursday: false,
    friday: false,
    saturday: false,
    sunday: false
  },
  customTimeSlots: false,
  timeSlots: {
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: []
  }
};

const WEEK_DAYS = [
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
  "sunday"
] as const;

type WeekDay = typeof WEEK_DAYS[number];

interface VehicleAvailabilitySettingsProps {
  vehicleId: string;
}

export default function VehicleAvailabilitySettings({ vehicleId }: VehicleAvailabilitySettingsProps) {
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  console.log("isEditing", isEditing);
  const [availabilitySettings, setAvailabilitySettings] = useState<AvailabilitySettings>(emptyAvailabilitySettings);

  // Obtener configuración de disponibilidad
  const { data: availabilityData, isLoading } = useQuery({
    queryKey: ['vehicle-availability', vehicleId],
    queryFn: () => vehiclesApi.getAvailabilitySettings(vehicleId),
    enabled: !!vehicleId
  });

  console.log("availabilityData", availabilityData);

  // Actualizar el estado local cuando se cargan los datos
  useEffect(() => {
    if (availabilityData) {
      setAvailabilitySettings({
        defaultCheckInTime: availabilityData.defaultCheckInTime || "",
        defaultCheckOutTime: availabilityData.defaultCheckOutTime || "",
        minimumRentalDays: availabilityData.minimumRentalNights || 0,
        maximumRentalDays: availabilityData.maximumRentalNights || 0,
        // bufferTimeBetweenRentals: availabilityData.bufferTimeBetweenRentals || 0,
        advanceBookingPeriod: availabilityData.advanceBookingPeriod || 0,
        instantBooking: availabilityData.instantBooking || false,
        allowSameDayBooking: availabilityData.allowSameDayBooking || false,
        cancellationPolicy: availabilityData.cancellationPolicy || "flexible",
        availableDays: {
          monday: availabilityData.mondayAvailable || false,
          tuesday: availabilityData.tuesdayAvailable || false,
          wednesday: availabilityData.wednesdayAvailable || false,
          thursday: availabilityData.thursdayAvailable || false,
          friday: availabilityData.fridayAvailable || false,
          saturday: availabilityData.saturdayAvailable || false,
          sunday: availabilityData.sundayAvailable || false
        },
        customTimeSlots: false,
        timeSlots: {
          monday: [],
          tuesday: [],
          wednesday: [],
          thursday: [],
          friday: [],
          saturday: [],
          sunday: []
        }
      });
    }
  }, [availabilityData]);

  // Función para manejar el cambio de valores en la configuración
  const handleInputChange = (field: string, value: any) => {
    setAvailabilitySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Función para manejar el cambio en los días disponibles
  const handleDayToggle = (day: WeekDay) => {
    setAvailabilitySettings(prev => ({
      ...prev,
      availableDays: {
        ...prev.availableDays,
        [day]: !prev.availableDays[day]
      }
    }));
  };

  // Función para guardar los cambios de configuración
  const handleSaveChanges = () => {
    // Mapear los datos del formato local al formato de la API
    const apiData = {
      defaultCheckInTime: availabilitySettings.defaultCheckInTime,
      defaultCheckOutTime: availabilitySettings.defaultCheckOutTime,
      minimumRentalNights: availabilitySettings.minimumRentalDays,
      maximumRentalNights: availabilitySettings.maximumRentalDays,
      // bufferTimeBetweenRentals: availabilitySettings.bufferTimeBetweenRentals,
      advanceBookingPeriod: availabilitySettings.advanceBookingPeriod,
      instantBooking: availabilitySettings.instantBooking,
      allowSameDayBooking: availabilitySettings.allowSameDayBooking,
      cancellationPolicy: availabilitySettings.cancellationPolicy,
      mondayAvailable: availabilitySettings.availableDays.monday,
      tuesdayAvailable: availabilitySettings.availableDays.tuesday,
      wednesdayAvailable: availabilitySettings.availableDays.wednesday,
      thursdayAvailable: availabilitySettings.availableDays.thursday,
      fridayAvailable: availabilitySettings.availableDays.friday,
      saturdayAvailable: availabilitySettings.availableDays.saturday,
      sundayAvailable: availabilitySettings.availableDays.sunday,
    };

    // Mostrar estado de carga
    toast.loading("Actualizando configuración...", { id: "saving-config" });

    vehiclesApi.host.updateAvailabilitySettings(vehicleId, apiData)
      .then(() => {
        queryClient.invalidateQueries({ queryKey: ['vehicle-availability', vehicleId] });
        setIsEditing(false);
        toast.dismiss("saving-config");
        toast.success("Disponibilidad actualizada exitosamente.");
      })
      .catch((error) => {
        toast.dismiss("saving-config");
        console.error("Error al actualizar la disponibilidad:", error);
        toast.error(`No se pudo actualizar la disponibilidad: ${error.message || JSON.stringify(error)}`);
      });
  };

  // Función para cancelar la edición
  const handleCancelEdit = () => {
    // Restaurar valores originales desde los datos de la API
    if (availabilityData) {
      setAvailabilitySettings({
        defaultCheckInTime: availabilityData.defaultCheckInTime || "",
        defaultCheckOutTime: availabilityData.defaultCheckOutTime || "",
        minimumRentalDays: availabilityData.minimumRentalNights || 0,
        maximumRentalDays: availabilityData.maximumRentalNights || 0,
        // bufferTimeBetweenRentals: availabilityData.bufferTimeBetweenRentals || 0,
        advanceBookingPeriod: availabilityData.advanceBookingPeriod || 0,
        instantBooking: availabilityData.instantBooking || false,
        allowSameDayBooking: availabilityData.allowSameDayBooking || false,
        cancellationPolicy: availabilityData.cancellationPolicy || "flexible",
        availableDays: {
          monday: availabilityData.mondayAvailable || false,
          tuesday: availabilityData.tuesdayAvailable || false,
          wednesday: availabilityData.wednesdayAvailable || false,
          thursday: availabilityData.thursdayAvailable || false,
          friday: availabilityData.fridayAvailable || false,
          saturday: availabilityData.saturdayAvailable || false,
          sunday: availabilityData.sundayAvailable || false
        },
        customTimeSlots: false,
        timeSlots: {
          monday: [],
          tuesday: [],
          wednesday: [],
          thursday: [],
          friday: [],
          saturday: [],
          sunday: []
        }
      });
    }
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center flex-wrap space-y-2">
          <div>
            <CardTitle className="text-2xl">Configuración de Disponibilidad</CardTitle>
            <CardDescription>
              Personaliza las preferencias de disponibilidad de tu vehículo.
            </CardDescription>
          </div>
          {!isEditing && (
            <Button onClick={() => setIsEditing(true)}>
              Editar Configuración
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="defaultCheckInTime">Hora de Check-In Predeterminada</Label>
              <Input
                id="defaultCheckInTime"
                type="time"
                value={availabilitySettings.defaultCheckInTime}
                onChange={e => handleInputChange('defaultCheckInTime', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="defaultCheckOutTime">Hora de Check-Out Predeterminada</Label>
              <Input
                id="defaultCheckOutTime"
                type="time"
                value={availabilitySettings.defaultCheckOutTime}
                onChange={e => handleInputChange('defaultCheckOutTime', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="minimumRentalDays">Días Mínimos de Renta</Label>
              <Input
                id="minimumRentalDays"
                type="number"
                value={availabilitySettings.minimumRentalDays}
                onChange={e => handleInputChange('minimumRentalDays', Number(e.target.value))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maximumRentalDays">Días Máximos de Renta</Label>
              <Input
                id="maximumRentalDays"
                type="number"
                value={availabilitySettings.maximumRentalDays}
                onChange={e => handleInputChange('maximumRentalDays', Number(e.target.value))}
              />
            </div>
              {/* <div className="space-y-2">
              <Label htmlFor="bufferTimeBetweenRentals">Tiempo de Buffer Entre Rentas (en horas)</Label>
              <Input
                id="bufferTimeBetweenRentals"
                type="number"
                value={availabilitySettings.bufferTimeBetweenRentals}
                onChange={e => handleInputChange('bufferTimeBetweenRentals', Number(e.target.value))}
              />
            </div> */}
            <div className="space-y-2">
              <Label htmlFor="advanceBookingPeriod">Período de Reserva Anticipada (en días)</Label>
              <Input
                id="advanceBookingPeriod"
                type="number"
                value={availabilitySettings.advanceBookingPeriod}
                onChange={e => handleInputChange('advanceBookingPeriod', Number(e.target.value))}
              />
            </div>
              {/* <div className="space-y-2">
                <Label htmlFor="instantBooking">Reserva Instantánea</Label>
              <Switch
                id="instantBooking"
                checked={availabilitySettings.instantBooking}
                onCheckedChange={checked => handleInputChange('instantBooking', checked)}
              />
            </div> */}
            <div className="space-y-2">
              <Label htmlFor="allowSameDayBooking">Permitir Reserva del Mismo Día</Label>
              <Switch
                id="allowSameDayBooking"
                checked={availabilitySettings.allowSameDayBooking}
                onCheckedChange={checked => handleInputChange('allowSameDayBooking', checked)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cancellationPolicy">Política de Cancelación</Label>
              <Select
                // id="cancellationPolicy"
                value={availabilitySettings.cancellationPolicy}
                onValueChange={value => handleInputChange('cancellationPolicy', value as any)}
              >
                <SelectTrigger id='cancellationPolicy'>
                  <SelectValue placeholder="Selecciona una política" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="flexible">Flexible</SelectItem>
                  <SelectItem value="moderate">Moderada</SelectItem>
                  <SelectItem value="strict">Estricta</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="availableDays">Días Disponibles</Label>
              <div className="flex flex-wrap gap-2">
                {WEEK_DAYS.map(day => {
                  const dayName = {
                    monday: "Lunes",
                    tuesday: "Martes",
                    wednesday: "Miércoles",
                    thursday: "Jueves",
                    friday: "Viernes",
                    saturday: "Sábado",
                    sunday: "Domingo"
                  }[day];

                  return (
                    <Button
                      key={day}
                      type="button"
                      variant={availabilitySettings.availableDays[day] ? "default" : "outline"}
                      onClick={() => handleDayToggle(day)}
                      disabled={!isEditing}
                      className="min-w-[90px]"
                    >
                      {dayName}
                    </Button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </CardContent>

      {isEditing && (
        <CardFooter className="flex gap-5">
          <Button variant="outline" onClick={handleCancelEdit}>
            Cancelar
          </Button>
          <Button onClick={handleSaveChanges}>
            Guardar Cambios
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}



