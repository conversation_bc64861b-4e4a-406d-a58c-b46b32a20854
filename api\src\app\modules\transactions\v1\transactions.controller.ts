import { <PERSON><PERSON>, t } from "elysia";
import { TransactionsService } from "./transactions.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';
import { prisma } from "@/lib/prisma";
import { HostBankingService } from "@/app/modules/host-banking/v1/host-banking.service";

// Esquemas de validación
const refundSchema = t.Object({
  amount: t.Optional(t.Number()),
  reason: t.Optional(t.String())
});

const createTransactionSchema = t.Object({
  reservationId: t.String(),
  stripePaymentIntentId: t.String(),
  amount: t.Number(),
  currency: t.String(),
  paymentMethod: t.Optional(t.String()),
  description: t.Optional(t.String())
});

const updateStripeMetadataSchema = t.Object({
  stripePaymentIntentId: t.String(),
  reservationId: t.String(),
  vehicleId: t.String(),
  hostId: t.String(),
  clientId: t.String()
});

const transactionFiltersSchema = t.Object({
  startDate: t.Optional(t.String()),
  endDate: t.Optional(t.String()),
  status: t.Optional(t.String()),
  minAmount: t.Optional(t.Number()),
  maxAmount: t.Optional(t.Number()),
  hostId: t.Optional(t.String()),
  transferStatus: t.Optional(t.String()),
  limit: t.Optional(t.Number()),
  offset: t.Optional(t.Number())
});

// Controlador para usuarios autenticados
export const userTransactionsController = new Elysia()
  .use(authMiddleware)
  .post('/user/transactions',
    async ({ body, user }) => {
      // Verificar que la reserva pertenece al usuario
      const reservation = await prisma.vehicleReservation.findUnique({
        where: { id: body.reservationId },
        include: { vehicle: true }
      });

      if (!reservation) {
        return { success: false, error: 'Reserva no encontrada' };
      }

      if (reservation.userId !== user.id) {
        return { success: false, error: 'No tienes permisos para crear una transacción para esta reserva' };
      }

      const transaction = await TransactionsService.createTransaction(body);
      return { success: true, data: transaction };

    },
    {
      body: createTransactionSchema
    }
  )
  .post('/user/transactions/update-stripe-metadata',
    async ({ body, user }) => {
      // Verificar que la reserva pertenece al usuario
      const reservation = await prisma.vehicleReservation.findUnique({
        where: { id: body.reservationId },
        include: { vehicle: true }
      });

      if (!reservation) {
        return { success: false, error: 'Reserva no encontrada' };
      }

      if (reservation.userId !== user.id) {
        return { success: false, error: 'No tienes permisos para actualizar esta transacción' };
      }

      const result = await TransactionsService.updateStripePaymentMetadata(body);
      return result;

    },
    {
      body: updateStripeMetadataSchema
    }
  );

// Controlador para administradores
export const adminTransactionsController = new Elysia()
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/admin/transactions/:id',
    async ({ params }) => {
      try {
        const transaction = await prisma.transaction.findUnique({
          where: { id: params.id },
          include: {
            reservation: {
              include: {
                vehicle: {
                  include: {
                    host: {
                      select: {
                        id: true,
                        name: true,
                        email: true
                      }
                    }
                  }
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true
                  }
                }
              }
            }
          }
        });

        if (!transaction) {
          return { success: false, error: 'Transacción no encontrada' };
        }

        return { success: true, data: transaction };
      } catch (error) {
        console.error('Error getting transaction:', error);
        return { success: false, error: 'Error al obtener la transacción' };
      }
    },
    {
      params: t.Object({
        id: t.String()
      })
    }
  )
  .post('/admin/transactions/:id/mark-transferred',
    async ({ params, user }) => {
      const updatedTransaction = await TransactionsService.markAsTransferred(params.id, user.id);
      return { success: true, data: updatedTransaction };
    },
    {
      params: t.Object({
        id: t.String()
      })
    }
  )
  .post('/admin/transactions/:id/refund',
    async ({ params, body }) => {
      const transaction = await TransactionsService.processRefund(params.id, body);
      return { success: true, data: transaction };
    },
    {
      params: t.Object({
        id: t.String()
      }),
      body: refundSchema
    }
  )
  .post('/admin/transactions/:id/transfer',
    async ({ params }) => {
      const transaction = await TransactionsService.processHostTransfer(params.id);
      return { success: true, data: transaction };
    },
    {
      params: t.Object({
        id: t.String()
      })
    }
  )
  .get('/admin/transactions',
    async ({ query }) => {
      try {
        const filters: any = {};
        const limit = query.limit ? parseInt(query.limit.toString()) : 10;
        const offset = query.offset ? parseInt(query.offset.toString()) : 0;

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);
        if (query.status) filters.status = query.status;
        if (query.minAmount) filters.minAmount = query.minAmount;
        if (query.maxAmount) filters.maxAmount = query.maxAmount;
        if (query.hostId) filters.hostId = query.hostId;
        if (query.transferStatus) filters.transferStatus = query.transferStatus;

        // Construir condiciones WHERE
        const whereConditions: any = {
          ...(filters.startDate || filters.endDate ? {
            createdAt: {
              ...(filters.startDate && { gte: filters.startDate }),
              ...(filters.endDate && { lte: filters.endDate })
            }
          } : {}),
          ...(filters.status && { status: filters.status }),
          ...(filters.minAmount || filters.maxAmount ? {
            amount: {
              ...(filters.minAmount && { gte: filters.minAmount }),
              ...(filters.maxAmount && { lte: filters.maxAmount })
            }
          } : {}),
          ...(filters.hostId && {
            reservation: {
              vehicle: {
                hostId: filters.hostId
              }
            }
          }),
          ...(filters.transferStatus && {
            status: filters.transferStatus === 'transferred' ? 'transferred' : { not: 'transferred' }
          })
        };

        // Obtener el total de registros para paginación
        const totalCount = await prisma.transaction.count({
          where: whereConditions
        });

        // Obtener transacciones con paginación y ordenamiento
        const transactions = await prisma.transaction.findMany({
          where: whereConditions,
          include: {
            reservation: {
              include: {
                vehicle: {
                  include: {
                    host: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true
                      }
                    }
                  }
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true
                  }
                }
              }
            }
          },
          // Ordenar por fecha de creación (más antiguos primero) y luego por estatus (pendientes primero)
          orderBy: [
            { createdAt: 'asc' },
            { status: 'asc' }
          ],
          take: limit,
          skip: offset
        });

        // Calcular estadísticas generales (de todas las transacciones, no solo la página actual)
        const allTransactions = await prisma.transaction.findMany({
          where: whereConditions,
          select: {
            amount: true,
            platformFee: true,
            hostEarnings: true,
            status: true
          }
        });

        const totalAmount = allTransactions.reduce((sum, t) => sum + t.amount, 0);
        const totalPlatformFees = allTransactions.reduce((sum, t) => sum + t.platformFee, 0);
        const totalHostEarnings = allTransactions.reduce((sum, t) => sum + t.hostEarnings, 0);
        const totalTransferred = allTransactions.filter(t => t.status === 'transferred').reduce((sum, t) => sum + t.hostEarnings, 0);
        const pendingTransfer = allTransactions.filter(t => t.status === 'succeeded').reduce((sum, t) => sum + t.hostEarnings, 0);

        return {
          success: true,
          data: {
            transactions,
            pagination: {
              total: totalCount,
              limit,
              offset,
              pages: Math.ceil(totalCount / limit),
              currentPage: Math.floor(offset / limit) + 1
            },
            stats: {
              totalTransactions: allTransactions.length,
              totalAmount,
              totalPlatformFees,
              totalHostEarnings,
              totalTransferred,
              pendingTransfer
            }
          }
        };
      } catch (error) {
        console.error('Error getting admin transactions:', error);
        return { success: false, error: 'Error al obtener transacciones' };
      }
    },
    {
      query: transactionFiltersSchema
    }
  )
  .get('/admin/transactions/stats',
    async ({ query }) => {
      try {
        const filters: any = {};

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);
        if (query.status) filters.status = query.status;
        if (query.minAmount) filters.minAmount = query.minAmount;
        if (query.maxAmount) filters.maxAmount = query.maxAmount;
        if (query.hostId) filters.hostId = query.hostId;
        if (query.transferStatus) filters.transferStatus = query.transferStatus;

        // Construir condiciones WHERE
        const whereConditions: any = {
          ...(filters.startDate || filters.endDate ? {
            createdAt: {
              ...(filters.startDate && { gte: filters.startDate }),
              ...(filters.endDate && { lte: filters.endDate })
            }
          } : {}),
          ...(filters.status && { status: filters.status }),
          ...(filters.minAmount || filters.maxAmount ? {
            amount: {
              ...(filters.minAmount && { gte: filters.minAmount }),
              ...(filters.maxAmount && { lte: filters.maxAmount })
            }
          } : {}),
          ...(filters.hostId && {
            reservation: {
              vehicle: {
                hostId: filters.hostId
              }
            }
          }),
          ...(filters.transferStatus && {
            status: filters.transferStatus === 'transferred' ? 'transferred' : { not: 'transferred' }
          })
        };

        const transactions = await prisma.transaction.findMany({
          where: whereConditions,
          select: {
            amount: true,
            platformFee: true,
            hostEarnings: true,
            status: true,
            createdAt: true
          }
        });

        const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
        const totalPlatformFees = transactions.reduce((sum, t) => sum + t.platformFee, 0);
        const totalHostEarnings = transactions.reduce((sum, t) => sum + t.hostEarnings, 0);
        const totalTransferred = transactions.filter(t => t.status === 'transferred').reduce((sum, t) => sum + t.hostEarnings, 0);
        const pendingTransfer = transactions.filter(t => t.status === 'succeeded').reduce((sum, t) => sum + t.hostEarnings, 0);

        return {
          success: true,
          data: {
            totalTransactions: transactions.length,
            totalAmount,
            totalPlatformFees,
            totalHostEarnings,
            totalTransferred,
            pendingTransfer
          }
        };
      } catch (error) {
        console.error('Error getting admin transaction stats:', error);
        return { success: false, error: 'Error al obtener estadísticas' };
      }
    },
    {
      query: transactionFiltersSchema
    }
  )
  .get('/admin/transactions/chart-data',
    async ({ query }) => {
      try {
        const filters: any = {};
        const groupBy = query.groupBy || 'day'; // 'day', 'week', 'month'

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);

        // Construir condiciones WHERE
        const whereConditions: any = {
          ...(filters.startDate || filters.endDate ? {
            createdAt: {
              ...(filters.startDate && { gte: filters.startDate }),
              ...(filters.endDate && { lte: filters.endDate })
            }
          } : {}),
          status: 'succeeded' // Solo transacciones exitosas
        };

        const transactions = await prisma.transaction.findMany({
          where: whereConditions,
          select: {
            amount: true,
            platformFee: true,
            hostEarnings: true,
            createdAt: true
          },
          orderBy: { createdAt: 'asc' }
        });

        // Agrupar datos según el tipo solicitado
        let chartData: any[] = [];

        if (groupBy === 'day') {
          // Agrupar por día
          const groupedByDay = transactions.reduce((acc, transaction) => {
            const date = new Date(transaction.createdAt);
            const dayKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
            const dayName = date.toLocaleDateString('es-ES', { weekday: 'long' });

            if (!acc[dayKey]) {
              acc[dayKey] = {
                name: dayName.charAt(0).toUpperCase() + dayName.slice(1),
                date: dayKey,
                ingresos: 0,
                comisiones: 0,
                transacciones: 0
              };
            }

            acc[dayKey].ingresos += transaction.amount;
            acc[dayKey].comisiones += transaction.platformFee;
            acc[dayKey].transacciones += 1;

            return acc;
          }, {} as Record<string, any>);

          chartData = Object.values(groupedByDay);
        } else if (groupBy === 'week') {
          // Agrupar por semana
          const groupedByWeek = transactions.reduce((acc, transaction) => {
            const date = new Date(transaction.createdAt);
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay()); // Inicio de semana (domingo)
            const weekKey = weekStart.toISOString().split('T')[0];

            if (!acc[weekKey]) {
              acc[weekKey] = {
                name: `Semana ${weekStart.toLocaleDateString('es-ES', { day: '2-digit', month: '2-digit' })}`,
                date: weekKey,
                ingresos: 0,
                comisiones: 0,
                transacciones: 0
              };
            }

            acc[weekKey].ingresos += transaction.amount;
            acc[weekKey].comisiones += transaction.platformFee;
            acc[weekKey].transacciones += 1;

            return acc;
          }, {} as Record<string, any>);

          chartData = Object.values(groupedByWeek);
        }

        return {
          success: true,
          data: chartData
        };
      } catch (error) {
        console.error('Error getting chart data:', error);
        return { success: false, error: 'Error al obtener datos del gráfico' };
      }
    },
    {
      query: t.Object({
        groupBy: t.Optional(t.String()),
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String())
      })
    }
  )
  .get('/admin/transactions/:id/host-banking',
    async ({ params }) => {
      try {
        // Primero obtener la transacción para obtener el hostId
        const transaction = await prisma.transaction.findUnique({
          where: { id: params.id },
          include: {
            reservation: {
              include: {
                vehicle: {
                  select: { hostId: true }
                }
              }
            }
          }
        });

        if (!transaction) {
          return { success: false, error: 'Transacción no encontrada' };
        }

        const hostId = transaction.reservation.vehicle.hostId;

        // Obtener información bancaria del host
        const defaultAccount = await HostBankingService.getDefaultBankAccount(hostId);

        return { success: true, data: defaultAccount };
      } catch (error) {
        console.error('Error getting host banking info:', error);
        return { success: false, error: 'Error al obtener información bancaria del host' };
      }
    },
    {
      params: t.Object({
        id: t.String()
      })
    }
  )
  .post('/admin/transactions/:id/mark-transferred',
    async ({ params, user }) => {
      try {
        // Verificar que la transacción existe
        const transaction = await prisma.transaction.findUnique({
          where: { id: params.id }
        });

        if (!transaction) {
          return { success: false, error: 'Transacción no encontrada' };
        }

        if (transaction.status === 'transferred') {
          return { success: false, error: 'La transacción ya está marcada como transferida' };
        }

        // Actualizar la transacción como transferida
        const updatedTransaction = await prisma.transaction.update({
          where: { id: params.id },
          data: {
            status: 'transferred',
            transferredAt: new Date()
          },
          include: {
            reservation: {
              include: {
                vehicle: {
                  include: {
                    host: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true
                      }
                    }
                  }
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true
                  }
                }
              }
            }
          }
        });

        return { success: true, data: updatedTransaction };
      } catch (error) {
        console.error('Error marking transaction as transferred:', error);
        return { success: false, error: 'Error al marcar transacción como transferida' };
      }
    },
    {
      params: t.Object({
        id: t.String()
      })
    }
  );

// Controlador para hosts
export const hostTransactionsController = new Elysia()
  .use(authMiddleware)
  .get('/host/transactions',
    async ({ query, user }) => {
      try {
        const filters: any = {};

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);
        if (query.status) filters.status = query.status;
        if (query.limit) filters.limit = parseInt(query.limit.toString());
        if (query.offset) filters.offset = parseInt(query.offset.toString());

        const transactions = await TransactionsService.getHostTransactions(user.id, filters);

        // Calcular estadísticas para el host
        const totalEarnings = transactions.reduce((sum, t) => sum + t.hostEarnings, 0);
        const totalPlatformFees = transactions.reduce((sum, t) => sum + t.platformFee, 0);
        const totalTransferred = transactions.filter(t => t.status === 'transferred').reduce((sum, t) => sum + t.hostEarnings, 0);
        const pendingTransfer = transactions.filter(t => t.status === 'succeeded').reduce((sum, t) => sum + t.hostEarnings, 0);

        return {
          success: true,
          data: {
            transactions,
            stats: {
              totalEarnings,
              totalPlatformFees,
              totalTransferred,
              pendingTransfer
            }
          }
        };
      } catch (error) {
        console.error('Error getting host transactions:', error);
        return { success: false, error: 'Error al obtener transacciones' };
      }
    },
    {
      query: transactionFiltersSchema
    }
)
  .get('/host/transactions/stats',
    async ({ query, user }) => {
      try {
        const filters: any = {};

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);
        if (query.status) filters.status = query.status;

        const transactions = await TransactionsService.getHostTransactions(user.id, filters);

        const totalEarnings = transactions.reduce((sum, t) => sum + t.hostEarnings, 0);
        const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
        const totalPlatformFees = transactions.reduce((sum, t) => sum + t.platformFee, 0);
        const totalTransferred = transactions.filter(t => t.status === 'transferred').reduce((sum, t) => sum + t.hostEarnings, 0);
        const pendingTransfer = transactions.filter(t => t.status === 'succeeded').reduce((sum, t) => sum + t.hostEarnings, 0);
        const totalTransactions = transactions.length;

        return {
          success: true,
          data: {
            totalEarnings,
            totalAmount,
            totalPlatformFees,
            totalTransferred,
            pendingTransfer,
            totalTransactions,
            averageCommissionRate: totalAmount > 0 ? Math.round((totalPlatformFees / totalAmount) * 100) : 12
          }
        };
      } catch (error) {
        console.error('Error getting host transaction stats:', error);
        return { success: false, error: 'Error al obtener estadísticas' };
      }
    },
    {
      query: transactionFiltersSchema
    }
  )
  .get('/host/transactions/chart-data',
    async ({ query, user }) => {
      try {
        const filters: any = {};
        const groupBy = query.groupBy || 'day'; // 'day', 'week', 'month'

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);

        const transactions = await TransactionsService.getHostTransactions(user.id, filters);

        // Agrupar transacciones por período
        const groupedData = new Map();

        transactions.forEach(transaction => {
          let key: string;
          const date = new Date(transaction.createdAt);

          switch (groupBy) {
            case 'week':
              const weekStart = new Date(date);
              weekStart.setDate(date.getDate() - date.getDay());
              key = weekStart.toISOString().split('T')[0];
              break;
            case 'month':
              key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
              break;
            default: // day
              key = date.toISOString().split('T')[0];
          }

          if (!groupedData.has(key)) {
            groupedData.set(key, {
              name: key,
              ingresos: 0,
              comisiones: 0,
              ganancias: 0,
              transacciones: 0
            });
          }

          const group = groupedData.get(key);
          group.ingresos += transaction.amount;
          group.comisiones += transaction.platformFee;
          group.ganancias += transaction.hostEarnings;
          group.transacciones += 1;
        });

        const chartData = Array.from(groupedData.values()).sort((a, b) => a.name.localeCompare(b.name));

        return {
          success: true,
          data: chartData
        };
      } catch (error) {
        console.error('Error getting host chart data:', error);
        return { success: false, error: 'Error al obtener datos del gráfico' };
      }
    },
    {
      query: t.Object({
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
        groupBy: t.Optional(t.String())
      })
    }
  );

// Controlador para clientes
export const clientTransactionsController = new Elysia()
  .use(authMiddleware)
  .get('/client/transactions',
    async ({ query, user }) => {
        const filters: any = {};

        if (query.startDate) filters.startDate = new Date(query.startDate);
        if (query.endDate) filters.endDate = new Date(query.endDate);
        if (query.status) filters.status = query.status;
        if (query.limit) filters.limit = parseInt(query.limit.toString());
        if (query.offset) filters.offset = parseInt(query.offset.toString());

      const result = await TransactionsService.getClientTransactionsWithPagination(user.id, filters);

      return {
        success: true,
        data: result
      };

    },
    {
      query: transactionFiltersSchema
    }
  )
  .get('/client/transactions/stats',
    async ({ query, user }) => {
      const filters: any = {};

      if (query.startDate) filters.startDate = new Date(query.startDate);
      if (query.endDate) filters.endDate = new Date(query.endDate);
      if (query.status) filters.status = query.status;

      const transactions = await TransactionsService.getClientTransactions(user.id, filters);

      // Calcular estadísticas para el cliente
      const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);
      const totalRefunded = transactions.filter(t => t.refundAmount).reduce((sum, t) => sum + (t.refundAmount || 0), 0);
      const successfulPayments = transactions.filter(t => t.status === 'succeeded' || t.status === 'transferred').length;

      const stats = {
        totalSpent,
        totalRefunded,
        totalTransactions: transactions.length,
        successfulPayments,
        averageTransactionValue: transactions.length > 0 ? totalSpent / transactions.length : 0
      };

      return { success: true, data: { stats } };
    },
    {
      query: t.Optional(t.Object({
        startDate: t.Optional(t.String()),
        endDate: t.Optional(t.String()),
        status: t.Optional(t.String())
      }))
    }
  );
