"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { ColumnDef } from "@tanstack/react-table"
import { Search, Filter, MoreHorizontal, Eye, Download, DollarSign } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, /* AvatarImage */ } from "@/components/ui/avatar"
import { DataTable } from "@/components/data-table/data-table"
import { AdvancedFiltersModal, AdvancedFilters } from "@/components/admin/AdvancedFiltersModal"
import { adminTransactionsApi, Transaction } from "@/lib/api/transactions.api"
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

// Estados y etiquetas para transacciones
const statusColors = {
  succeeded: "bg-green-100 text-green-800",
  pending: "bg-yellow-100 text-yellow-800",
  processing: "bg-blue-100 text-blue-800",
  failed: "bg-red-100 text-red-800",
  refunded: "bg-gray-100 text-gray-800",
}

const statusLabels = {
  succeeded: "Completado",
  pending: "Pendiente",
  processing: "Procesando",
  failed: "Fallido",
  refunded: "Reembolsado",
}

export default function AdminPayoutsPage() {
  const router = useRouter()
  const [page, setPage] = useState(0) // DataTable usa índice 0
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilters>({})
  const [statsFilter, setStatsFilter] = useState("current-month")
  const limit = 10

  // Funciones para obtener rangos de fechas por mes
  const getMonthRange = (monthsBack: number) => {
    const now = new Date()
    const startDate = new Date(now.getFullYear(), now.getMonth() - monthsBack, 1)
    const endDate = new Date(now.getFullYear(), now.getMonth() - monthsBack + 1, 0, 23, 59, 59, 999)
    return { startDate, endDate }
  }

  const getStatsDateRange = () => {
    switch (statsFilter) {
      case "current-month":
        return getMonthRange(0)
      case "previous-month":
        return getMonthRange(1)
      case "2-months-ago":
        return getMonthRange(2)
      case "3-months-ago":
        return getMonthRange(3)
      default:
        return getMonthRange(0)
    }
  }

  const getPreviousMonthRange = () => {
    switch (statsFilter) {
      case "current-month":
        return getMonthRange(1)
      case "previous-month":
        return getMonthRange(2)
      case "2-months-ago":
        return getMonthRange(3)
      case "3-months-ago":
        return getMonthRange(4)
      default:
        return getMonthRange(1)
    }
  }

  // Query para obtener transacciones
  const { data: transactionsData, isLoading } = useQuery({
    queryKey: ['admin-transactions', page, searchTerm, statusFilter, advancedFilters],
    queryFn: async () => {
      const response = await adminTransactionsApi.getTransactions({
        limit,
        offset: page * limit,
        ...(statusFilter !== "all" && { status: statusFilter }),
        ...(advancedFilters.dateRange && {
          startDate: advancedFilters.dateRange.from.toISOString(),
          endDate: advancedFilters.dateRange.to.toISOString()
        }),
        ...(advancedFilters.minAmount && { minAmount: advancedFilters.minAmount }),
        ...(advancedFilters.maxAmount && { maxAmount: advancedFilters.maxAmount }),
        ...(advancedFilters.hostId && { hostId: advancedFilters.hostId }),
        ...(advancedFilters.transferStatus && { transferStatus: advancedFilters.transferStatus }),
        // TODO: Agregar filtro de búsqueda si es necesario
      })
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener transacciones')
      }
      return response.data
    },
    staleTime: 30 * 1000, // 30 segundos
  })

  // Query para obtener estadísticas del período seleccionado
  const { data: statsData } = useQuery({
    queryKey: ['admin-transaction-stats', statsFilter],
    queryFn: async () => {
      const { startDate, endDate } = getStatsDateRange()
      const response = await adminTransactionsApi.getStats({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener estadísticas')
      }
      return response.data
    },
    staleTime: 60 * 1000, // 1 minuto
  })

  // Query para obtener estadísticas del período anterior (para comparación)
  const { data: previousStatsData } = useQuery({
    queryKey: ['admin-transaction-stats-previous', statsFilter],
    queryFn: async () => {
      const { startDate, endDate } = getPreviousMonthRange()
      const response = await adminTransactionsApi.getStats({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener estadísticas anteriores')
      }
      return response.data
    },
    staleTime: 60 * 1000, // 1 minuto
  })

  const transactions = transactionsData?.data?.transactions || []
  const pagination = transactionsData?.data?.pagination || { total: 0, limit, offset: 0, pages: 0, currentPage: 1 }
  const stats = statsData?.data || {
    totalTransactions: 0,
    totalAmount: 0,
    totalPlatformFees: 0,
    totalHostEarnings: 0,
    totalTransferred: 0,
    pendingTransfer: 0
  }
  const previousStats = previousStatsData?.data || {
    totalTransactions: 0,
    totalAmount: 0,
    totalPlatformFees: 0,
    totalHostEarnings: 0,
    totalTransferred: 0,
    pendingTransfer: 0
  }

  // Funciones para calcular cambios porcentuales
  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return Math.round(((current - previous) / previous) * 100)
  }

  const formatPercentageChange = (current: number, previous: number) => {
    const change = calculatePercentageChange(current, previous)
    const sign = change > 0 ? '+' : ''
    return `${sign}${change}%`
  }

  const getStatsFilterLabel = () => {
    switch (statsFilter) {
      case "current-month":
        return "mes pasado"
      case "previous-month":
        return "hace 2 meses"
      case "2-months-ago":
        return "hace 3 meses"
      case "3-months-ago":
        return "hace 4 meses"
      default:
        return "mes pasado"
    }
  }

  // Función para exportar datos
  const handleExportData = async () => {
    try {
      // Obtener todas las transacciones sin paginación
      const response = await adminTransactionsApi.getTransactions({
        limit: 10000, // Límite alto para obtener todos los datos
        offset: 0,
        ...(statusFilter !== "all" && { status: statusFilter }),
        ...(advancedFilters.dateRange && {
          startDate: advancedFilters.dateRange.from.toISOString(),
          endDate: advancedFilters.dateRange.to.toISOString()
        }),
        ...(advancedFilters.minAmount && { minAmount: advancedFilters.minAmount }),
        ...(advancedFilters.maxAmount && { maxAmount: advancedFilters.maxAmount }),
        ...(advancedFilters.hostId && { hostId: advancedFilters.hostId }),
        ...(advancedFilters.transferStatus && { transferStatus: advancedFilters.transferStatus }),
      })

      if (!response.success) {
        throw new Error(response.error || 'Error al obtener datos para exportar')
      }

      const allTransactions = response.data?.data?.transactions || []

      // Preparar datos para Excel
      const excelData = allTransactions.map((transaction: Transaction) => ({
        'ID Transacción': transaction.id,
        'Fecha': new Date(transaction.createdAt).toLocaleDateString('es-ES', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/\//g, '-'),
        'Host': transaction.reservation?.vehicle?.host?.name || 'N/A',
        'Email Host': transaction.reservation?.vehicle?.host?.email || 'N/A',
        'Cliente': transaction.reservation?.user?.name || 'N/A',
        'Email Cliente': transaction.reservation?.user?.email || 'N/A',
        'Vehículo': `${transaction.reservation?.vehicle?.make} ${transaction.reservation?.vehicle?.model} ${transaction.reservation?.vehicle?.year}`,
        'Monto Total': `$${transaction.amount.toLocaleString()}`,
        'Comisión Plataforma': `$${transaction.platformFee.toLocaleString()}`,
        'Ganancia Host': `$${transaction.hostEarnings.toLocaleString()}`,
        'Estado': statusLabels[transaction.status as keyof typeof statusLabels] || transaction.status,
        'Estado Transferencia': transaction.status === 'transferred' ? 'Transferido' : 'Pendiente',
        'Método Pago': transaction.paymentMethod || 'N/A',
        'ID Stripe': transaction.stripePaymentIntentId || 'N/A'
      }))

      // Crear workbook
      const wb = XLSX.utils.book_new()

      // Crear hoja de transacciones
      const ws = XLSX.utils.json_to_sheet(excelData)

      // Ajustar ancho de columnas
      const colWidths = [
        { wch: 20 }, // ID Transacción
        { wch: 12 }, // Fecha
        { wch: 20 }, // Host
        { wch: 25 }, // Email Host
        { wch: 20 }, // Cliente
        { wch: 25 }, // Email Cliente
        { wch: 30 }, // Vehículo
        { wch: 15 }, // Monto Total
        { wch: 18 }, // Comisión
        { wch: 15 }, // Ganancia Host
        { wch: 12 }, // Estado
        { wch: 18 }, // Estado Transferencia
        { wch: 15 }, // Método Pago
        { wch: 20 }  // ID Stripe
      ]
      ws['!cols'] = colWidths

      // Agregar worksheet de transacciones
      XLSX.utils.book_append_sheet(wb, ws, 'Transacciones')

      // Crear hoja de estadísticas con fórmulas
      const statsData = [
        ['Estadísticas de Transacciones', ''],
        ['', ''],
        ['Métrica', 'Valor'],
        ['Total de Transacciones', `=COUNTA(Transacciones.A:A)-1`],
        ['Total de Ingresos', `=SUMPRODUCT(VALUE(SUBSTITUTE(SUBSTITUTE(Transacciones.H:H,"$",""),",","")))`],
        ['Total de Comisiones', `=SUMPRODUCT(VALUE(SUBSTITUTE(SUBSTITUTE(Transacciones.I:I,"$",""),",","")))`],
        ['Total Ganancias Host', `=SUMPRODUCT(VALUE(SUBSTITUTE(SUBSTITUTE(Transacciones.J:J,"$",""),",","")))`],
        ['Transacciones Transferidas', `=COUNTIF(Transacciones.L:L,"Transferido")`],
        ['Transacciones Pendientes', `=COUNTIF(Transacciones.L:L,"Pendiente")`],
        ['', ''],
        ['Nota:', 'Las estadísticas se actualizan automáticamente'],
        ['', 'cuando cambias el estado de transferencia en la hoja Transacciones']
      ]

      const statsWs = XLSX.utils.aoa_to_sheet(statsData)

      // Formatear la hoja de estadísticas
      statsWs['!cols'] = [{ wch: 25 }, { wch: 30 }]

      // Agregar worksheet de estadísticas
      XLSX.utils.book_append_sheet(wb, statsWs, 'Estadísticas')

      // Generar archivo y descargar
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

      const fileName = `transacciones_${new Date().toISOString().split('T')[0]}.xlsx`
      saveAs(data, fileName)

    } catch (error) {
      console.error('Error al exportar datos:', error)
      // Aquí podrías agregar una notificación de error
    }
  }

  // Definir columnas para DataTable
  const columns: ColumnDef<Transaction>[] = [
    {
      id: "host",
      header: "Anfitrión",
      cell: ({ row }) => {
        const transaction = row.original
        const host = transaction.reservation?.vehicle?.host
        const hostName = host?.name || `Host-${transaction.reservationId.slice(-4)}`
        const hostInitials = host?.name ? host.name.split(' ').map(n => n[0]).join('') : 'H'

        return (
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarFallback>
                {hostInitials}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{hostName}</div>
              {host?.email && (
                <div className="text-sm text-muted-foreground">{host.email}</div>
              )}
            </div>
          </div>
        )
      },
    },
    {
      id: "vehicle",
      header: "Vehículo",
      cell: ({ row }) => {
        const transaction = row.original
        const vehicle = transaction.reservation?.vehicle
        const vehicleInfo = vehicle ? `${vehicle.year} ${vehicle.make} ${vehicle.model}` : 'N/A'

        return (
          <div>
            <div className="font-medium">{vehicleInfo}</div>
            <div className="text-sm text-muted-foreground font-mono">{transaction.reservationId.slice(-8)}</div>
          </div>
        )
      },
    },
    {
      id: "client",
      header: "Cliente",
      cell: ({ row }) => {
        const transaction = row.original
        const client = transaction.reservation?.user
        const clientName = client?.name || 'Cliente'
        const clientInitials = client?.name ? client.name.split(' ').map(n => n[0]).join('') : 'C'

        return (
          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {clientInitials}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium text-sm">{clientName}</div>
              {client?.email && (
                <div className="text-xs text-muted-foreground">{client.email}</div>
              )}
            </div>
          </div>
        )
      },
    },
    {
      id: "amount",
      header: "Monto Bruto",
      cell: ({ row }) => {
        const transaction = row.original
        return `$${transaction.amount.toLocaleString()}`
      },
    },
    {
      id: "commission",
      header: "Comisión",
      cell: ({ row }) => {
        const transaction = row.original
        return `$${transaction.platformFee.toLocaleString()}`
      },
    },
    {
      id: "netAmount",
      header: "Monto Neto",
      cell: ({ row }) => {
        const transaction = row.original
        return (
          <div className="font-medium">
            ${transaction.hostEarnings.toLocaleString()}
          </div>
        )
      },
    },
    {
      id: "date",
      header: "Fecha",
      cell: ({ row }) => {
        const transaction = row.original
        const date = new Date(transaction.createdAt)
        return (
          <div className="text-sm">
            <div>{date.toLocaleDateString('es-ES', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/\//g, '-')}</div>
            <div className="text-muted-foreground">{date.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}</div>
          </div>
        )
      },
    },
    {
      id: "status",
      header: "Estado",
      cell: ({ row }) => {
        const transaction = row.original
        return (
          <Badge className={statusColors[transaction.status as keyof typeof statusColors]}>
            {statusLabels[transaction.status as keyof typeof statusLabels]}
          </Badge>
        )
      },
    },

    {
      id: "actions",
      header: "Acciones",
      cell: ({ row }) => {
        const transaction = row.original
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Abrir menú</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => router.push(`/dashboard/admin/payouts/${transaction.id}`)}>
                  <Eye className="mr-2 h-4 w-4" />
                  Ver Detalle
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )
      },
    },
  ]

  const handlePageChange = (newPage: number) => {
    setPage(newPage) // DataTable y nuestra API usan el mismo índice
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pagos</h1>
          <p className="text-muted-foreground">Gestiona los pagos y comisiones de la plataforma</p>
        </div>
        <Button onClick={handleExportData}>
          <Download className="mr-2 h-4 w-4" />
          Exportar
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="space-y-4">
        {/* Selector de período para estadísticas */}
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold">Estadísticas</h2>
          <Select value={statsFilter} onValueChange={setStatsFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Seleccionar período" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current-month">Mes actual</SelectItem>
              <SelectItem value="previous-month">Mes anterior</SelectItem>
              <SelectItem value="2-months-ago">2 meses atrás</SelectItem>
              <SelectItem value="3-months-ago">3 meses atrás</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pagos</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
              <div className="text-2xl font-bold">${(stats.totalAmount || 0).toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {formatPercentageChange(stats.totalAmount || 0, previousStats.totalAmount || 0)} desde {getStatsFilterLabel()}
              </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Comisiones</CardTitle>
          </CardHeader>
          <CardContent>
              <div className="text-2xl font-bold">${(stats.totalPlatformFees || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
                {stats.totalAmount ? Math.round(((stats.totalPlatformFees || 0) / stats.totalAmount) * 100) : 0}% del total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendientes</CardTitle>
          </CardHeader>
          <CardContent>
              <div className="text-2xl font-bold">${(stats.pendingTransfer || 0).toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {formatPercentageChange(stats.pendingTransfer || 0, previousStats.pendingTransfer || 0)} desde {getStatsFilterLabel()}
              </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transferidos</CardTitle>
          </CardHeader>
          <CardContent>
              <div className="text-2xl font-bold">${(stats.totalTransferred || 0).toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {formatPercentageChange(stats.totalTransferred || 0, previousStats.totalTransferred || 0)} desde {getStatsFilterLabel()}
              </p>
          </CardContent>
        </Card>
        </div>
      </div>

      {/* Payouts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Pagos</CardTitle>
          <CardDescription>Administra todos los pagos a anfitriones y comisiones</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar pagos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filtrar por estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="succeeded">Completado</SelectItem>
                <SelectItem value="pending">Pendiente</SelectItem>
                <SelectItem value="transferred">Transferido</SelectItem>
                <SelectItem value="failed">Fallido</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={() => setShowAdvancedFilters(true)}>
              <Filter className="mr-2 h-4 w-4" />
              Más Filtros
            </Button>
          </div>

          <DataTable
            columns={columns}
            data={transactions}
            rowCount={pagination.total}
            pageSize={limit}
            pageIndex={page}
            onPageChange={handlePageChange}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* Modal de Filtros Avanzados */}
      <AdvancedFiltersModal
        open={showAdvancedFilters}
        onOpenChange={setShowAdvancedFilters}
        filters={advancedFilters}
        onFiltersChange={setAdvancedFilters}
        hosts={[]} // TODO: Agregar lista de hosts si es necesario
      />
    </div>
  )
}
