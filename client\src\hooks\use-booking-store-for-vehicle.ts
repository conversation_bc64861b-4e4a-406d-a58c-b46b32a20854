import { useEffect } from 'react'
import { useBookingStore } from '@/lib/store/booking-store'
import {
  saveBookingStateToStorage,
  getBookingStateFromStorage,
  clearBookingStateFromStorage,
  initializeBookingSystem
} from '@/lib/booking-utils'

/**
 * Hook personalizado que maneja el estado de booking específico por vehículo
 * Carga y guarda automáticamente el estado en localStorage por vehículo
 */
export function useBookingStoreForVehicle(vehicleId: string) {
  const store = useBookingStore()

  // Inicializar sistema de booking una sola vez
  useEffect(() => {
    initializeBookingSystem()
  }, [])

  // Cargar estado específico del vehículo al montar o cambiar vehicleId
  useEffect(() => {
    if (!vehicleId) return

    // Solo cargar si el vehículo actual es diferente
    if (store.vehicle?.id !== vehicleId) {
      const savedState = getBookingStateFromStorage(vehicleId)

      if (savedState) {
        console.log(`Cargando estado guardado para vehículo ${vehicleId}`)

        // Restaurar el estado específico del vehículo usando el estado interno del store
        const newState = {
          vehicle: savedState.vehicle || null,
          dateRange: savedState.dateRange ? {
            startDate: savedState.dateRange.startDate ? new Date(savedState.dateRange.startDate) : null,
            endDate: savedState.dateRange.endDate ? new Date(savedState.dateRange.endDate) : null
          } : { startDate: null, endDate: null },
          pickupLocation: savedState.pickupLocation || "",
          returnLocation: savedState.returnLocation || "",
          coverage: savedState.coverage || null,
          addOns: savedState.addOns || [],
          mileagePackage: savedState.mileagePackage || null,
          currentStep: savedState.currentStep || 1,
          totalPrice: savedState.totalPrice || 0,
          contactInfo: savedState.contactInfo || {},
          paymentInfo: savedState.paymentInfo || {}
        }

        // Usar el método interno de zustand para evitar disparar efectos
        useBookingStore.setState(newState)
      } else {
        // No hay estado guardado, inicializar estado limpio
        console.log(`No hay estado guardado para vehículo ${vehicleId}, iniciando limpio`)
        useBookingStore.setState({
          vehicle: null,
          dateRange: { startDate: null, endDate: null },
          currentStep: 1,
          totalPrice: 0
        })
      }
    }
  }, [vehicleId, store.vehicle?.id])

  // Guardar estado automáticamente cuando cambie (debounced)
  useEffect(() => {
    if (!vehicleId || !store.vehicle?.id) return

    // Solo guardar si el vehículo actual coincide con el vehicleId
    if (store.vehicle.id === vehicleId) {
      const timeoutId = setTimeout(() => {
        const currentState = {
          vehicle: store.vehicle,
          dateRange: store.dateRange,
          pickupLocation: store.pickupLocation,
          returnLocation: store.returnLocation,
          coverage: store.coverage,
          addOns: store.addOns,
          mileagePackage: store.mileagePackage,
          currentStep: store.currentStep,
          totalPrice: store.totalPrice,
          contactInfo: store.contactInfo,
          paymentInfo: store.paymentInfo
        }

        saveBookingStateToStorage(vehicleId, currentState)
      }, 500) // Debounce de 500ms

      return () => clearTimeout(timeoutId)
    }
  }, [
    vehicleId,
    store.vehicle,
    store.dateRange,
    store.pickupLocation,
    store.returnLocation,
    store.coverage,
    store.addOns,
    store.mileagePackage,
    store.currentStep,
    store.totalPrice,
    store.contactInfo,
    store.paymentInfo
  ])

  // Función para limpiar el estado de este vehículo específico
  const clearVehicleState = () => {
    if (vehicleId) {
      clearBookingStateFromStorage(vehicleId)
      store.reset()
    }
  }

  // Función para cambiar a otro vehículo
  const switchToVehicle = (/* newVehicleId: string */) => {
    // Guardar estado actual antes de cambiar
    if (vehicleId && store.vehicle?.id === vehicleId) {
      const currentState = {
        vehicle: store.vehicle,
        dateRange: store.dateRange,
        pickupLocation: store.pickupLocation,
        returnLocation: store.returnLocation,
        coverage: store.coverage,
        addOns: store.addOns,
        mileagePackage: store.mileagePackage,
        currentStep: store.currentStep,
        totalPrice: store.totalPrice,
        contactInfo: store.contactInfo,
        paymentInfo: store.paymentInfo
      }

      saveBookingStateToStorage(vehicleId, currentState)
    }
  }

  return {
    ...store,
    clearVehicleState,
    switchToVehicle
  }
}
