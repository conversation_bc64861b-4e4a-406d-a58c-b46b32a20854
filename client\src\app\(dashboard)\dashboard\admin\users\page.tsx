"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import useDebounce from "@/hooks/use-debounce"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { UserTable } from "@/components/admin/user-table"
import { UserStatsCards } from "@/components/admin/user-stats-cards"
import { Search, Download, Users, X } from "lucide-react"

export default function AdminUsersPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '')

  // Función para actualizar la URL
  const updateURL = (searchValue: string) => {
    const params = new URLSearchParams(searchParams.toString())

    if (searchValue.trim()) {
      params.set('search', searchValue.trim())
      params.set('page', '1')
    } else {
      params.delete('search')
      params.delete('page')
    }

    // Reset to first page when searching
    console.log('updateURL params:', params.toString()) // Debug
    router.push(`/dashboard/admin/users?${params.toString()}`)
  }

  // Debounce la función de búsqueda
  const debouncedUpdateURL = useDebounce(updateURL, 500)

  // Efecto para aplicar la búsqueda cuando cambie el searchTerm
  useEffect(() => {
    // Solo ejecutar si el searchTerm es diferente al parámetro actual en la URL
    const currentSearch = searchParams.get('search') || ''
    if ((searchTerm !== currentSearch)) {
      debouncedUpdateURL(searchTerm)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm])

  const handleClearSearch = () => {
    setSearchTerm('')
    // remove queries like page and search
    router.push(`/dashboard/admin/users`)
  }

  const handleExport = () => {
    // TODO: Implementar exportación de usuarios
    console.log('Exportar usuarios')
  }

  return (
    <div className="space-y-8 p-6 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      {/* Header Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-lg">
              <Users className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Gestión de Usuarios
              </h1>
              <p className="text-gray-600 mt-1">
                Administra todos los usuarios de la plataforma, incluyendo roles, estados y sesiones
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={handleExport}
              className="border-gray-300 hover:border-blue-400 hover:text-blue-600 transition-colors"
            >
              <Download className="mr-2 h-4 w-4" />
              Exportar
            </Button>
            {/* Comentado por ahora - se puede implementar más adelante
            <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              <UserPlus className="mr-2 h-4 w-4" />
              Crear Usuario
            </Button>
            */}
          </div>
        </div>
      </div>

      {/* Estadísticas */}
      <UserStatsCards />

      {/* Filtros y búsqueda */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3 flex-1 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar por nombre o email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 border-gray-300 focus:border-blue-400 focus:ring-blue-400"
              />
            </div>
            {(searchTerm || searchParams.get('search')) && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleClearSearch}
                className="border-gray-300 hover:border-red-400 hover:text-red-600"
              >
                <X className="mr-1 h-4 w-4" />
                Limpiar
              </Button>
            )}
          </div>
          <div className="text-sm text-gray-500">
            {searchTerm && (
              <span>Buscando: &ldquo;{searchTerm}&rdquo;</span>
            )}
          </div>
        </div>
      </div>

      {/* Tabla de usuarios */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Lista de Usuarios</h2>
          <p className="text-sm text-gray-600 mt-1">Gestiona usuarios, roles y permisos</p>
        </div>
        <UserTable />
      </div>
    </div>
  )
}
