export interface Bank {
  value: string
  label: string
  code?: string // Código bancario oficial
}

export const MEXICAN_BANKS: Bank[] = [
  { value: "bbva", label: "BBVA México", code: "012" },
  { value: "banamex", label: "Citibanamex", code: "002" },
  { value: "santander", label: "Santander México", code: "014" },
  { value: "banorte", label: "Banorte", code: "072" },
  { value: "hsbc", label: "HSBC México", code: "021" },
  { value: "scotiabank", label: "Scotiabank México", code: "044" },
  { value: "inbursa", label: "Banco Inbursa", code: "036" },
  { value: "bajio", label: "Banco del Bajío", code: "030" },
  { value: "azteca", label: "Banco Azteca", code: "127" },
  { value: "afirme", label: "Banco Afirme", code: "062" },
  { value: "mifel", label: "Banco Mifel", code: "042" },
  { value: "invex", label: "Banco Invex", code: "059" },
  { value: "ve_por_mas", label: "Banco Ve Por Más", code: "113" },
  { value: "multiva", label: "Banco Multiva", code: "132" },
  { value: "famsa", label: "Banco Famsa", code: "138" },
  { value: "autofin", label: "Banco Autofin", code: "128" },
  { value: "compartamos", label: "Banco Compartamos", code: "130" },
  { value: "bancoppel", label: "BanCoppel", code: "137" },
  { value: "banco_walmart", label: "Banco Walmart", code: "160" },
  { value: "consubanco", label: "Consubanco", code: "140" },
  { value: "banco_covalto", label: "Banco Covalto", code: "141" },
  { value: "pagatodo", label: "PagaTodo", code: "143" },
  { value: "banco_finterra", label: "Banco Finterra", code: "144" },
  { value: "nu_mexico", label: "Nu México", code: "638" },
  { value: "klar", label: "Klar", code: "661" },
  { value: "stori", label: "Stori Card", code: "901" },
  { value: "mercado_pago", label: "Mercado Pago", code: "902" },
  { value: "rappi_pay", label: "RappiPay", code: "903" },
  { value: "otro", label: "Otro banco", code: "999" }
]

export const getBankByValue = (value: string): Bank | undefined => {
  return MEXICAN_BANKS.find(bank => bank.value === value)
}

export const getBankByCode = (code: string): Bank | undefined => {
  return MEXICAN_BANKS.find(bank => bank.code === code)
}
