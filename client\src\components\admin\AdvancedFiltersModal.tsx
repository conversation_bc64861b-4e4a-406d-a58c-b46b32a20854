"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AdminDateRangeModal } from "@/components/admin/AdminDateRangeModal"
// import { DateRange } from "react-date-range"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { Calendar, X } from "lucide-react"

export interface AdvancedFilters {
  dateRange?: { from: Date; to: Date }
  minAmount?: number
  maxAmount?: number
  hostId?: string
  transferStatus?: string
}

interface AdvancedFiltersModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  filters: AdvancedFilters
  onFiltersChange: (filters: AdvancedFilters) => void
  hosts?: Array<{ id: string; name: string; email: string }>
}

export function AdvancedFiltersModal({
  open,
  onOpenChange,
  filters,
  onFiltersChange,
  hosts = []
}: AdvancedFiltersModalProps) {
  const [localFilters, setLocalFilters] = useState<AdvancedFilters>(filters)
  const [showDatePicker, setShowDatePicker] = useState(false)

  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
    onOpenChange(false)
  }

  const handleClearFilters = () => {
    const clearedFilters: AdvancedFilters = {}
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
    onOpenChange(false)
  }

  const handleDateRangeSelect = (ranges: any) => {
    const { startDate, endDate } = ranges.selection
    if (startDate && endDate) {
      setLocalFilters(prev => ({
        ...prev,
        dateRange: { from: startDate, to: endDate }
      }))
    }
  }

  const formatDateRange = () => {
    if (!localFilters.dateRange?.from || !localFilters.dateRange?.to) {
      return "Seleccionar rango de fechas"
    }
    return `${format(localFilters.dateRange.from, "dd/MM/yyyy", { locale: es })} - ${format(localFilters.dateRange.to, "dd/MM/yyyy", { locale: es })}`
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Filtros Avanzados</DialogTitle>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Rango de Fechas */}
            <div className="space-y-2">
              <Label>Rango de Fechas</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                  onClick={() => setShowDatePicker(true)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {formatDateRange()}
                </Button>
                {localFilters.dateRange && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setLocalFilters(prev => ({ ...prev, dateRange: undefined }))}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* Rango de Montos */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minAmount">Monto Mínimo</Label>
                <Input
                  id="minAmount"
                  type="number"
                  placeholder="$0"
                  value={localFilters.minAmount || ""}
                  onChange={(e) => setLocalFilters(prev => ({
                    ...prev,
                    minAmount: e.target.value ? Number(e.target.value) : undefined
                  }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxAmount">Monto Máximo</Label>
                <Input
                  id="maxAmount"
                  type="number"
                  placeholder="$999,999"
                  value={localFilters.maxAmount || ""}
                  onChange={(e) => setLocalFilters(prev => ({
                    ...prev,
                    maxAmount: e.target.value ? Number(e.target.value) : undefined
                  }))}
                />
              </div>
            </div>

            {/* Filtro por Host */}
            <div className="space-y-2">
              <Label>Host</Label>
              <Select
                value={localFilters.hostId || "all"}
                onValueChange={(value) => setLocalFilters(prev => ({
                  ...prev,
                  hostId: value === "all" ? undefined : value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar host" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los hosts</SelectItem>
                  {hosts.map((host) => (
                    <SelectItem key={host.id} value={host.id}>
                      {host.name} ({host.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Estado de Transferencia */}
            <div className="space-y-2">
              <Label>Estatus de Transferencia</Label>
              <Select
                value={localFilters.transferStatus || "all"}
                onValueChange={(value) => setLocalFilters(prev => ({
                  ...prev,
                  transferStatus: value === "all" ? undefined : value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estatus</SelectItem>
                  <SelectItem value="pending">Pendiente de transferir</SelectItem>
                  <SelectItem value="transferred">Ya transferido</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={handleClearFilters}>
              Limpiar Filtros
            </Button>
            <Button onClick={handleApplyFilters}>
              Aplicar Filtros
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Selección de Fechas */}
      <AdminDateRangeModal
        open={showDatePicker}
        onOpenChange={setShowDatePicker}
        onDateRangeSelect={handleDateRangeSelect}
        initialRange={localFilters.dateRange ? {
          startDate: localFilters.dateRange.from,
          endDate: localFilters.dateRange.to,
          key: 'selection'
        } : undefined}
      />
    </>
  )
}
