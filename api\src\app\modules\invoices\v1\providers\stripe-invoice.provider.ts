import Stripe from 'stripe';
import { InvoiceProviderService, CreateInvoiceInput, ExternalInvoiceResponse } from './invoice-provider.service';

export class StripeInvoiceProvider extends InvoiceProviderService {
  private stripe: Stripe;

  constructor() {
    super();
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2024-06-20'
    });
  }

  async createInvoice(data: CreateInvoiceInput): Promise<ExternalInvoiceResponse> {
    try {
      // 1. Crear o obtener cliente en Stripe
      const customer = await this.createOrGetCustomer(data.customerEmail, data.customerName);

      // 2. Crear productos/precios para cada item
      const lineItems = await Promise.all(
        data.items.map(async (item) => {
          const product = await this.stripe.products.create({
            name: item.description,
            metadata: {
              productCode: item.productCode || '',
              unitCode: item.unitCode || ''
            }
          });

          const price = await this.stripe.prices.create({
            product: product.id,
            unit_amount: Math.round(item.unitPrice * 100), // Convertir a centavos
            currency: data.currency || 'mxn'
          });

          return {
            price: price.id,
            quantity: item.quantity
          };
        })
      );

      // 3. Crear factura en Stripe
      const invoice = await this.stripe.invoices.create({
        customer: customer.id,
        collection_method: 'send_invoice',
        days_until_due: 30,
        metadata: {
          invoiceNumber: data.invoiceNumber,
          customerTaxId: data.customerTaxId || '',
          source: 'autoop'
        }
      });

      // 4. Agregar items a la factura
      for (const lineItem of lineItems) {
        await this.stripe.invoiceItems.create({
          customer: customer.id,
          invoice: invoice.id,
          price: lineItem.price,
          quantity: lineItem.quantity
        });
      }

      // 5. Finalizar factura
      const finalizedInvoice = await this.stripe.invoices.finalizeInvoice(invoice.id);

      return {
        id: finalizedInvoice.id,
        invoiceNumber: data.invoiceNumber,
        status: this.mapStripeStatus(finalizedInvoice.status),
        pdfUrl: finalizedInvoice.invoice_pdf,
        publicUrl: finalizedInvoice.hosted_invoice_url,
        metadata: {
          stripeInvoiceId: finalizedInvoice.id,
          stripeCustomerId: customer.id
        }
      };

    } catch (error) {
      console.error('Error creating Stripe invoice:', error);
      throw new Error(`Error al crear factura en Stripe: ${error.message}`);
    }
  }

  async getInvoice(externalId: string): Promise<ExternalInvoiceResponse> {
    try {
      const invoice = await this.stripe.invoices.retrieve(externalId);

      return {
        id: invoice.id,
        invoiceNumber: invoice.metadata?.invoiceNumber || invoice.number || '',
        status: this.mapStripeStatus(invoice.status),
        pdfUrl: invoice.invoice_pdf,
        publicUrl: invoice.hosted_invoice_url,
        metadata: {
          stripeInvoiceId: invoice.id,
          stripeCustomerId: invoice.customer as string
        }
      };

    } catch (error) {
      console.error('Error getting Stripe invoice:', error);
      throw new Error(`Error al obtener factura de Stripe: ${error.message}`);
    }
  }

  async cancelInvoice(externalId: string): Promise<void> {
    try {
      await this.stripe.invoices.voidInvoice(externalId);
    } catch (error) {
      console.error('Error canceling Stripe invoice:', error);
      throw new Error(`Error al cancelar factura en Stripe: ${error.message}`);
    }
  }

  async sendInvoice(externalId: string, email: string): Promise<void> {
    try {
      await this.stripe.invoices.sendInvoice(externalId);
    } catch (error) {
      console.error('Error sending Stripe invoice:', error);
      throw new Error(`Error al enviar factura de Stripe: ${error.message}`);
    }
  }

  async getPdfUrl(externalId: string): Promise<string> {
    try {
      const invoice = await this.stripe.invoices.retrieve(externalId);
      return invoice.invoice_pdf || '';
    } catch (error) {
      console.error('Error getting Stripe invoice PDF:', error);
      throw new Error(`Error al obtener PDF de factura de Stripe: ${error.message}`);
    }
  }

  validateConfig(config: any): boolean {
    return !!(config.secretKey && config.publishableKey);
  }

  getProviderName(): string {
    return 'stripe';
  }

  /**
   * Crear o obtener cliente en Stripe
   */
  private async createOrGetCustomer(email: string, name: string): Promise<Stripe.Customer> {
    // Buscar cliente existente
    const existingCustomers = await this.stripe.customers.list({
      email: email,
      limit: 1
    });

    if (existingCustomers.data.length > 0) {
      return existingCustomers.data[0];
    }

    // Crear nuevo cliente
    return await this.stripe.customers.create({
      email: email,
      name: name
    });
  }

  /**
   * Mapear estados de Stripe a estados internos
   */
  private mapStripeStatus(stripeStatus: string | null): string {
    switch (stripeStatus) {
      case 'draft':
        return 'draft';
      case 'open':
        return 'sent';
      case 'paid':
        return 'paid';
      case 'void':
        return 'cancelled';
      case 'uncollectible':
        return 'cancelled';
      default:
        return 'draft';
    }
  }
}
