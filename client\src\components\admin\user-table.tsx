"use client"

import { useState } from "react"
import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import toast from "react-hot-toast"
import Swal from "sweetalert2"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { DataTable } from "@/components/data-table/data-table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  MoreHorizontal,
  // Shield, 
  // ShieldOff, 
  UserX,
  UserCheck,
  Trash2,
  Eye,
  UserCog,
  LogOut
} from "lucide-react"
import { authClient } from "@/auth-client"
import { UserSessionsModal } from "./user-sessions-modal"
import { usersApi } from '@/lib/api/users.api'

interface User {
  id: string
  name: string
  email: string
  role: string
  banned: boolean
  emailVerified: boolean
  userType?: string
  mainUserType?: string
  createdAt: string
  updatedAt: string
}

export function UserTable() {
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const router = useRouter()
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [showSessionsModal, setShowSessionsModal] = useState(false)

  // Obtener parámetros de paginación
  const page = Number(searchParams.get('page') || 1)
  const limit = Number(searchParams.get('limit') || 10)
  const search = searchParams.get('search') || ''

  // Query para obtener usuarios
  const { data, isLoading, error } = useQuery({
    queryKey: ['admin-users', page, limit, search],
    queryFn: async () => {
      const offset = (page - 1) * limit

      // Configurar búsqueda si hay término de búsqueda
      const queryConfig: any = {
        limit,
        offset
      }

      if (search && search.trim()) {
        // Buscar por email si contiene @, sino por nombre
        if (search.includes('@')) {
          queryConfig.searchField = "email"
        } else {
          queryConfig.searchField = "name"
        }
        queryConfig.searchOperator = "contains"
        queryConfig.searchValue = search.trim()
      }

      // return await authClient.admin.listUsers({
      //   query: queryConfig
      // })

      return await usersApi.getAllUsersWithPagination({
        page,
        limit,
        search
      })

    },
    // staleTime: 60 * 1000, // 1 minuto
  })

  // Mutación para cambiar rol
  const changeRoleMutation = useMutation({
    mutationFn: async ({ userId, role }: { userId: string; role: string }) => {
      return await authClient.admin.setRole({
        userId,
        role
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
      toast.success("Rol cambiado correctamente")
    },
    onError: (error: any) => {
      toast.error(`Error al cambiar rol: ${error.message}`)
    }
  })

  // Mutación para banear usuario
  const banUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      return await authClient.admin.banUser({
        userId,
        banReason: "Baneado por administrador"
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
      toast.success("Usuario baneado correctamente")
    },
    onError: (error: any) => {
      toast.error(`Error al banear usuario: ${error.message}`)
    }
  })

  // Mutación para desbanear usuario
  const unbanUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      return await authClient.admin.unbanUser({
        userId
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
      toast.success("Usuario desbaneado correctamente")
    },
    onError: (error: any) => {
      toast.error(`Error al desbanear usuario: ${error.message}`)
    }
  })

  // Mutación para eliminar usuario
  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      return await authClient.admin.removeUser({
        userId
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-users'] })
      toast.success("Usuario eliminado correctamente")
    },
    onError: (error: any) => {
      toast.error(`Error al eliminar usuario: ${error.message}`)
    }
  })

  // Mutación para impersonar usuario
  const impersonateMutation = useMutation({
    mutationFn: async (userId: string) => {
      return await authClient.admin.impersonateUser({
        userId
      })
    },
    onSuccess: () => {
      toast.success("Impersonación iniciada correctamente")
      // Recargar la página para aplicar la nueva sesión
      window.location.reload()
    },
    onError: (error: any) => {
      toast.error(`Error al impersonar usuario: ${error.message}`)
    }
  })

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', (newPage + 1).toString())
    router.push(`/dashboard/admin/users?${params.toString()}`)
  }

  // Manejar cambio de rol con confirmación
  const handleChangeRole = (user: User) => {
    const newRole = user.role === 'admin' ? 'user' : 'admin'

    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas cambiar el rol de ${user.name} a ${newRole}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, cambiar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        changeRoleMutation.mutate({ userId: user.id, role: newRole })
      }
    })
  }

  // Manejar baneo con confirmación
  const handleBanUser = (user: User) => {
    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas banear a ${user.name}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, banear',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        banUserMutation.mutate(user.id)
      }
    })
  }

  // Manejar desbaneo con confirmación
  const handleUnbanUser = (user: User) => {
    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas desbanear a ${user.name}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, desbanear',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        unbanUserMutation.mutate(user.id)
      }
    })
  }

  // Manejar eliminación con confirmación
  const handleDeleteUser = (user: User) => {
    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas eliminar permanentemente a ${user.name}? Esta acción no se puede deshacer.`,
      icon: 'error',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        deleteUserMutation.mutate(user.id)
      }
    })
  }

  // Manejar impersonación con confirmación
  const handleImpersonateUser = (user: User) => {
    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas impersonar a ${user.name}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, impersonar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        impersonateMutation.mutate(user.id)
      }
    })
  }

  // Manejar ver sesiones
  const handleViewSessions = (userId: string) => {
    setSelectedUserId(userId)
    setShowSessionsModal(true)
  }

  const columns = [
    {
      accessorKey: "name",
      header: "Nombre",
      cell: ({ row }: any) => <div className="font-medium">{row.original.name}</div>,
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }: any) => <div>{row.original.email}</div>,
    },
    {
      accessorKey: "role",
      header: "Rol",
      cell: ({ row }: any) => (
        <Badge variant={row.original.role === 'admin' ? 'default' : 'secondary'}>
          {row.original.role === 'admin' ? 'Administrador' : 'Usuario'}
        </Badge>
      ),
    },
    {
      accessorKey: "userType",
      header: "Tipo",
      cell: ({ row }: any) => {
        const userType = row.original.mainUserType || row.original.userType
        if (!userType) return <span className="text-muted-foreground">-</span>
        return (
          <Badge variant="outline">
            {userType === 'host' ? 'Anfitrión' : userType === 'client' ? 'Cliente' : userType}
          </Badge>
        )
      },
    },
    {
      accessorKey: "banned",
      header: "Estado",
      cell: ({ row }: any) => (
        <Badge variant={row.original.banned ? 'destructive' : 'default'}>
          {row.original.banned ? 'Baneado' : 'Activo'}
        </Badge>
      ),
    },
    {
      accessorKey: "emailVerified",
      header: "Email Verificado",
      cell: ({ row }: any) => (
        <Badge variant={row.original.emailVerified ? 'default' : 'secondary'}>
          {row.original.emailVerified ? 'Sí' : 'No'}
        </Badge>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Fecha de Registro",
      cell: ({ row }: any) => {
        const date = new Date(row.original.createdAt)
        return <div>{date.toLocaleDateString('es-ES')}</div>
      },
    }
  ]

  if (error) {
    return <div className="text-center text-red-500">Error al cargar usuarios</div>
  }

  return (
    <>
      <DataTable
        columns={[
          ...columns,
          {
            id: "actions",
            header: "Acciones",
            cell: ({ row }: any) => {
              const user = row.original as User
              return (
                <div className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Abrir menú</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleChangeRole(user)}>
                        <UserCog className="mr-2 h-4 w-4" />
                        Cambiar rol ({user.role === 'admin' ? 'a usuario' : 'a admin'})
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      {user.banned ? (
                        <DropdownMenuItem onClick={() => handleUnbanUser(user)}>
                          <UserCheck className="mr-2 h-4 w-4" />
                          Desbanear usuario
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem onClick={() => handleBanUser(user)}>
                          <UserX className="mr-2 h-4 w-4" />
                          Banear usuario
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuItem onClick={() => handleViewSessions(user.id)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Ver sesiones
                      </DropdownMenuItem>

                      <DropdownMenuItem onClick={() => handleImpersonateUser(user)}>
                        <LogOut className="mr-2 h-4 w-4" />
                        Impersonar usuario
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteUser(user)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Eliminar usuario
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )
            },
          },
        ]}
        data={data?.data || []}
        rowCount={data?.pagination?.total || 0}
        pageSize={limit}
        pageIndex={page - 1}
        onPageChange={handlePageChange}
        isLoading={isLoading}
      />

      {selectedUserId && (
        <UserSessionsModal
          userId={selectedUserId}
          isOpen={showSessionsModal}
          onClose={() => {
            setShowSessionsModal(false)
            setSelectedUserId(null)
          }}
        />
      )}
    </>
  )
}
