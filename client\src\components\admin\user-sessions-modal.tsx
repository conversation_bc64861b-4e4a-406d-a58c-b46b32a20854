"use client"

import { useEffect, useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import toast from "react-hot-toast"
import Swal from "sweetalert2"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Trash2, Shield, AlertTriangle } from "lucide-react"
import { authClient } from "@/auth-client"

// interface Session {
//   id: string
//   token: string
//   expiresAt: string
//   ipAddress?: string
//   userAgent?: string
//   impersonatedBy?: string
//   createdAt: string
//   updatedAt: string
// }

interface UserSessionsModalProps {
  userId: string
  isOpen: boolean
  onClose: () => void
}

export function UserSessionsModal({ userId, isOpen, onClose }: UserSessionsModalProps) {
  const queryClient = useQueryClient()
  const [isSwalOpen, setIsSwalOpen] = useState(false)

  // Agregar CSS personalizado para el z-index de SweetAlert2
  useEffect(() => {
    // Crear el estilo si no existe
    if (!document.getElementById('swal2-z-index-fix')) {
      const style = document.createElement('style')
      style.id = 'swal2-z-index-fix'
      style.textContent = `
        .swal2-container-higher-z-index {
          z-index: 999999 !important;
          position: fixed !important;
        }
        .swal2-container-higher-z-index .swal2-popup {
          z-index: 999999 !important;
          position: relative !important;
        }
        .swal2-container-higher-z-index .swal2-backdrop-show {
          z-index: 999998 !important;
        }
        .swal2-container-higher-z-index .swal2-actions {
          z-index: 999999 !important;
          position: relative !important;
        }
        .swal2-container-higher-z-index .swal2-actions button {
          z-index: 999999 !important;
          position: relative !important;
          pointer-events: auto !important;
        }
        .swal2-popup-higher-z-index {
          z-index: 999999 !important;
          position: relative !important;
        }
        .swal2-actions-higher-z-index {
          z-index: 999999 !important;
          position: relative !important;
        }
        .swal2-confirm-higher-z-index,
        .swal2-cancel-higher-z-index {
          z-index: 999999 !important;
          position: relative !important;
          pointer-events: auto !important;
        }
        /* Deshabilitar pointer events del modal cuando SweetAlert está activo */
        .modal-disabled-for-swal {
          pointer-events: none !important;
        }
        .modal-disabled-for-swal .swal2-container-higher-z-index {
          pointer-events: auto !important;
        }
        .modal-disabled-for-swal .swal2-container-higher-z-index * {
          pointer-events: auto !important;
        }
        /* Deshabilitar el overlay/backdrop del modal de Radix UI */
        .modal-disabled-for-swal [data-radix-popper-content-wrapper],
        .modal-disabled-for-swal [data-radix-dialog-overlay] {
          pointer-events: none !important;
        }
        /* Asegurar que el contenido del modal también esté deshabilitado */
        .modal-disabled-for-swal [data-radix-dialog-content] {
          pointer-events: none !important;
        }
      `
      document.head.appendChild(style)
    }

    // Limpiar el estilo cuando el componente se desmonte
    return () => {
      const style = document.getElementById('swal2-z-index-fix')
      if (style) {
        style.remove()
      }
    }
  }, [])

  // Query para obtener sesiones del usuario
  const { data: sessions, isLoading, error } = useQuery({
    queryKey: ['user-sessions', userId],
    queryFn: async () => {
      const result = await authClient.admin.listUserSessions({
        userId
      })
      if (result.error) {
        throw new Error(result.error.message || 'Error al obtener sesiones')
      }
      return result.data.sessions
    },
    enabled: isOpen && !!userId,
    staleTime: 30 * 1000, // 30 segundos
  })

  // Mutación para revocar sesión específica
  const revokeSessionMutation = useMutation({
    mutationFn: async (sessionToken: string) => {
      return await authClient.admin.revokeUserSession({
        sessionToken
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-sessions', userId] })
      toast.success("Sesión revocada correctamente")
    },
    onError: (error: any) => {
      toast.error(`Error al revocar sesión: ${error.message}`)
    }
  })

  // Mutación para revocar todas las sesiones
  const revokeAllSessionsMutation = useMutation({
    mutationFn: async () => {
      return await authClient.admin.revokeUserSessions({
        userId
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-sessions', userId] })
      toast.success("Todas las sesiones han sido revocadas")
    },
    onError: (error: any) => {
      toast.error(`Error al revocar sesiones: ${error.message}`)
    }
  })

  // Manejar revocación de sesión específica con confirmación
  const handleRevokeSession = (session: any) => {
    // Deshabilitar eventos del modal
    setIsSwalOpen(true)
    const modalElement = document.querySelector('[role="dialog"]');
    const overlayElement = document.querySelector('[data-radix-dialog-overlay]');

    if (modalElement) {
      modalElement.classList.add('modal-disabled-for-swal');
    }
    if (overlayElement) {
      overlayElement.classList.add('modal-disabled-for-swal');
    }

    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas revocar esta sesión?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, revocar',
      cancelButtonText: 'Cancelar',
      // Asegurar que SweetAlert2 aparezca por encima del modal
      backdrop: `rgba(0,0,0,0.4)`,
      allowOutsideClick: false,
      // Aumentar el z-index para que esté por encima del modal
      customClass: {
        container: 'swal2-container-higher-z-index',
        popup: 'swal2-popup-higher-z-index',
        actions: 'swal2-actions-higher-z-index',
        confirmButton: 'swal2-confirm-higher-z-index',
        cancelButton: 'swal2-cancel-higher-z-index'
      }
    }).then((result) => {
      // Restaurar eventos del modal
      setIsSwalOpen(false)
      const modalElement = document.querySelector('[role="dialog"]');
      const overlayElement = document.querySelector('[data-radix-dialog-overlay]');

      if (modalElement) {
        modalElement.classList.remove('modal-disabled-for-swal');
      }
      if (overlayElement) {
        overlayElement.classList.remove('modal-disabled-for-swal');
      }

      if (result.isConfirmed) {
        revokeSessionMutation.mutate(session.token)
      }
    }).catch(() => {
      // Asegurar que se restauren los eventos en caso de error
      setIsSwalOpen(false)
      const modalElement = document.querySelector('[role="dialog"]');
      const overlayElement = document.querySelector('[data-radix-dialog-overlay]');

      if (modalElement) {
        modalElement.classList.remove('modal-disabled-for-swal');
      }
      if (overlayElement) {
        overlayElement.classList.remove('modal-disabled-for-swal');
      }
    })
  }

  // Manejar revocación de todas las sesiones con confirmación
  const handleRevokeAllSessions = () => {
    if (!sessions || sessions.length === 0) {
      // toast.info("No hay sesiones activas para revocar")
      toast.custom((t) => (
        <div
          className="flex items-center justify-center w-96 h-48 bg-white border border-gray-200 rounded-lg shadow-lg"
          onClick={() => toast.dismiss(t.id)}
        >
          <div className="flex flex-col items-center justify-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
            <h2 className="text-lg font-semibold">No hay sesiones activas</h2>
            <p className="text-sm text-muted-foreground">No hay sesiones activas para revocar</p>
          </div>
        </div>
      ))
      return
    }

    // Deshabilitar eventos del modal
    setIsSwalOpen(true)
    const modalElement = document.querySelector('[role="dialog"]');
    const overlayElement = document.querySelector('[data-radix-dialog-overlay]');

    if (modalElement) {
      modalElement.classList.add('modal-disabled-for-swal');
    }
    if (overlayElement) {
      overlayElement.classList.add('modal-disabled-for-swal');
    }

    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas revocar todas las ${sessions.length} sesiones activas de este usuario?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, revocar todas',
      cancelButtonText: 'Cancelar',
      // Asegurar que SweetAlert2 aparezca por encima del modal
      backdrop: `rgba(0,0,0,0.4)`,
      allowOutsideClick: false,
      // Aumentar el z-index para que esté por encima del modal
      customClass: {
        container: 'swal2-container-higher-z-index',
        popup: 'swal2-popup-higher-z-index',
        actions: 'swal2-actions-higher-z-index',
        confirmButton: 'swal2-confirm-higher-z-index',
        cancelButton: 'swal2-cancel-higher-z-index'
      }
    }).then((result) => {
      // Restaurar eventos del modal
      setIsSwalOpen(false)
      const modalElement = document.querySelector('[role="dialog"]');
      const overlayElement = document.querySelector('[data-radix-dialog-overlay]');

      if (modalElement) {
        modalElement.classList.remove('modal-disabled-for-swal');
      }
      if (overlayElement) {
        overlayElement.classList.remove('modal-disabled-for-swal');
      }

      if (result.isConfirmed) {
        revokeAllSessionsMutation.mutate()
      }
    }).catch(() => {
      // Asegurar que se restauren los eventos en caso de error
      setIsSwalOpen(false)
      const modalElement = document.querySelector('[role="dialog"]');
      const overlayElement = document.querySelector('[data-radix-dialog-overlay]');

      if (modalElement) {
        modalElement.classList.remove('modal-disabled-for-swal');
      }
      if (overlayElement) {
        overlayElement.classList.remove('modal-disabled-for-swal');
      }
    })
  }

  // Función para formatear fecha
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Función para obtener información del navegador desde userAgent
  const getBrowserInfo = (userAgent?: string) => {
    if (!userAgent) return 'Desconocido'

    if (userAgent.includes('Chrome')) return 'Chrome'
    if (userAgent.includes('Firefox')) return 'Firefox'
    if (userAgent.includes('Safari')) return 'Safari'
    if (userAgent.includes('Edge')) return 'Edge'

    return 'Otro'
  }

  // Función para verificar si la sesión está expirada
  const isSessionExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date()
  }

  // Función para manejar el evento de keydown y prevenir que Escape cierre el SweetAlert
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Si SweetAlert está abierto, prevenir que Escape cierre el modal
    if (e.key === 'Escape' && (isSwalOpen || document.querySelector('.swal2-container'))) {
      e.stopPropagation();
      e.preventDefault();
    }
  }

  // Función para manejar el cierre del modal
  const handleModalClose = () => {
    // Solo permitir cerrar el modal si SweetAlert no está abierto
    if (!isSwalOpen && !document.querySelector('.swal2-container')) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleModalClose}>
      <DialogContent
        className="max-w-6xl max-h-[85vh] flex flex-col"
        onKeyDown={handleKeyDown}>
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Sesiones del Usuario
          </DialogTitle>
          <DialogDescription>
            Gestiona las sesiones activas del usuario. Puedes revocar sesiones individuales o todas a la vez.
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-4 flex-1 min-h-0">
          {/* Botón para revocar todas las sesiones */}
          <div className="flex justify-between items-center flex-shrink-0">
            <div className="text-sm text-muted-foreground">
              {sessions ? `${sessions.length} sesión(es) encontrada(s)` : 'Cargando...'}
            </div>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleRevokeAllSessions}
              disabled={!sessions || sessions.length === 0 || revokeAllSessionsMutation.isPending}
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              Revocar Todas las Sesiones
            </Button>
          </div>

          {/* Tabla de sesiones */}
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-muted-foreground">Cargando sesiones...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              Error al cargar las sesiones
            </div>
          ) : !sessions || sessions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No hay sesiones activas para este usuario
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <div className="overflow-x-auto overflow-y-auto max-h-[400px]">
                <Table>
                  <TableHeader className="sticky top-0 bg-white z-10">
                    <TableRow>
                      <TableHead className="min-w-[100px]">Estado</TableHead>
                      <TableHead className="min-w-[150px]">Navegador</TableHead>
                      <TableHead className="min-w-[120px]">IP</TableHead>
                      <TableHead className="min-w-[140px]">Creada</TableHead>
                      <TableHead className="min-w-[140px]">Expira</TableHead>
                      <TableHead className="text-right min-w-[100px]">Acciones</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>
                          <Badge
                            variant={isSessionExpired(session.expiresAt.toString()) ? 'secondary' : 'default'}
                          >
                            {isSessionExpired(session.expiresAt.toString()) ? 'Expirada' : 'Activa'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {getBrowserInfo(session.userAgent || '')}
                          </div>
                          {session.userAgent && (
                            <div className="text-xs text-muted-foreground truncate max-w-[150px]">
                              {session.userAgent}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm font-mono">
                            {session.ipAddress || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDate(session.createdAt.toString())}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDate(session.expiresAt.toString())}
                          </div>
                        </TableCell>

                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRevokeSession(session)}
                            disabled={revokeSessionMutation.isPending || isSessionExpired(session.expiresAt.toString())}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end flex-shrink-0 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cerrar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
