import { Elysia, t } from "elysia";
import { VehiclesService } from "./vehicles.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { VehicleAvailabilityService } from './availability.service';
import { checkAdmin } from '@/lib/check-admin';
import { availabilityUpdateSchema, createVehicleDto, rejectSchema, statusSchema, updateFilesDto, uploadFilesDto, vehicleSchema } from './dto/vehicle.dto';
import { HttpException } from '@/exceptions/HttpExceptions';

// Controlador público de vehículos
export const publicVehiclesController = new Elysia({ prefix: '/vehicles' })
  .get('/', async ({ query, request }) => {
    // Manejar amenities que pueden venir como array o string único
    let amenities: string[] | undefined;
    if (query.amenities) {
      if (Array.isArray(query.amenities)) {
        amenities = query.amenities;
      } else {
        amenities = [query.amenities];
      }
    }

    const vehicles = await VehiclesService.getAllPublic({
      query: {
        page: query.page!,
        limit: query.limit!,
        location: query.location,
        state: query.state,
        startDate: query.startDate,
        endDate: query.endDate,
        passengers: query.passengers,
        bodyType: query.bodyType,
        priceMin: query.priceMin,
        priceMax: query.priceMax,
        transmission: query.transmission,
        amenities: amenities,
        search: query.search
      }
    });
    return vehicles;
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
      location: t.Optional(t.String()),
      state: t.Optional(t.String()),
      startDate: t.Optional(t.String()),
      endDate: t.Optional(t.String()),
      passengers: t.Optional(t.Number()),
      bodyType: t.Optional(t.String()),
      priceMin: t.Optional(t.Number()),
      priceMax: t.Optional(t.Number()),
      transmission: t.Optional(t.String()),
      amenities: t.Optional(t.Union([t.String(), t.Array(t.String())])),
      search: t.Optional(t.String())
    }))
  })
  .get('/:id', async ({ params }) => {
    console.log(`Getting vehicle with ID: ${params.id}`);
    return await VehiclesService.getByIdPublic(params.id);
  })
  .get('/:id/availability', async ({ params }) => {
    // console.log(`Getting availability for vehicle with ID: ${params.id}`);
    return await VehicleAvailabilityService.getByVehicleId(params.id);
  });

// Controlador para hosts
export const hostVehiclesController = new Elysia({ prefix: '/host/vehicles' })
  .use(authMiddleware)
  // Obtener todos los vehículos del host
  .get('/', async ({ user, query }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }
    return await VehiclesService.getByHostId(user.id, {
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })
  // Obtener un vehículo por ID (para el host)
  .get('/:id', async ({ params, user }) => {
    // if (user.userType !== 'host' && user.role !== 'admin') {
    //   return { error: 'Unauthorized', status: 403 };
    // }
    console.log(`Getting vehicle with ID: ${params.id}`);
    return await VehiclesService.getByIdForHost(params.id, user.id);
  })
  // Actualizar disponibilidad de un vehículo
  .put('/:id/availability',
    async ({ params, body, user }) => {
      if (user.userType !== 'host' && user.role !== 'admin') {
        return { error: 'Unauthorized', status: 403 };
      }
      console.log(`Updating availability for vehicle with ID: ${params.id}`);
      return await VehicleAvailabilityService.update(params.id, body);
    },
    {
      body: availabilityUpdateSchema
    }
  )
  // Eliminar un vehículo (host)
  .delete('/:id', async ({ params, user }) => {
    return await VehiclesService.delete(params.id, user.id, false);
  })
  // Actualizar estado de un vehículo (host)
  .patch('/:id/status',
    async ({ params, body, user }) => {
      return await VehiclesService.updateStatus(params.id, body.status, user.id, false);
    },
    {
      body: statusSchema
    }
)
  // Crear un vehículo
  .post('/',
    async ({ body, user }) => {
      // Verificar si el host está verificado
      if (!user.isHostVerified) {
        throw HttpException.Forbidden("Debes verificar tu identidad antes de poder registrar vehículos");
      }

      // Procesar y subir archivos
      // Crear el vehículo con las URLs de los archivos
      const vehicle = await VehiclesService.create({
        ...body,
      }, user.id);

      return vehicle;
    },
    {
      body: createVehicleDto
    }
  )
  // Subir archivos para un vehículo
  .patch('/:id/upload-files',
    async ({ params, body, user }) => {
      // console.log('Body:', body);
      if (user.userType !== 'host' && user.role !== 'admin') {
        return { error: 'Unauthorized', status: 403 };
      }
      console.log(`Uploading files for vehicle with ID: ${params.id}`);
      // return await VehiclesService.uploadFiles(params.id, body, user.id);
      return await VehiclesService.uploadFiles({
        vehicleId: params.id,
        files: body,
        userId: user.id,
      });
    },
    {
      body: uploadFilesDto
    }
  )

  // Actualizar archivos de un vehículo
  .patch('/:id/update-files',
    async ({ params, body, user }) => {
      if (user.userType !== 'host' && user.role !== 'admin') {
        return { error: 'Unauthorized', status: 403 };
      }

      // Extraer imágenes a eliminar del formData
      let imagesToRemove: string[] = [];
      if (body.imagesToRemove) {
        try {
          imagesToRemove = JSON.parse(body.imagesToRemove);
          delete body.imagesToRemove; // Eliminar del body para no procesarlo como archivo
        } catch (error) {
          console.error('Error parsing imagesToRemove:', error);
        }
      }

      console.log(`Updating files for vehicle with ID: ${params.id}`);
      return await VehiclesService.updateFiles({
        vehicleId: params.id,
        files: body,
        userId: user.id,
        imagesToRemove,
      });
    },
    {
      body: updateFilesDto
    }
  )
  // Obtener estadísticas de los vehículos del host
  .get('/stats', async ({ user }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }

    const { totalVehicles, activeVehicles, maintenanceVehicles, averageRating, averageIncome } = await VehiclesService.getStatsByHostId(user.id);

    return {
      totalVehicles,
      activeVehicles,
      maintenanceVehicles,
      averageRating,
      averageIncome
    };
  })
  // Actualizar un vehículo (host)
  .put('/:id',
    async ({ params, body, user }) => {
      if (user.userType !== 'host' && user.role !== 'admin') {
        throw HttpException.Forbidden("Unauthorized");
      }
      return await VehiclesService.update(params.id, body, user.id, false);
    },
    {
      body: vehicleSchema
    }
  )
  // Obtener documentos de un vehículo
  .get('/:id/documents',
    async ({ params, user }) => {
      if (user.userType !== 'host' && user.role !== 'admin') {
        throw HttpException.Forbidden("Unauthorized");
      }

      console.log(`Getting documents for vehicle with ID: ${params.id}`);
      return await VehiclesService.getVehicleDocuments({
        vehicleId: params.id,
        userId: user.id,
        isAdmin: user.role === 'admin'
      });
    }
  )
  // Solicitar una nueva revisión para un vehículo rechazado
  .post('/:id/request-review', async ({ params, user }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      throw HttpException.Forbidden("Unauthorized");
    }
    return await VehiclesService.requestReview(params.id, user.id);
  });

// Controlador para administradores
export const adminVehiclesController = new Elysia({ prefix: '/admin/vehicles' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/', async ({ query }) => {
    return await VehiclesService.getAllForAdmin({
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })
  .get('/:id', async ({ params }) => {
    return await VehiclesService.getByIdForAdmin(params.id);
  })
  .get('/pending', async () => {
    return await VehiclesService.getPendingApprovalVehicles();
  })
  .post('/:id/approve', async ({ params, user }) => {
    return await VehiclesService.approveVehicle(params.id, user.id);
  })
  .post('/:id/reject',
    async ({ params, body, user }) => {
      return await VehiclesService.rejectVehicle(params.id, body.reason, user.id);
    },
    {
      body: rejectSchema
    }
  )
  // Actualizar un vehículo (admin)
  .put('/:id',
    async ({ params, body, user }) => {
      return await VehiclesService.update(params.id, body, user.id, true);
    },
    {
      body: vehicleSchema
    }
  )
  .delete('/:id', async ({ params, user }) => {
    if (user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }
    return await VehiclesService.delete(params.id, user.id, true);
  })
  .patch('/:id/status',
    async ({ params, body, user }) => {
      if (user.role !== 'admin') {
        return { error: 'Unauthorized', status: 403 };
      }
      return await VehiclesService.updateStatus(params.id, body.status, user.id, true);
    },
    {
      body: statusSchema
    }
)
  // Nuevo endpoint para obtener estadísticas
  .get('/stats', async () => {
    // Obtener todos los vehículos para calcular estadísticas
    const stats = await VehiclesService.getStatsForAdmin();
    return stats;
  });

// Solicitar una nueva revisión para un vehículo rechazado
// app.post('/host/vehicles/:id/request-review', async ({ params, user }) => {
//   try {
//     const result = await VehiclesService.requestReview(params.id, user.id);
//     return {
//       success: true,
//       data: result
//     };
//   } catch (error) {
//     return handleApiError(error);
//   }
// });















