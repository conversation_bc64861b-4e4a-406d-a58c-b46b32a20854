
import { prisma } from "@/lib/prisma";
import { HttpException } from "@/exceptions/HttpExceptions";

export class UsersService {
  // Obtener todos los usuarios (admin)
  static async getAllUsers() {
    return await prisma.user.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  // Obtener un usuario por ID (admin)
  static async getUserById(id: string) {
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    return user;
  }

  static async getAllHosts({ query }: { query: { page: number; limit: number } }) {
    const skip = (query.page - 1) * query.limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: {
          mainUserType: 'host',  // Usar mainUserType en lugar de userType
          NOT: {
            role: 'admin'
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: query.limit,
      }),
      prisma.user.count({
        where: {
          mainUserType: 'host',
          NOT: {
            role: 'admin'
          }
        }
      })
    ]);

    return {
      data: users,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
      }
    };
  }

  static async getAllClients({ query }: { query: { page: number; limit: number } }) {
    const skip = (query.page - 1) * query.limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: {
          mainUserType: 'client',  // Usar mainUserType en lugar de userType
          NOT: {
            role: 'admin'
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: query.limit,
      }),
      prisma.user.count({
        where: {
          mainUserType: 'client',
          NOT: {
            role: 'admin'
          }
        }
      })
    ]);

    return {
      data: users,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
      }
    };
  }

  static async verifyUser(id: string) {
    return await prisma.user.update({
      where: { id },
      data: { emailVerified: true }
    });
  }

  // Obtener todos los usuarios con paginación (admin)
  static async getAllUsersWithPagination({ query }: { query: { page: number; limit: number; search?: string } }) {
    const skip = (query.page - 1) * query.limit;

    const whereClause = {
      ...(query.search && {
        OR: [
          { name: { contains: query.search, mode: 'insensitive' as const } },
          { email: { contains: query.search, mode: 'insensitive' as const } }
        ]
      })
    };

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        include: {
          _count: {
            select: {
              sessions: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: query.limit,
      }),
      prisma.user.count({
        where: whereClause
      })
    ]);

    return {
      data: users,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
      }
    };
  }

  // Cambiar rol de usuario (admin)
  static async changeUserRole(id: string, role: 'user' | 'admin') {
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    return await prisma.user.update({
      where: { id },
      data: { role }
    });
  }

  // Banear usuario (admin)
  static async banUser(id: string) {
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    if (user.banned) {
      throw HttpException.BadRequest("User is already banned");
    }

    return await prisma.user.update({
      where: { id },
      data: { banned: true }
    });
  }

  // Desbanear usuario (admin)
  static async unbanUser(id: string) {
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    if (!user.banned) {
      throw HttpException.BadRequest("User is not banned");
    }

    return await prisma.user.update({
      where: { id },
      data: { banned: false }
    });
  }

  // Obtener sesiones de usuario (admin)
  static async getUserSessions(id: string) {
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    return await prisma.session.findMany({
      where: { userId: id },
      orderBy: { createdAt: 'desc' }
    });
  }

  // Revocar sesión específica (admin)
  static async revokeUserSession(userId: string, sessionId: string) {
    const session = await prisma.session.findFirst({
      where: {
        id: sessionId,
        userId: userId
      }
    });

    if (!session) {
      throw HttpException.NotFound("Session not found");
    }

    await prisma.session.delete({
      where: { id: sessionId }
    });

    return { success: true };
  }

  // Revocar todas las sesiones de un usuario (admin)
  static async revokeAllUserSessions(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    const deletedSessions = await prisma.session.deleteMany({
      where: { userId: userId }
    });

    return {
      success: true,
      deletedCount: deletedSessions.count
    };
  }

  // Eliminar usuario (admin)
  static async deleteUser(id: string) {
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    // Eliminar usuario (las relaciones se eliminan en cascada)
    await prisma.user.delete({
      where: { id }
    });

    return { success: true };
  }

  // Obtener estadísticas de usuarios (admin)
  static async getUserStats() {
    const [
      totalUsers,
      totalAdmins,
      totalBanned,
      totalActiveSessions
    ] = await Promise.all([
      // Total de usuarios
      prisma.user.count(),

      // Total de administradores
      prisma.user.count({
        where: { role: 'admin' }
      }),

      // Total de usuarios baneados
      prisma.user.count({
        where: { banned: true }
      }),

      // Total de sesiones activas (no expiradas)
      prisma.session.count({
        where: {
          expiresAt: {
            gt: new Date()
          }
        }
      })
    ]);

    return {
      totalUsers,
      totalAdmins,
      totalBanned,
      totalActiveSessions
    };
  }

}