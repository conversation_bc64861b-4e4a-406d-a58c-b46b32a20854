import { prisma } from "@/lib/prisma";
import { HttpException } from "@/exceptions/HttpExceptions";
import Strip<PERSON> from 'stripe';
import { HostBankingService } from "../../host-banking/v1/host-banking.service";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  // apiVersion: '2024-11-20.acacia',
});

export class StripeConnectService {

  /**
   * Crear cuenta conectada automáticamente para un nuevo host
   */
  static async createAccountForNewHost(hostId: string, email: string) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host) {
        throw HttpException.NotFound('Host no encontrado');
      }

      // Si ya tiene cuenta, no crear otra
      if (host.stripeConnectedAccountId) {
        return {
          accountId: host.stripeConnectedAccountId,
          alreadyExists: true
        };
      }

      // Crear cuenta conectada en Stripe automáticamente
      const account = await stripe.accounts.create({
        type: 'custom', // Tipo custom para manejo manual sin onboarding
        country: 'MX',
        email: email || host.email,
        capabilities: {
          transfers: { requested: true },
        },
        business_type: 'individual',
        tos_acceptance: {
          date: Math.floor(Date.now() / 1000),
          ip: '127.0.0.1' // IP temporal para desarrollo
        },
        // Información mínima requerida para cuentas custom en México
        individual: {
          first_name: host.name?.split(' ')[0] || 'Usuario',
          last_name: host.name?.split(' ').slice(1).join(' ') || 'Autoop',
          email: host.email,
          dob: {
            day: 1,
            month: 1,
            year: 1990
          },
          address: {
            line1: 'Dirección temporal',
            city: 'Ciudad de México',
            state: 'CDMX',
            postal_code: '01000',
            country: 'MX'
          }
        },
        metadata: {
          hostId: hostId,
          platform: 'autoop',
          userEmail: host.email,
          userName: host.name,
          autoCreated: 'true'
        }
      });

      // Guardar el ID de la cuenta conectada
      await prisma.user.update({
        where: { id: hostId },
        data: {
          stripeConnectedAccountId: account.id
        }
      });

      return {
        accountId: account.id,
        alreadyExists: false
      };
    } catch (error: any) {
      console.error('Error creating account for new host:', error);

      // No lanzar error para no interrumpir el registro del usuario
      // Solo loggear el error
      return {
        accountId: null,
        alreadyExists: false,
        error: error.message
      };
    }
  }

  /**
   * Crear cuenta conectada de Stripe para un host
   */
  static async createConnectedAccount(hostId: string, accountData: {
    email: string;
    country: string; // 'MX' para México
    type: 'express' | 'standard' | 'custom';
  }) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host) {
        throw HttpException.NotFound('Host no encontrado');
      }

      if (host.stripeConnectedAccountId) {
        // Si ya tiene cuenta, solo crear enlace de onboarding
        const onboardingUrl = await this.createOnboardingLink(host.stripeConnectedAccountId);
        return {
          accountId: host.stripeConnectedAccountId,
          onboardingUrl: onboardingUrl
        };
      }

      // Crear cuenta conectada en Stripe
      const account = await stripe.accounts.create({
        type: 'custom', // Forzar custom para manejo manual
        country: accountData.country,
        email: accountData.email || host.email,
        capabilities: {
          transfers: { requested: true },
        },
        business_type: 'individual', // Para hosts individuales
        tos_acceptance: {
          date: Math.floor(Date.now() / 1000),
          ip: '127.0.0.1' // IP temporal para desarrollo
        },
        // Información mínima requerida para cuentas custom en México
        individual: {
          first_name: host.name?.split(' ')[0] || 'Usuario',
          last_name: host.name?.split(' ').slice(1).join(' ') || 'Autoop',
          email: host.email,
          dob: {
            day: 1,
            month: 1,
            year: 1990
          },
          address: {
            line1: 'Dirección temporal',
            city: 'Ciudad de México',
            state: 'CDMX',
            postal_code: '01000',
            country: 'MX'
          }
        },
        metadata: {
          hostId: hostId,
          platform: 'autoop',
          userEmail: host.email,
          userName: host.name
        }
      });

      // Guardar el ID de la cuenta conectada
      await prisma.user.update({
        where: { id: hostId },
        data: {
          stripeConnectedAccountId: account.id
        }
      });

      // Crear enlace de onboarding inmediatamente
      const onboardingUrl = await this.createOnboardingLink(account.id);

      return {
        accountId: account.id,
        onboardingUrl: onboardingUrl
      };
    } catch (error: any) {
      console.error('Error creating connected account:', error);

      // Manejar errores específicos de Stripe
      if (error.type === 'StripeInvalidRequestError') {
        throw HttpException.BadRequest(error.message || 'Error de validación en Stripe');
      }

      throw HttpException.InternalServerError('Error al crear cuenta conectada');
    }
  }

  /**
   * Crear enlace de onboarding para completar configuración de cuenta
   */
  static async createOnboardingLink(accountId: string) {
    try {
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: `${process.env.FRONTEND_URL}/dashboard/host/onboarding?refresh=true`,
        return_url: `${process.env.BACKEND_URL}/dashboard/host/banking?success=true`,
        type: 'account_onboarding',
        collect: 'eventually_due', // Recopilar toda la información necesaria
      });

      return accountLink.url;
    } catch (error: any) {
      console.error('Error creating onboarding link:', error);

      // Manejar errores específicos de Stripe
      if (error.type === 'StripeInvalidRequestError') {
        throw HttpException.BadRequest(error.message || 'Error de validación en Stripe');
      }

      throw HttpException.InternalServerError('Error al crear enlace de configuración');
    }
  }

  /**
   * Verificar estado de la cuenta conectada
   */
  static async getAccountStatus(hostId: string) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host?.stripeConnectedAccountId) {
        return {
          hasAccount: false,
          isComplete: false,
          canReceivePayments: false,
          needsOnboarding: true
        };
      }

      const account = await stripe.accounts.retrieve(host.stripeConnectedAccountId);

      const canReceivePayments = account.charges_enabled && account.payouts_enabled;
      const isComplete = account.details_submitted && canReceivePayments;
      const needsOnboarding = !isComplete;

      return {
        hasAccount: true,
        isComplete,
        canReceivePayments,
        needsOnboarding,
        requirements: account.requirements,
        accountId: account.id,
        // Información adicional
        country: account.country,
        defaultCurrency: account.default_currency,
        email: account.email,
        // Estado de capacidades
        capabilities: account.capabilities,
        // Información de verificación
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        detailsSubmitted: account.details_submitted
      };
    } catch (error: any) {
      console.error('Error getting account status:', error);

      // Manejar errores específicos de Stripe
      if (error.type === 'StripeInvalidRequestError') {
        throw HttpException.BadRequest(error.message || 'Error de validación en Stripe');
      }

      throw HttpException.InternalServerError('Error al obtener estado de cuenta');
    }
  }

  /**
   * Obtener cuentas bancarias de la cuenta conectada
   */
  static async getBankAccounts(hostId: string) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host?.stripeConnectedAccountId) {
        return [];
      }

      // Obtener cuentas bancarias externas
      const externalAccounts = await stripe.accounts.listExternalAccounts(
        host.stripeConnectedAccountId,
        { object: 'bank_account', limit: 10 }
      );

      return externalAccounts.data;
    } catch (error) {
      console.error('Error getting bank accounts:', error);
      throw HttpException.InternalServerError('Error al obtener cuentas bancarias');
    }
  }

  /**
   * Agregar método de pago (cuenta bancaria) a cuenta conectada
   */
  static async addBankAccount(hostId: string, bankData: {
    routingNumber: string; // CLABE en México
    accountHolderName: string;
    accountType: 'checking' | 'savings';
    bankName?: string;
  }) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host) {
        throw HttpException.NotFound('Host no encontrado');
      }

      // Si no tiene cuenta conectada, crear una primero
      let stripeAccountId = host.stripeConnectedAccountId;
      if (!stripeAccountId) {
        console.log('Creando cuenta conectada para host:', hostId);
        const account = await stripe.accounts.create({
          type: 'custom',
          country: 'MX',
          email: host.email,
          capabilities: {
            transfers: { requested: true },
          },
          business_type: 'individual',
          tos_acceptance: {
            date: Math.floor(Date.now() / 1000),
            ip: '127.0.0.1' // IP temporal para desarrollo
          },
          // Información mínima requerida para cuentas custom en México
          individual: {
            first_name: host.name?.split(' ')[0] || 'Usuario',
            last_name: host.name?.split(' ').slice(1).join(' ') || 'Autoop',
            email: host.email,
            dob: {
              day: 1,
              month: 1,
              year: 1990
            },
            address: {
              line1: 'Dirección temporal',
              city: 'Ciudad de México',
              state: 'CDMX',
              postal_code: '01000',
              country: 'MX'
            }
          }
        });

        stripeAccountId = account.id;

        // Actualizar el host con el ID de la cuenta conectada
        await prisma.user.update({
          where: { id: hostId },
          data: { stripeConnectedAccountId: stripeAccountId }
        });
      }

      // Crear método de pago externo (cuenta bancaria) directamente
      const externalAccount = await stripe.accounts.createExternalAccount(
        stripeAccountId,
        {
          external_account: {
            object: 'bank_account',
            country: 'MX',
            currency: 'mxn',
            account_number: bankData.routingNumber, // En México, usar CLABE como account_number
            account_holder_name: bankData.accountHolderName,
            account_holder_type: 'individual'
          },
          metadata: {
            bank_name: bankData.bankName || '',
            added_via: 'autoop_form',
            clabe: bankData.routingNumber
          }
        }
      );

      // Guardar en la base de datos con el ID de Stripe
      await HostBankingService.addBankAccount(hostId, {
        accountHolderName: bankData.accountHolderName,
        routingNumber: bankData.routingNumber,
        bankName: bankData.bankName || '',
        accountType: bankData.accountType,
        stripeExternalAccountId: externalAccount.id
      });

      return {
        externalAccountId: externalAccount.id,
        accountId: stripeAccountId,
        success: true,
        message: 'Cuenta bancaria agregada exitosamente.'
      };

    } catch (error: any) {
      console.error('Error adding bank account:', error);

      // Manejar errores específicos de Stripe
      if (error.type === 'StripeInvalidRequestError') {
        throw HttpException.BadRequest(error.message || 'Datos de cuenta bancaria inválidos');
      }

      if (error.type === 'StripePermissionError') {
        // Si es error de permisos, probablemente necesita onboarding
        const host = await prisma.user.findUnique({
          where: { id: hostId }
        });

        if (host?.stripeConnectedAccountId) {
          // Guardar solo en base de datos local
          await HostBankingService.addBankAccount(hostId, {
            accountHolderName: bankData.accountHolderName,
            routingNumber: bankData.routingNumber,
            bankName: bankData.bankName || '',
            accountType: bankData.accountType,
          });

          const onboardingUrl = await this.createOnboardingLink(host.stripeConnectedAccountId);

          return {
            success: true,
            needsOnboarding: true,
            onboardingUrl: onboardingUrl,
            accountId: host.stripeConnectedAccountId,
            message: 'Información bancaria guardada. Completa la configuración de tu cuenta para recibir pagos.'
          };
        }

        throw HttpException.Forbidden('No tienes permisos para realizar esta acción. Completa la configuración de tu cuenta.');
      }

      throw HttpException.InternalServerError('Error al agregar cuenta bancaria');
    }
  }

  /**
   * Eliminar cuenta bancaria
   */
  static async removeBankAccount(hostId: string, bankAccountId: string) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host?.stripeConnectedAccountId) {
        throw HttpException.BadRequest('El host no tiene una cuenta conectada');
      }

      // Obtener todas las cuentas bancarias primero
      const account = await stripe.accounts.retrieve(host.stripeConnectedAccountId);
      const externalAccounts = account.external_accounts?.data || [];
      const bankAccounts = externalAccounts.filter(acc => acc.object === 'bank_account');

      // Verificar si es la única cuenta bancaria
      if (bankAccounts.length === 1) {
        throw HttpException.BadRequest('No puedes eliminar la única cuenta bancaria. Agrega otra cuenta primero.');
      }

      // Verificar si es la cuenta por defecto y hay otras cuentas
      const accountToDelete = bankAccounts.find(acc => acc.id === bankAccountId);
      if (accountToDelete?.default_for_currency && bankAccounts.length > 1) {
        // Hacer que otra cuenta sea la por defecto antes de eliminar
        const otherAccount = bankAccounts.find(acc => acc.id !== bankAccountId);
        if (otherAccount) {
          await stripe.accounts.updateExternalAccount(
            host.stripeConnectedAccountId,
            otherAccount.id,
            { default_for_currency: true }
          );
        }
      }

      // Eliminar cuenta bancaria externa
      const deletedAccount = await stripe.accounts.deleteExternalAccount(
        host.stripeConnectedAccountId,
        bankAccountId
      );

      // Si la operación en Stripe fue exitosa, eliminar también de la base de datos
      try {
        const bankingInfo = await HostBankingService.getHostBankingInfo(hostId);
        const accountToRemove = bankingInfo.bankAccounts.find(
          acc => acc.stripeExternalAccountId === bankAccountId
        );

        if (accountToRemove) {
          await HostBankingService.removeBankAccount(hostId, accountToRemove.id);
        }
      } catch (dbError) {
        console.error('Error removing bank account from database:', dbError);
        // No lanzar error aquí para no afectar la operación de Stripe
      }

      return deletedAccount;
    } catch (error: any) {
      console.error('Error removing bank account:', error);

      // Manejar errores específicos de Stripe
      if (error.type === 'StripeInvalidRequestError') {
        if (error.message && error.message.includes('default external account')) {
          throw HttpException.BadRequest('No puedes eliminar la cuenta bancaria por defecto. Agrega otra cuenta primero.');
        }
        throw HttpException.BadRequest(error.message || 'Error de validación en Stripe');
      }

      // Manejar errores de HttpException
      if (error instanceof HttpException) {
        throw error;
      }

      throw HttpException.InternalServerError('Error al eliminar cuenta bancaria');
    }
  }

  /**
   * Actualizar cuenta bancaria (eliminar la anterior y agregar nueva)
   */
  static async updateBankAccount(hostId: string, oldBankAccountId: string, newBankData: {
    // accountNumber: string;
    routingNumber: string;
    accountHolderName: string;
    accountType: 'checking' | 'savings';
    bankName?: string;
  }) {
    try {
      // Eliminar cuenta anterior
      await this.removeBankAccount(hostId, oldBankAccountId);

      // Agregar nueva cuenta
      const newAccount = await this.addBankAccount(hostId, newBankData);

      return newAccount;
    } catch (error) {
      console.error('Error updating bank account:', error);
      throw HttpException.InternalServerError('Error al actualizar cuenta bancaria');
    }
  }

  /**
   * Listar métodos de pago de la cuenta conectada
   */
  static async listBankAccounts(hostId: string) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host?.stripeConnectedAccountId) {
        throw HttpException.BadRequest('El host no tiene una cuenta conectada');
      }

      const externalAccounts = await stripe.accounts.listExternalAccounts(
        host.stripeConnectedAccountId,
        { object: 'bank_account' }
      );

      return externalAccounts.data
        .filter(account => account.object === 'bank_account') // Solo cuentas bancarias
        .map(account => {
          // Type assertion ya que filtramos por bank_account
          const bankAccount = account as Stripe.BankAccount;
          return {
            id: bankAccount.id,
            last4: bankAccount.last4,
            bankName: bankAccount.bank_name || 'Banco desconocido',
            accountType: bankAccount.account_type || 'checking',
            isDefault: bankAccount.default_for_currency || false
          };
        });
    } catch (error) {
      console.error('Error listing bank accounts:', error);
      throw HttpException.InternalServerError('Error al obtener cuentas bancarias');
    }
  }

  /**
   * Crear enlace de dashboard para que el host gestione su cuenta
   */
  static async createDashboardLink(hostId: string) {
    try {
      const host = await prisma.user.findUnique({
        where: { id: hostId }
      });

      if (!host?.stripeConnectedAccountId) {
        throw HttpException.BadRequest('El host no tiene una cuenta conectada');
      }

      const loginLink = await stripe.accounts.createLoginLink(
        host.stripeConnectedAccountId
      );

      return loginLink.url;
    } catch (error) {
      console.error('Error creating dashboard link:', error);
      throw HttpException.InternalServerError('Error al crear enlace de dashboard');
    }
  }

  /**
   * Verificar si el host puede recibir transferencias
   */
  static async canReceiveTransfers(hostId: string): Promise<boolean> {
    try {
      const status = await this.getAccountStatus(hostId);
      return status.canReceivePayments;
    } catch (error) {
      console.error('Error checking transfer capability:', error);
      return false;
    }
  }
}
