import { apiService } from '@/services/api';
import { Vehicle } from './vehicles.api';

export interface ReservationData {
  vehicleId: string;
  startDate: string;
  endDate: string;
  totalPrice: number;
  contactName?: string;
  contactEmail?: string;
  contactPhone?: string;
}

export interface ReservationResponse {
  id: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  vehicleId: string;
  startDate: string;
  endDate: string;
  totalPrice: number;
  status: string;
  by: string;
  reason: string | null;
  contactName: string | null;
  contactEmail: string | null;
  contactPhone: string | null;
  hostVerification: boolean;
  hostVerificationDate: string | null;
  hostVerificationNotes: string | null;
  vehicle: Vehicle;
}

export interface HostReservation extends ReservationResponse {
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
}

export interface ReservationCreationResponse {
  id: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  vehicleId: string;
  startDate: Date;
  endDate: Date;
  totalPrice: number;
  status: string;
  by: string;
  reason: string | null;
  contactName: string | null;
  contactEmail: string | null;
  contactPhone: string | null;
}

export interface ReservationsForAdminView extends HostReservation {
  isPersonalReservation: boolean;
}

export interface ReservationStats {
  totalReservations: number;
  activeReservations: number;
  pendingReservations: number;
  completedReservations: number;
  cancelledReservations: number;
  totalEarnings: number;
}

export const reservationsApi = {

  client: {
  // Crear una nueva reserva
    create: async (data: ReservationData) => {
      const response = await apiService.post<ReservationCreationResponse>('/user/reservations', data);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },

    // Obtener reservas del usuario
    getReservations: async () => {
      const response = await apiService.get<ReservationResponse[]>('/user/reservations');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },

    // Obtener una reserva específica
    getReservationById: async (id: string) => {
      const response = await apiService.get<ReservationResponse>(`/user/reservations/${id}`);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },

    // Cancelar una reserva
    cancelReservation: async (id: string) => {
      const response = await apiService.patch<ReservationResponse>(`/user/reservations/${id}/cancel`);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },
  },



  /* Group by host */
  host: {
    getReservations: async (params: { page: number; limit: number }) => {
      const response = await apiService.get<HostReservation[]>('/host/reservations', { params });
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },
    cancelReservation: async (id: string) => {
      const response = await apiService.patch<ReservationResponse>(`/host/reservations/${id}/cancel`);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },
    getPersonalReservations: async () => {
      const response = await apiService.get<ReservationResponse[]>('/host/reservations/personal');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },
    updateReservationStatus: async (id: string, status: string) => {
      const response = await apiService.patch<ReservationResponse>(`/host/reservations/${id}/status`, { status });
      return response.data;
    },
    getStats: async () => {
      const response = await apiService.get<ReservationStats>('/host/reservations/stats');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },
    verifyVehicleReturn: async (reservationId: string, notes?: string) => {
      const response = await apiService.patch<ReservationResponse>(
        `/host/reservations/${reservationId}/verify-return`,
        { notes }
      );
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    }
  },

  admin: {

    // Obtener todas las reservas para el administrador
    getReservations: async () => {
      const response = await apiService.get<ReservationsForAdminView[]>('/admin/reservations');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error.message);
      }
    },
  }
};


