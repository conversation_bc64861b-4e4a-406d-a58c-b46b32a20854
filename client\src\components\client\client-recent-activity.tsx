"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"
import { clientTransactionsApi } from "@/lib/api/transactions.api"
import { reservationsApi } from "@/lib/api/reservations.api"
import { DateTime } from "luxon"

interface ActivityItem {
  id: string;
  type: "reservation_confirmed" | "payment_completed" | "reservation_completed" | "reservation_cancelled";
  title: string;
  description: string;
  time: string;
  status: "success" | "info" | "completed" | "cancelled";
  createdAt: Date;
}

export function ClientRecentActivity() {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        const [transactionsResponse, reservationsResponse] = await Promise.all([
          clientTransactionsApi.getTransactions({ limit: 10, offset: 0 }),
          reservationsApi.client.getReservations()
        ]);

        const activityItems: ActivityItem[] = [];

        // Agregar transacciones como actividad
        if (transactionsResponse.success && transactionsResponse.data?.transactions) {
          transactionsResponse.data.transactions.forEach(transaction => {
            if (transaction.status === 'completed' && transaction.paidAt) {
              activityItems.push({
                id: `transaction-${transaction.id}`,
                type: "payment_completed",
                title: "Pago procesado",
                description: `$${transaction.amount.toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} - ${transaction.reservation.vehicle.make} ${transaction.reservation.vehicle.model}`,
                time: getRelativeTime(transaction.paidAt),
                status: "success",
                createdAt: new Date(transaction.paidAt)
              });
            }
          });
        }

        // Agregar reservas como actividad
        if (reservationsResponse) {
          reservationsResponse.forEach(reservation => {
            if (reservation.status === 'confirmed') {
              console.log('created AT: ', reservation.createdAt, typeof reservation.createdAt);
              activityItems.push({
                id: `reservation-${reservation.id}`,
                type: "reservation_confirmed",
                title: "Reserva confirmada",
                description: `${reservation.vehicle.make} ${reservation.vehicle.model} - ${getDaysDifference(reservation.startDate, reservation.endDate)} ${getDaysDifference(reservation.startDate, reservation.endDate) === 1 ? 'día' : 'días'}`,
                time: getRelativeTime(reservation.createdAt as unknown as string),
                status: "success",
                createdAt: new Date(reservation.createdAt)
              });
            } else if (reservation.status === 'completed') {
              activityItems.push({
                id: `reservation-completed-${reservation.id}`,
                type: "reservation_completed",
                title: "Reserva completada",
                description: `${reservation.vehicle.make} ${reservation.vehicle.model} - ${getDaysDifference(reservation.startDate, reservation.endDate)} ${getDaysDifference(reservation.startDate, reservation.endDate) === 1 ? 'día' : 'días'}`,
                time: getRelativeTime(reservation.updatedAt.toISOString()),
                status: "completed",
                createdAt: new Date(reservation.updatedAt)
              });
            } else if (reservation.status === 'cancelled') {
              activityItems.push({
                id: `reservation-cancelled-${reservation.id}`,
                type: "reservation_cancelled",
                title: "Reserva cancelada",
                description: `${reservation.vehicle.make} ${reservation.vehicle.model}`,
                time: getRelativeTime(reservation.updatedAt.toISOString()),
                status: "cancelled",
                createdAt: new Date(reservation.updatedAt)
              });
            }
          });
        }

        // Ordenar por fecha más reciente y tomar solo los primeros 4
        const sortedActivities = activityItems
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
          .slice(0, 4);

        setActivities(sortedActivities);
      } catch (error) {
        console.error('Error fetching recent activity:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentActivity();
  }, []);

  const getRelativeTime = (dateString: string) => {
    const date = DateTime.fromISO(dateString);
    const now = DateTime.now();
    const diff = now.diff(date);

    if (diff.as('minutes') < 60) {
      const minutes = Math.floor(diff.as('minutes'));
      return `Hace ${minutes} ${minutes === 1 ? 'minuto' : 'minutos'}`;
    } else if (diff.as('hours') < 24) {
      const hours = Math.floor(diff.as('hours'));
      return `Hace ${hours} ${hours === 1 ? 'hora' : 'horas'}`;
    } else if (diff.as('days') < 7) {
      const days = Math.floor(diff.as('days'));
      return `Hace ${days} ${days === 1 ? 'día' : 'días'}`;
    } else if (diff.as('weeks') < 4) {
      const weeks = Math.floor(diff.as('weeks'));
      return `Hace ${weeks} ${weeks === 1 ? 'semana' : 'semanas'}`;
    } else {
      const months = Math.floor(diff.as('months'));
      return `Hace ${months} ${months === 1 ? 'mes' : 'meses'}`;
    }
  };

  const getDaysDifference = (startDate: string, endDate: string) => {
    const start = DateTime.fromISO(startDate);
    const end = DateTime.fromISO(endDate);
    return Math.ceil(end.diff(start, 'days').days);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Actividad Reciente</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/client/reservations">Ver Todo</Link>
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-start gap-4">
                <div className="h-2 w-2 rounded-full bg-muted animate-pulse mt-2" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-48 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-20 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No hay actividad reciente</p>
          </div>
        ) : (
              <div className="space-y-4">
                {activities.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-4">
                    <div className="h-2 w-2 rounded-full bg-primary mt-2" />
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{activity.title}</p>
                        <Badge
                          variant={
                            activity.status === "success"
                              ? "default"
                              : activity.status === "completed"
                                ? "secondary"
                                : activity.status === "cancelled"
                                  ? "destructive"
                                  : "outline"
                          }
                          className="text-xs"
                        >
                          {activity.status === "success"
                            ? "Exitoso"
                            : activity.status === "completed"
                              ? "Completado"
                              : activity.status === "cancelled"
                                ? "Cancelado"
                                : "Info"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
        )}
      </CardContent>
    </Card>
  )
}
