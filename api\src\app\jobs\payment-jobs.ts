import { Queue, Worker, Job } from 'bullmq';
import { prisma } from '@/lib/prisma';
import { TransactionsService } from '../modules/transactions/v1/transactions.service';
import { ReportsService } from '../modules/reports/v1/reports.service';
import { DateTime } from 'luxon';

// Configuración de Redis para BullMQ
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
};

// Colas de trabajos
export const transferQueue = new Queue('host-transfers', { connection: redisConfig });
export const reportsQueue = new Queue('monthly-reports', { connection: redisConfig });

// Tipos de trabajos
interface TransferJobData {
  transactionId: string;
  reservationId: string;
  hostId: string;
}

interface ReportJobData {
  year?: number;
  month?: number;
  hostId?: string; // Si se especifica, solo generar para este host
}

/**
 * Programar transferencia a host después del check-in
 * NOTA: Actualmente desactivado para transferencias manuales
 */
export async function scheduleHostTransfer(transactionId: string, reservationId: string, checkInDate: Date) {
  try {
    // TRANSFERENCIAS AUTOMÁTICAS DESACTIVADAS
    // Las transferencias ahora se realizan manualmente desde el panel de administración
    console.log(`Transferencia automática desactivada para transacción ${transactionId}. Usar panel de admin para transferencias manuales.`);
    return null;

    /* CÓDIGO ORIGINAL COMENTADO PARA USO FUTURO
    // Obtener configuración de delay
    let platformSettings = await prisma.platformSettings.findFirst();
    if (!platformSettings) {
      platformSettings = await prisma.platformSettings.create({
        data: { commissionRate: 0.12, transferDelayHours: 24 }
      });
    }

    // Calcular cuándo ejecutar la transferencia
    const transferDate = DateTime.fromJSDate(checkInDate).plus({
      hours: platformSettings.transferDelayHours
    }).toJSDate();

    // Obtener información de la transacción
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        reservation: {
          include: {
            vehicle: {
              select: { hostId: true }
            }
          }
        }
      }
    });

    if (!transaction) {
      throw new Error(`Transacción ${transactionId} no encontrada`);
    }

    const jobData: TransferJobData = {
      transactionId,
      reservationId,
      hostId: transaction.reservation.vehicle.hostId
    };

    // Programar el trabajo
    const job = await transferQueue.add(
      'process-host-transfer',
      jobData,
      {
        delay: transferDate.getTime() - Date.now(),
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 60000, // 1 minuto
        },
        removeOnComplete: 100,
        removeOnFail: 50
      }
    );

    console.log(`Transferencia programada para ${transferDate.toISOString()}, Job ID: ${job.id}`);
    return job;
    */
  } catch (error) {
    console.error('Error scheduling host transfer:', error);
    throw error;
  }
}

/**
 * Programar generación de reportes mensuales
 */
export async function scheduleMonthlyReports(year?: number, month?: number) {
  try {
    const jobData: ReportJobData = { year, month };

    const job = await reportsQueue.add(
      'generate-monthly-reports',
      jobData,
      {
        attempts: 2,
        backoff: {
          type: 'fixed',
          delay: 300000, // 5 minutos
        },
        removeOnComplete: 10,
        removeOnFail: 10
      }
    );

    console.log(`Generación de reportes mensuales programada, Job ID: ${job.id}`);
    return job;
  } catch (error) {
    console.error('Error scheduling monthly reports:', error);
    throw error;
  }
}

/**
 * Worker para procesar transferencias a hosts
 * NOTA: Actualmente desactivado para transferencias manuales
 */
export const transferWorker = new Worker(
  'host-transfers',
  async (job: Job<TransferJobData>) => {
    const { transactionId } = job.data;

    // TRANSFERENCIAS AUTOMÁTICAS DESACTIVADAS
    console.log(`Worker de transferencias desactivado. Transacción ${transactionId} requiere procesamiento manual desde el panel de admin.`);
    return { success: false, message: 'Transferencias automáticas desactivadas' };

    /* CÓDIGO ORIGINAL COMENTADO PARA USO FUTURO
    const { transactionId, reservationId, hostId } = job.data;

    console.log(`Procesando transferencia para transacción ${transactionId}`);

    try {
      // Verificar que la reserva esté activa
      const reservation = await prisma.vehicleReservation.findUnique({
        where: { id: reservationId },
        include: {
          transaction: true
        }
      });

      if (!reservation) {
        throw new Error(`Reserva ${reservationId} no encontrada`);
      }

      if (reservation.status === 'cancelled') {
        console.log(`Reserva ${reservationId} fue cancelada, no se procesará la transferencia`);
        return { success: false, reason: 'Reserva cancelada' };
      }

      // Verificar que la transacción esté en estado válido
      if (!reservation.transaction || reservation.transaction.status !== 'succeeded') {
        throw new Error(`Transacción no está en estado válido para transferir`);
      }

      // Procesar la transferencia
      const result = await TransactionsService.processHostTransfer(transactionId);

      console.log(`Transferencia completada para transacción ${transactionId}`);
      return { success: true, transfer: result };
    } catch (error) {
      console.error(`Error procesando transferencia ${transactionId}:`, error);
      throw error;
    }
    */
  },
  { connection: redisConfig }
);

/**
 * Worker para generar reportes mensuales
 */
export const reportsWorker = new Worker(
  'monthly-reports',
  async (job: Job<ReportJobData>) => {
    const { year, month, hostId } = job.data;

    console.log(`Generando reportes mensuales para ${year || 'año actual'}-${month || 'mes anterior'}`);

    try {
      if (hostId) {
        // Generar reporte para un host específico
        const targetYear = year || new Date().getFullYear();
        const targetMonth = month || new Date().getMonth();

        const result = await ReportsService.generateHostMonthlyReport(hostId, targetYear, targetMonth);
        console.log(`Reporte mensual generado para host ${hostId}`);
        return { success: true, report: result };
      } else {
        // Generar reportes para todos los hosts
        const result = await ReportsService.generateAllHostsMonthlyReports(year, month);
        console.log(`Reportes mensuales generados para ${result.totalHosts} hosts`);
        return { success: true, reports: result };
      }
    } catch (error) {
      console.error('Error generando reportes mensuales:', error);
      throw error;
    }
  },
  { connection: redisConfig }
);

/**
 * Configurar trabajos recurrentes
 * NOTA: Transferencias automáticas desactivadas temporalmente
 */
export async function setupRecurringJobs() {
  try {
    // Limpiar trabajos recurrentes existentes
    await reportsQueue.removeRepeatable('generate-monthly-reports', {
      pattern: '0 2 1 * *' // 2 AM del primer día de cada mes
    });

    // TRANSFERENCIAS AUTOMÁTICAS DESACTIVADAS
    // Comentar la programación de transferencias automáticas
    //   await transferQueue.removeRepeatable('process-pending-transfers', {
    //     pattern: '0 */6 * * * ' // Cada 6 horas
    // });

    // await transferQueue.add(
    //   'process-pending-transfers',
    //   {},
    //   {
    //     repeat: {
    //       pattern: '0 */6 * * *', // Cada 6 horas
    //       tz: 'America/Mexico_City'
    //     },
    //     removeOnComplete: 5,
    //     removeOnFail: 3
    //   }
    // );

    // Programar generación automática de reportes mensuales
    await reportsQueue.add(
      'generate-monthly-reports',
      {},
      {
        repeat: {
          pattern: '0 2 1 * *', // 2 AM del primer día de cada mes
          tz: 'America/Mexico_City'
        },
        removeOnComplete: 5,
        removeOnFail: 3
      }
    );

    console.log('Trabajos recurrentes configurados correctamente');
    console.log('NOTA: Transferencias automáticas desactivadas - se requiere procesamiento manual desde el panel de admin');
  } catch (error) {
    console.error('Error configurando trabajos recurrentes:', error);
    throw error;
  }
}

/**
 * Procesar transferencias pendientes (para ejecutar manualmente si es necesario)
 */
export async function processPendingTransfers() {
  try {
    // Buscar transacciones que deberían haberse transferido
    const platformSettings = await prisma.platformSettings.findFirst();
    const delayHours = platformSettings?.transferDelayHours || 24;

    const cutoffDate = DateTime.now().minus({ hours: delayHours }).toJSDate();

    const pendingTransactions = await prisma.transaction.findMany({
      where: {
        status: 'succeeded',
        paidAt: {
          lte: cutoffDate
        },
        reservation: {
          status: 'confirmed',
          startDate: {
            lte: cutoffDate
          }
        }
      },
      include: {
        reservation: {
          include: {
            vehicle: {
              select: { hostId: true }
            }
          }
        }
      }
    });

    console.log(`Encontradas ${pendingTransactions.length} transferencias pendientes`);

    const results: any[] = [];
    // for (const transaction of pendingTransactions) {
    //   try {
    //     const result = await TransactionsService.processHostTransfer(transaction.id);
    //     results.push({ transactionId: transaction.id, success: true, result });
    //   } catch (error: any) {
    //     console.error(`Error procesando transferencia ${transaction.id}:`, error);
    //     results.push({ transactionId: transaction.id, success: false, error: error.message });
    //   }
    // }
    await Promise.all(pendingTransactions.map(async (transaction) => {
      try {
        const result = await TransactionsService.processHostTransfer(transaction.id);
        results.push({ transactionId: transaction.id, success: true, result });
      } catch (error: any) {
        console.error(`Error procesando transferencia ${transaction.id}:`, error);
        results.push({ transactionId: transaction.id, success: false, error: error.message });
      }
    }));

    return {
      totalProcessed: pendingTransactions.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  } catch (error) {
    console.error('Error processing pending transfers:', error);
    throw error;
  }
}

// Event listeners para logging
transferWorker.on('completed', (job) => {
  console.log(`Transfer job ${job.id} completed successfully`);
});

transferWorker.on('failed', (job, err) => {
  console.error(`Transfer job ${job?.id} failed:`, err);
});

reportsWorker.on('completed', (job) => {
  console.log(`Reports job ${job.id} completed successfully`);
});

reportsWorker.on('failed', (job, err) => {
  console.error(`Reports job ${job?.id} failed:`, err);
});
