import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface VehicleInfo {
  id: string
  make: string
  model: string
  year: number
  color: string
  vin?: string
  plate?: string
  price: number
  hostId?: string
  features?: {
    fuelType?: string
    seats?: number
    mileage?: number
    registrationNumber?: string
    insurancePolicy?: string
    rules?: string
    location?: string
    weeklyRate?: number
    monthlyRate?: number
  }
}

export interface DateRange {
  startDate: Date | null
  endDate: Date | null
}

export interface Location {
  address: string
  city: string
  state: string
  zipCode: string
}

export interface CoverageOption {
  type: "basic" | "standard" | "premium"
  price: number
}

export interface AddOn {
  id: string
  name: string
  price: number
  selected: boolean
}

interface PricingBreakdown {
  basePrice: number
  finalPrice: number
  totalDiscount: number
  discountType: 'none' | 'weekly' | 'monthly'
  weeksApplied: number
  monthsApplied: number
  extraDays: number
  extraDaysPrice: number
}

export interface BookingState {
  vehicle: VehicleInfo | null
  dateRange: DateRange
  pickupLocation: Location | null
  returnLocation: Location | null
  coverage: CoverageOption | null
  addOns: AddOn[]
  mileagePackage: {
    id: string
    name: string
    limit: number
    price: number
  } | null
  currentStep: number
  totalPrice: number
  pricingBreakdown: PricingBreakdown | null
  contactInfo: {
    contactName?: string
    contactEmail?: string
    contactPhone?: string
  }
  paymentInfo: {
    paymentIntentId?: string
    amount?: number
    currency?: string
  } | null

  // Actions
  setVehicle: (vehicle: VehicleInfo) => void
  setDateRange: (dateRange: DateRange) => void
  setPickupLocation: (location: Location) => void
  setReturnLocation: (location: Location) => void
  setCoverage: (coverage: CoverageOption) => void
  toggleAddOn: (id: string) => void
  setMileagePackage: (pkg: { id: string; name: string; limit: number; price: number }) => void
  nextStep: () => void
  prevStep: () => void
  goToStep: (step: number) => void
  calculateTotal: () => void
  reset: () => void
  setContactInfo: (contactInfo: {
    contactName?: string;
    contactEmail?: string;
    contactPhone?: string;
  }) => void
  setPaymentInfo: (paymentInfo: {
    paymentIntentId?: string;
    amount?: number;
    currency?: string;
  }) => void
}

// Función para calcular precios con descuentos por volumen
function calculateVehiclePricing(vehicle: VehicleInfo, days: number): PricingBreakdown {
  const dailyRate = vehicle.price
  const weeklyRate = vehicle.features?.weeklyRate || null
  const monthlyRate = vehicle.features?.monthlyRate || null

  // Precio base sin descuentos
  const basePrice = dailyRate * days

  // Si no hay tarifas especiales, usar precio base
  if (!weeklyRate && !monthlyRate) {
    return {
      basePrice,
      finalPrice: basePrice,
      totalDiscount: 0,
      discountType: 'none',
      weeksApplied: 0,
      monthsApplied: 0,
      extraDays: 0,
      extraDaysPrice: 0
    }
  }

  let finalPrice = 0
  let totalDiscount = 0
  let discountType: 'none' | 'weekly' | 'monthly' = 'none'
  let weeksApplied = 0
  let monthsApplied = 0
  let extraDays = 0
  let extraDaysPrice = 0

  // Calcular meses completos (30 días por mes)
  if (monthlyRate && days >= 30) {
    monthsApplied = Math.floor(days / 30)
    extraDays = days % 30

    finalPrice = monthlyRate * monthsApplied
    discountType = 'monthly'

    // Si quedan días extra, calcular si es mejor usar semanas o días
    if (extraDays > 0) {
      if (weeklyRate && extraDays >= 7) {
        const weeksInExtra = Math.floor(extraDays / 7)
        const remainingDays = extraDays % 7

        const weeklyOption = (weeklyRate * weeksInExtra) + (dailyRate * remainingDays)
        const dailyOption = dailyRate * extraDays

        if (weeklyOption < dailyOption) {
          finalPrice += weeklyOption
          weeksApplied = weeksInExtra
          extraDays = remainingDays
          extraDaysPrice = dailyRate * remainingDays
        } else {
          finalPrice += dailyOption
          extraDaysPrice = dailyOption
        }
      } else {
        finalPrice += dailyRate * extraDays
        extraDaysPrice = dailyRate * extraDays
      }
    }
  }
  // Calcular semanas completas (7 días por semana)
  else if (weeklyRate && days >= 7) {
    weeksApplied = Math.floor(days / 7)
    extraDays = days % 7

    const weeklyOption = weeklyRate * weeksApplied + (dailyRate * extraDays)
    const dailyOption = dailyRate * days

    // Solo usar precio semanal si es realmente más barato
    if (weeklyOption < dailyOption) {
      finalPrice = weeklyOption
      discountType = 'weekly'
      extraDaysPrice = dailyRate * extraDays
    } else {
      // Precio diario es mejor
      finalPrice = dailyOption
      extraDays = days
      extraDaysPrice = dailyOption
      weeksApplied = 0
    }
  }
  // Menos de una semana, usar precio diario
  else {
    finalPrice = basePrice
    extraDays = days
    extraDaysPrice = basePrice
  }

  totalDiscount = basePrice - finalPrice

  return {
    basePrice,
    finalPrice,
    totalDiscount,
    discountType,
    weeksApplied,
    monthsApplied,
    extraDays,
    extraDaysPrice
  }
}

const initialState = {
  vehicle: null,
  dateRange: {
    startDate: null,
    endDate: null,
  },
  pickupLocation: null,
  returnLocation: null,
  coverage: null,
  addOns: [],
  mileagePackage: null,
  currentStep: 1,
  totalPrice: 0,
  pricingBreakdown: null,
  contactInfo: {
    contactName: "",
    contactEmail: "",
    contactPhone: "",
  },
  paymentInfo: null,
}

export const useBookingStore = create<BookingState>()(
  persist(
    (set, get) => ({
      ...initialState,

      setVehicle: (vehicle) => set({ vehicle }),

      setDateRange: (dateRange) => {
        set({ dateRange })
        get().calculateTotal()
      },

      setPickupLocation: (location) => set({ pickupLocation: location }),

      setReturnLocation: (location) => set({ returnLocation: location }),

      setCoverage: (coverage) => {
        set({ coverage })
        get().calculateTotal()
      },

      toggleAddOn: (id) => {
        const addOns = get().addOns.map((addon) => (addon.id === id ? { ...addon, selected: !addon.selected } : addon))
        set({ addOns })
        get().calculateTotal()
      },

      setMileagePackage: (pkg) => {
        set({ mileagePackage: pkg })
        get().calculateTotal()
      },

      nextStep: () => set((state) => ({ currentStep: state.currentStep + 1 })),

      prevStep: () => set((state) => ({ currentStep: state.currentStep - 1 })),

      goToStep: (step) => set({ currentStep: step }),

      calculateTotal: () => {
        try {
          const { vehicle, dateRange, coverage, addOns, mileagePackage } = get()

          if (!vehicle || !dateRange.startDate || !dateRange.endDate) {
            set({ totalPrice: 0 })
            return
          }

          // Verificar que las fechas son válidas
          if (!(dateRange.startDate instanceof Date) || !(dateRange.endDate instanceof Date)) {
            console.error("Invalid date objects in calculateTotal")
            set({ totalPrice: 0 })
            return
          }

          // Verificar que el vehículo tiene precio
          if (!vehicle.price || typeof vehicle.price !== 'number') {
            console.error("Invalid vehicle price in calculateTotal")
            set({ totalPrice: 0 })
            return
          }

        const days = Math.ceil(
          (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
        )

          // Calcular precio con descuentos por volumen
          const pricingResult = calculateVehiclePricing(vehicle, days)
          const basePrice = pricingResult.finalPrice

        // Calculate total including coverage, addOns, and mileagePackage
        let total = basePrice

        // Add coverage cost if selected
        if (coverage) {
          total += coverage.price * days
        }

        // Add selected add-ons
        addOns.forEach((addon) => {
          if (addon.selected) {
            total += addon.price
          }
        })

        // Add mileage package if selected
        if (mileagePackage) {
          total += mileagePackage.price
        }

          set({
            totalPrice: total,
            pricingBreakdown: pricingResult
          })
        } catch (error) {
          console.error("Error in calculateTotal:", error)
          set({
            totalPrice: 0,
            pricingBreakdown: null
          })
        }
      },

      reset: () => set(initialState),
      setContactInfo: (contactInfo) => set({ contactInfo }),
      setPaymentInfo: (paymentInfo) => set({ paymentInfo }),
    }),
    {
      name: "autoop-booking-storage",
    },
  ),
)
