import { <PERSON>sia, t } from "elysia";
import { HostBankingService, CreateBankAccountData, UpdateBankAccountData } from "./host-banking.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';

// Esquemas de validación
const createBankAccountSchema = t.Object({
  accountHolderName: t.String(),
  routingNumber: t.String(), // CLABE
  bankName: t.String(),
  accountType: t.Union([t.Literal('checking'), t.Literal('savings')]),
  stripeExternalAccountId: t.Optional(t.String())
});

const updateBankAccountSchema = t.Object({
  id: t.String(),
  accountHolderName: t.String(),
  routingNumber: t.String(),
  bankName: t.String(),
  accountType: t.Union([t.Literal('checking'), t.Literal('savings')]),
  stripeExternalAccountId: t.Optional(t.String())
});

const setDefaultAccountSchema = t.Object({
  accountId: t.String()
});

// Controlador para hosts
export const hostBankingController = new Elysia()
  .use(authMiddleware)
  .get('/host/banking-info',
    async ({ user }) => {
      const bankingInfo = await HostBankingService.getHostBankingInfo(user.id);
      return { success: true, data: bankingInfo };
    }
  )
  .post('/host/banking-info/accounts',
    async ({ user, body }) => {
      const accountData: CreateBankAccountData = body;
      const newAccount = await HostBankingService.addBankAccount(user.id, accountData);
      return { success: true, data: newAccount };
    },
    {
      body: createBankAccountSchema
    }
  )
  .put('/host/banking-info/accounts',
    async ({ user, body }) => {
      const accountData: UpdateBankAccountData = body;
      const updatedAccount = await HostBankingService.updateBankAccount(user.id, accountData);
      return { success: true, data: updatedAccount };
    },
    {
      body: updateBankAccountSchema
    }
  )
  .delete('/host/banking-info/accounts/:accountId',
    async ({ user, params }) => {
      const result = await HostBankingService.removeBankAccount(user.id, params.accountId);
      return { success: true, data: result };
    }
  )
  .post('/host/banking-info/set-default',
    async ({ user, body }) => {
      const result = await HostBankingService.setDefaultBankAccount(user.id, body.accountId);
      return { success: true, data: result };
    },
    {
      body: setDefaultAccountSchema
    }
  );

// Controlador para administradores
export const adminBankingController = new Elysia()
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/admin/banking-info/:hostId',
    async ({ params }) => {
      const bankingInfo = await HostBankingService.getHostBankingInfo(params.hostId);
      return { success: true, data: bankingInfo };
    }
  )
  .get('/admin/banking-info/:hostId/default',
    async ({ params }) => {
      const defaultAccount = await HostBankingService.getDefaultBankAccount(params.hostId);
      return { success: true, data: defaultAccount };
    }
  );
