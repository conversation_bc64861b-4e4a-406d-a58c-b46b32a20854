'use server';
import { cookies } from "next/headers";
import { headers } from "next/headers";
import { cache } from "react";
import { betterFetch } from "@better-fetch/fetch";
import env from "@/constants/env";
import { sendLogToLogflare } from "@/lib/log-requests";
import { getCookieServerByName } from './cookies';
import { BEARER_COOKIE_NAME } from '@/constants';
import { Session } from 'better-auth/types';
import { redirect } from 'next/navigation';

type Config = {
  // redirect?: boolean;
  shouldRedirect?: boolean;
}

export const getServerSession = cache(async (name?: string, { shouldRedirect = true }: Config = {}) => {
  
  const headersList = await headers();
  const sessionHeader = headersList.get('session');

  if (sessionHeader) {
    const session = JSON.parse(sessionHeader);
    if (session && session.user){
      return session as { user: User, session: Session };
    }
  }
  
  const cookiesStore = await cookies();

  const allCookies = cookiesStore.toString();
  try {

    const token = await getCookieServerByName({
      name: BEARER_COOKIE_NAME,
    });


    const { data: session } = await betterFetch<{ user: User, session: Session }>(
      "/api/auth/get-session",
      {
        baseURL: env.NEXT_PUBLIC_API_URL,
        headers: {
          cookie: allCookies,
          'x-function-call': `${name}`
        },
        auth: {
          type: 'Bearer',
          token: () => {
            if (token) {
              return token; // No truncar el token
            }
          }
        }
      },
    );
    // console.log('Session on [getServerSession]: ', session);

    if (token && !session) {
      // remove token from cookie
      cookiesStore.delete(BEARER_COOKIE_NAME);
    }

    // log session using sendLogToLogflare
    sendLogToLogflare({
      message: 'Session on [getServerSession]',
      metadata: {
        session: session
      }
    });

    // if (!session) return redirect('/sign-in');
    if (!session && shouldRedirect) return redirect('/sign-in');


  return session as NonNullable<typeof session>;
} catch (error) {
  console.error(`Error getting session on [${name}]`, error);
    // return null;
    throw error;
}
});