import { getCurrentUserIdFromRequestContext } from '@/app/context/request-context';
import { Prisma, PrismaClient } from '@prisma/client'

declare global {
  var prisma: typeof prismaClient | undefined
}

const prismaClient = new PrismaClient({
  // log: ['query', 'info', 'warn', 'error']
  // log: env.NODE_ENV === 'development' ? ["query", "info", "warn", "error"] : ["error"]
}).$extends({
  model: {
    $allModels: {
      // Sobrescribir el método delete para hacer soft delete
      async softDelete<T>(this: T, where: any) {
        const context = Prisma.getExtensionContext(this);

        return (context as any).update({
          where,
          data: {
            deletedAt: Math.floor(Date.now() / 1000) // Unix timestamp
          }
        });
      },

      // Método para encontrar solo registros no eliminados
      async findManyActive<T>(this: T, args?: any) {
        const context = Prisma.getExtensionContext(this);

        return (context as any).findMany({
          ...args,
          where: {
            ...args?.where,
            deletedAt: null
          }
        });
      },

      // Método para restaurar registros eliminados
      async restore<T>(this: T, where: any) {
        const context = Prisma.getExtensionContext(this);

        return (context as any).update({
          where,
          data: {
            deletedAt: null
          }
        });
      }
    }
  },

  query: {
    $allModels: {
      // Interceptar findMany para excluir eliminados por defecto
      async findMany({ args, query }) {
        // Solo aplicar filtro si no se especifica explícitamente deletedAt
        if (!args.where?.deletedAt && !args.where?.OR?.some((condition: any) => 'deletedAt' in condition)) {
          args.where = {
            ...args.where,
            deletedAt: null
          } as any;
        }

        return query(args);
      },

      // Interceptar findUnique para excluir eliminados
      async findUnique({ args, query }) {
        if (!args.where?.deletedAt) {
          args.where = {
            ...args.where,
            deletedAt: null
          } as any;
        }

        return query(args);
      },

      // Interceptar findFirst para excluir eliminados
      async findFirst({ args, query }) {
        if (!args.where?.deletedAt) {
          args.where = {
            ...args.where,
            deletedAt: null
          } as any;
        }

        return query(args);
      }
    }
  }
}).$extends({
  query: {
    // Interceptar todas las operaciones en todos los modelos
    $allModels: {
      async create({ model, operation, args, query }) {
        // Ejecutar la operación original
        const result = await query(args);

        // Registrar en historial después de la creación
        await prisma.historyMovement.create({
          data: {
            entityType: model,
            entityId: result.id,
            action: 'create',
            userId: args.data.userId || getCurrentUserIdFromRequestContext(), // Necesitas obtener el userId del contexto
            details: `${model} created`,
            timestamp: new Date()
          }
        });

        return result;
      },

      async update({ model, operation, args, query }) {
        // Obtener datos actuales antes de la actualización
        const currentData = await prisma[model.toLowerCase()].findUnique({
          where: args.where
        });

        // Ejecutar la operación original
        const result = await query(args);

        // Calcular cambios
        const changes = {};
        Object.keys(args.data).forEach(key => {
          if (args.data[key] !== currentData[key]) {
            changes[key] = {
              oldValue: currentData[key],
              newValue: args.data[key]
            };
          }
        });

        // Registrar en historial
        await prisma.historyMovement.create({
          data: {
            entityType: model,
            entityId: result.id,
            action: 'update',
            userId: getCurrentUserIdFromRequestContext(),
            details: `${model} updated`,
            changes: changes,
            timestamp: new Date()
          }
        });

        return result;
      },

      async delete({ model, operation, args, query }) {
        // Obtener datos antes de eliminar
        const dataToDelete = await prisma[model.toLowerCase()].findUnique({
          where: args.where
        });

        // Ejecutar la operación original
        const result = await query(args);

        // Registrar en historial
        await prisma.historyMovement.create({
          data: {
            entityType: model,
            entityId: dataToDelete.id,
            action: 'delete',
            // userId: getCurrentUserIdFromRequestContext(),
            details: `${model} deleted`,
            changes: { deletedData: dataToDelete },
            timestamp: new Date()
          }
        });

        return result;
      }
    }
  }
});

/* // lib/prisma-with-audit.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient().$extends({
  query: {
    // Interceptar todas las operaciones en todos los modelos
    $allModels: {
      async create({ model, operation, args, query }) {
        // Ejecutar la operación original
        const result = await query(args);
        
        // Registrar en historial después de la creación
        await prisma.historyMovement.create({
          data: {
            entityType: model,
            entityId: result.id,
            action: 'create',
            userId: args.data.userId || getCurrentUserIdFromRequestContext(), // Necesitas obtener el userId del contexto
            details: `${model} created`,
            timestamp: new Date()
          }
        });
        
        return result;
      },
      
      async update({ model, operation, args, query }) {
        // Obtener datos actuales antes de la actualización
        const currentData = await prisma[model.toLowerCase()].findUnique({
          where: args.where
        });
        
        // Ejecutar la operación original
        const result = await query(args);
        
        // Calcular cambios
        const changes = {};
        Object.keys(args.data).forEach(key => {
          if (args.data[key] !== currentData[key]) {
            changes[key] = {
              oldValue: currentData[key],
              newValue: args.data[key]
            };
          }
        });
        
        // Registrar en historial
        await prisma.historyMovement.create({
          data: {
            entityType: model,
            entityId: result.id,
            action: 'update',
            userId: getCurrentUserIdFromRequestContext(),
            details: `${model} updated`,
            changes: changes,
            timestamp: new Date()
          }
        });
        
        return result;
      },
      
      async delete({ model, operation, args, query }) {
        // Obtener datos antes de eliminar
        const dataToDelete = await prisma[model.toLowerCase()].findUnique({
          where: args.where
        });
        
        // Ejecutar la operación original
        const result = await query(args);
        
        // Registrar en historial
        await prisma.historyMovement.create({
          data: {
            entityType: model,
            entityId: dataToDelete.id,
            action: 'delete',
            userId: getCurrentUserIdFromRequestContext(),
            details: `${model} deleted`,
            changes: { deletedData: dataToDelete },
            timestamp: new Date()
          }
        });
        
        return result;
      }
    }
  }
});

export { prisma }; */


export const prisma = global.prisma || prismaClient;

if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma
}


