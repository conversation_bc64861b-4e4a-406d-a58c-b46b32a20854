import { Prisma, PrismaClient } from '@prisma/client'

declare global {
  var prisma: typeof prismaClient | undefined
}

const prismaClient = new PrismaClient({
  // log: ['query', 'info', 'warn', 'error']
  // log: env.NODE_ENV === 'development' ? ["query", "info", "warn", "error"] : ["error"]
}).$extends({
  model: {
    $allModels: {
      // Sobrescribir el método delete para hacer soft delete
      async softDelete<T>(this: T, where: any) {
        const context = Prisma.getExtensionContext(this);

        return (context as any).update({
          where,
          data: {
            deletedAt: new Date()
          }
        });
      },

      // Método para encontrar solo registros no eliminados
      async findManyActive<T>(this: T, args?: any) {
        const context = Prisma.getExtensionContext(this);

        return (context as any).findMany({
          ...args,
          where: {
            ...args?.where,
            deletedAt: null
          }
        });
      },

      // Método para restaurar registros eliminados
      async restore<T>(this: T, where: any) {
        const context = Prisma.getExtensionContext(this);

        return (context as any).update({
          where,
          data: {
            deletedAt: null
          }
        });
      }
    }
  },

  query: {
    $allModels: {
      // Interceptar findMany para excluir eliminados por defecto
      async findMany({ model, operation, args, query }) {
        // Solo aplicar filtro si no se especifica explícitamente deletedAt
        // if (!args.where?.deletedAt) {
        //   args.where = {
        //     ...args.where,
        //     deletedAt: null
        //   };
        // }

        return query(args);
      },

      // Interceptar findUnique para excluir eliminados
      async findUnique({ model, operation, args, query }) {
        // if (!args.where?.deletedAt) {
        //   args.where = {
        //     ...args.where,
        //     deletedAt: null
        //   };
        // }

        return query(args);
      }
    }
  }
});

export const prisma = global.prisma || prismaClient;
prisma.user.softDelete({ id: '1' });
if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma
}

/* 
okey perfecto, dame de nuevo todas las funciones para encriptar y desencriptar datos (solamente, sin incluir lo de los metodos de pago con la db, las puras clases con la funcionalidad) utilizando aws kms
*/
