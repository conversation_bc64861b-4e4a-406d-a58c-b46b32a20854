"use client"

import { Car, Calendar, CreditCard } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { clientTransactionsApi } from "@/lib/api/transactions.api"
import { reservationsApi } from "@/lib/api/reservations.api"

interface DashboardStats {
  activeReservations: number;
  totalRentals: number;
  totalSpent: number;
}

export function ClientDashboardStats() {
  const [stats, setStats] = useState<DashboardStats>({
    activeReservations: 0,
    totalRentals: 0,
    totalSpent: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Obtener reservas activas
        const reservationsResponse = await reservationsApi.client.getReservations();

        // Obtener estadísticas de pagos
        const transactionsResponse = await clientTransactionsApi.getPaymentStats();

        if (reservationsResponse && transactionsResponse.success) {
          const activeReservations = reservationsResponse.filter(r =>
            r.status === 'confirmed' && new Date(r.startDate) > new Date()
          ).length;

          setStats({
            activeReservations,
            totalRentals: transactionsResponse.data?.stats?.successfulPayments || 0,
            totalSpent: transactionsResponse.data?.stats?.totalSpent || 0
          });
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Reservas Activas</CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.activeReservations}</div>
          <p className="text-xs text-muted-foreground">
            {stats.activeReservations === 1 ? 'reserva activa' : 'reservas activas'}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Rentas</CardTitle>
          <Car className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalRentals}</div>
          <p className="text-xs text-muted-foreground">
            {stats.totalRentals === 1 ? 'renta completada' : 'rentas completadas'}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Gasto Total</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            ${stats.totalSpent.toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
          </div>
          <p className="text-xs text-muted-foreground">
            total gastado en rentas
          </p>
        </CardContent>
      </Card>

    </div>
  )
}
