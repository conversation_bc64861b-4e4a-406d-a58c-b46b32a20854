"use client"

import { ClientSearchFilters } from "@/components/client/client-search-filters"
import { ClientVehicleGrid } from "@/components/client/client-vehicle-grid"
import { useState } from "react"

export default function ClientSearchPage() {
  const [filters, setFilters] = useState({})

  const handleFiltersChange = (newFilters: any) => {
    setFilters(newFilters)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Buscar Vehículos</h1>
        <p className="text-muted-foreground">Encuentra el vehículo perfecto para tu próximo viaje.</p>
      </div>

      <ClientSearchFilters onFiltersChange={handleFiltersChange} />

      <div className="w-full">
        <ClientVehicleGrid filters={filters} />
      </div>
    </div>
  )
}
