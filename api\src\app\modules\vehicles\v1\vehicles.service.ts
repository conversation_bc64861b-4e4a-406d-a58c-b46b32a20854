import { prisma } from "@/lib/prisma";
import { HttpException } from "@/exceptions/HttpExceptions";
import { deleteFileFromR2, uploadVehicleFilesToR2 } from '@/services/r2';
import { DateTime } from 'luxon';

export class VehiclesService {
  // Obtener todos los vehículos (público)
  static async getAllPublic({
    query = {
      page: 1,
      limit: 10
    }
  }: {
    query: {
      page: number;
      limit: number;
      location?: string;
      state?: string;
      startDate?: string;
      endDate?: string;
      passengers?: number;
      bodyType?: string;
      priceMin?: number;
      priceMax?: number;
      transmission?: string;
      amenities?: string[];
      search?: string;
    }
  }) {

    const { page, limit, location, state, startDate, endDate, passengers, bodyType, priceMin, priceMax, transmission, amenities, search } = query;

    // Construir filtros dinámicos
    const whereClause: any = {
      status: "active"
    };

    // Filtro por búsqueda general (marca, modelo)
    if (search) {
      whereClause.OR = [
        { make: { contains: search, mode: 'insensitive' } },
        { model: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Filtro por ubicación (legacy)
    if (location) {
      whereClause.features = {
        path: ['location'],
        string_contains: location
      };
    }

    // Filtro por estado
    if (state) {
      whereClause.state_code = state;
    }

    // Filtro por tipo de carrocería
    if (bodyType) {
      whereClause.bodyType = bodyType;
    }

    // Filtro por transmisión
    if (transmission) {
      whereClause.transmission = transmission;
    }

    // Filtro por amenities
    if (amenities && amenities.length > 0 && !amenities.includes('[]')) {
      whereClause.amenities = {
        hasEvery: amenities
      };
    }

    // Filtro por precio
    if (priceMin !== undefined || priceMax !== undefined) {
      whereClause.price = {};
      if (priceMin !== undefined) {
        whereClause.price.gte = priceMin;
      }
      if (priceMax !== undefined) {
        whereClause.price.lte = priceMax;
      }
    }

    // Filtro por número de asientos (pasajeros)
    if (passengers) {
      whereClause.AND = whereClause.AND || [];
      whereClause.AND.push({
        features: {
          path: ['seats'],
          gte: passengers
        }
      });
    }

    // Filtro por disponibilidad de fechas
    let vehicleIdsToExclude: string[] = [];
    if (startDate && endDate) {
      const startDateTime = new Date(startDate);
      const endDateTime = new Date(endDate);

      // Buscar reservaciones que se solapen con las fechas solicitadas
      const conflictingReservations = await prisma.vehicleReservation.findMany({
        where: {
          status: {
            in: ['pending', 'confirmed', 'completed']
          },
          OR: [
            {
              // Reservación que empieza antes y termina después del inicio solicitado
              startDate: { lte: startDateTime },
              endDate: { gt: startDateTime }
            },
            {
              // Reservación que empieza antes del final solicitado y termina después
              startDate: { lt: endDateTime },
              endDate: { gte: endDateTime }
            },
            {
              // Reservación completamente dentro del rango solicitado
              startDate: { gte: startDateTime },
              endDate: { lte: endDateTime }
            }
          ]
        },
        select: {
          vehicleId: true
        }
      });

      vehicleIdsToExclude = conflictingReservations.map(r => r.vehicleId);

      if (vehicleIdsToExclude.length > 0) {
        whereClause.id = {
          notIn: vehicleIdsToExclude
        };
      }
    }

    const vehicles = await prisma.vehicle.findMany({
      where: whereClause,
      select: {
        id: true,
        make: true,
        model: true,
        year: true,
        color: true,
        price: true,
        images: true,
        features: true,
        description: true,
        amenities: true,
        createdAt: true,
        host: {
          select: {
            id: true,
            name: true,
            image: true,
            createdAt: true
          }
        },
        vehicleReviews: {
          select: {
            rating: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: (page - 1) * limit
    });

    const totalVehicles = await prisma.vehicle.count({
      where: whereClause
    });

    const totalPages = Math.ceil(totalVehicles / limit);

    // Procesar vehículos para agregar estadísticas de reseñas
    const vehiclesWithStats = vehicles.map(vehicle => {
      const reviews = vehicle.vehicleReviews;
      const totalReviews = reviews.length;
      const averageRating = totalReviews > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
        : 0;

      // Remover vehicleReviews del objeto y agregar las estadísticas calculadas
      const { vehicleReviews, ...vehicleData } = vehicle;

      return {
        ...vehicleData,
        totalReviews,
        averageRating: averageRating > 0 ? Number(averageRating.toFixed(1)) : 0
      };
    });



    return {
      data: vehiclesWithStats,
      pagination: {
        total: totalVehicles,
        currentPage: page,
        totalPages,
        from: (page - 1) * limit + 1,
        to: Math.min(page * limit, totalVehicles)
      }
    };
  }

  // Obtener un vehículo por ID (público)
  static async getByIdPublic(id: string) {
    const vehicle = await prisma.vehicle.findUnique({
      where: { id, status: "active" },
      include: {
        host: {
          select: {
            id: true,
            name: true,
            image: true,
            createdAt: true
          }
        },
        vehicleReviews: {
          select: {
            rating: true
          }
        }
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Calcular estadísticas de reseñas
    const reviews = vehicle.vehicleReviews;
    const totalReviews = reviews.length;
    const averageRating = totalReviews > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
      : 0;

    // Remover vehicleReviews del objeto y agregar las estadísticas calculadas
    const { vehicleReviews, ...vehicleData } = vehicle;

    return {
      ...vehicleData,
      totalReviews,
      averageRating: averageRating > 0 ? Number(averageRating.toFixed(1)) : 0
    };
  }

  // Obtener todos los vehículos (admin)
  static async getAllForAdmin({
    query = {
      page: 1,
      limit: 10
    }
  }: {
    query: {
      page: number;
      limit: number;
    }
  }) {
    const vehicles = await prisma.vehicle.findMany({
      include: {
        host: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: query.limit,
      skip: (query.page - 1) * query.limit
    });

    const totalVehicles = await prisma.vehicle.count();
    const totalPages = Math.ceil(totalVehicles / query.limit);

    return {
      data: vehicles,
      pagination: {
        total: totalVehicles,
        currentPage: query.page,
        totalPages,
        from: (query.page - 1) * query.limit + 1,
        to: Math.min(query.page * query.limit, totalVehicles)
      }
    }
  }

  // Obtener vehículos por host ID
  static async getByHostId(hostId: string, {
    query = {
      page: 1,
      limit: 10
    }
  }: {
    query: {
      page: number;
      limit: number;
    }
  }) {

    const vehicles = await prisma.vehicle.findMany({
      where: {
        hostId
      },
      include: {
        vehicleReviews: {
          select: {
            rating: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: query.limit,
      skip: (query.page - 1) * query.limit
    });

    const totalVehicles = await prisma.vehicle.count({
      where: {
        hostId
      }
    });

    const totalPages = Math.ceil(totalVehicles / query.limit);

    // Procesar vehículos para agregar estadísticas de reseñas
    const vehiclesWithStats = vehicles.map(vehicle => {
      const reviews = vehicle.vehicleReviews;
      const totalReviews = reviews.length;
      const averageRating = totalReviews > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
        : 0;

      // Remover vehicleReviews del objeto y agregar las estadísticas calculadas
      const { vehicleReviews, ...vehicleData } = vehicle;

      return {
        ...vehicleData,
        totalReviews,
        averageRating: averageRating > 0 ? Number(averageRating.toFixed(1)) : 0
      };
    });

    return {
      data: vehiclesWithStats,
      pagination: {
        total: totalVehicles,
        currentPage: query.page,
        totalPages,
        from: (query.page - 1) * query.limit + 1,
        to: Math.min(query.page * query.limit, totalVehicles)
      }
    }
  }

  // Crear un nuevo vehículo
  static async create(data: any, hostId: string) {
    // Preparar datos para guardar
    const vehicleData = {
      ...data,
      hostId,
      features: data.features || {},
      amenities: data.amenities || [],
      unavailableDates: data.unavailableDates || []
    };

    const vehicle = await prisma.vehicle.create({
      data: vehicleData
    });
    return vehicle;
  }

  static async uploadFiles({
    vehicleId,
    files,
    userId,
  }: {
    vehicleId: string;
    files: {
      images?: File[];
      plateDocument?: File[];
      vinDocument?: File[];
      registrationDocument?: File[];
      insurancePolicyDocument?: File[];
    };
    userId: string;
  }) {
    console.log('Uploading files for vehicle with ID:', vehicleId);
    console.log('Incoming  Files:', files);
    console.log('User ID:', userId);

    const vehicle = await prisma.vehicle.findUnique({
      where: { id: vehicleId },
      select: {
        id: true,
        hostId: true,
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    if (vehicle.hostId !== userId) {
      throw HttpException.Forbidden("You don't have permission to upload files for this vehicle");
    }

    const fileUrls = await uploadVehicleFilesToR2({
      vehicleId,
      userId,
      files,
    });
    console.log('File URLs:', fileUrls);

    // Actualizar el vehículo con las URLs de las imágenes
    await prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        images: fileUrls.imageUrls || [],
      }
    });

    // Crear el registro de documentos
    if (fileUrls.plateDocumentUrl || fileUrls.vinDocumentUrl ||
      fileUrls.registrationDocumentUrl || fileUrls.insurancePolicyDocumentUrl) {

      await prisma.vehicleDocument.create({
        data: {
          vehicleId,
          plateDocument: fileUrls.plateDocumentUrl,
          vinDocument: fileUrls.vinDocumentUrl,
          registrationDocument: fileUrls.registrationDocumentUrl,
          insurancePolicyDocument: fileUrls.insurancePolicyDocumentUrl,
        }
      });
    }
    return vehicle;
  }

  // static async updateFiles({
  //   vehicleId,
  //   files,
  //   userId,
  // }: {
  //   vehicleId: string;
  //   files: {
  //     images?: File[];
  //     plateDocument?: File[];
  //     vinDocument?: File[];
  //     registrationDocument?: File[];
  //     insurancePolicyDocument?: File[];
  //   };
  //   userId: string;
  // }) {
  //   console.log('Updating files for vehicle with ID:', vehicleId);
  //   console.log('Incoming  Files:', files);
  //   console.log('User ID:', userId);

  //   const vehicleDocuments = await prisma.vehicleDocument.findUnique({
  //     where: { vehicleId },
  //     select: {
  //       id: true,
  //       plateDocument: files.plateDocument?.length ? true : false,
  //       vinDocument: files.vinDocument?.length ? true : false,
  //       registrationDocument: files.registrationDocument?.length ? true : false,
  //       insurancePolicyDocument: files.insurancePolicyDocument?.length ? true : false,
  //       vehicle: {
  //         select: {
  //           id: true,
  //           hostId: true
  //         }
  //       }
  //     }
  //   });

  //   if (!vehicleDocuments) {
  //     throw HttpException.NotFound("Vehicle not found");
  //   }

  //   if (vehicleDocuments.vehicle.hostId !== userId) {
  //     throw HttpException.Forbidden("You don't have permission to update files for this vehicle");
  //   }

  //   const fileUrls = await uploadVehicleFilesToR2({
  //     vehicleId,
  //     userId,
  //     files,
  //   });

  //   // Actualizar el vehículo con las URLs de las imágenes
  //   // Evita hacer multiples update, solo los campos que se actualizarán
  //   const updateData: any = {};
  //   if (fileUrls.imageUrls) {
  //     await prisma.vehicle.update({
  //       where: { id: vehicleId },
  //       data: {
  //         images: fileUrls.imageUrls
  //       }
  //     });
  //   }
  //   if (fileUrls.plateDocumentUrl) {
  //     // Debería borrar el archivo anterior
  //     if (vehicleDocuments.plateDocument) {
  //       await deleteFileFromR2({
  //         path: vehicleDocuments.plateDocument,
  //         isPublic: false,
  //       });
  //     }
  //     updateData.plateDocument = fileUrls.plateDocumentUrl;
  //   }
  //   if (fileUrls.vinDocumentUrl) {
  //     if (vehicleDocuments.vinDocument) {
  //       await deleteFileFromR2({
  //         path: vehicleDocuments.vinDocument,
  //         isPublic: false,
  //       });
  //     }
  //     updateData.vinDocument = fileUrls.vinDocumentUrl;
  //   }
  //   if (fileUrls.registrationDocumentUrl) {
  //     if (vehicleDocuments.registrationDocument) {
  //       await deleteFileFromR2({
  //         path: vehicleDocuments.registrationDocument,
  //         isPublic: false,
  //       });
  //     }
  //     updateData.registrationDocument = fileUrls.registrationDocumentUrl;
  //   }
  //   if (fileUrls.insurancePolicyDocumentUrl) {
  //     if (vehicleDocuments.insurancePolicyDocument) {
  //       await deleteFileFromR2({
  //         path: vehicleDocuments.insurancePolicyDocument,
  //         isPublic: false,
  //       });
  //     }
  //     updateData.insurancePolicyDocument = fileUrls.insurancePolicyDocumentUrl;
  //   }

  //   await prisma.vehicleDocument.update({
  //     where: { vehicleId },
  //     data: updateData
  //   });

  //   return vehicleDocuments.vehicle;

  // }

  // Actualizar un vehículo

  static async updateFiles({
    vehicleId,
    files,
    userId,
    imagesToRemove = [],
  }: {
    vehicleId: string;
    files: {
      images?: File[];
      plateDocument?: File[];
      vinDocument?: File[];
      registrationDocument?: File[];
      insurancePolicyDocument?: File[];
    };
    userId: string;
    imagesToRemove?: string[];
  }) {

    // Obtener el vehículo y sus documentos
    const vehicle = await prisma.vehicle.findUnique({
      where: { id: vehicleId },
      select: {
        id: true,
        hostId: true,
        images: true,
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    if (vehicle.hostId !== userId) {
      throw HttpException.Forbidden("You don't have permission to update files for this vehicle");
    }

    const vehicleDocuments = await prisma.vehicleDocument.findUnique({
      where: { vehicleId },
      select: {
        id: true,
        plateDocument: files.plateDocument?.length ? true : false,
        vinDocument: files.vinDocument?.length ? true : false,
        registrationDocument: files.registrationDocument?.length ? true : false,
        insurancePolicyDocument: files.insurancePolicyDocument?.length ? true : false,
      }
    });

    // Check if total of images is more than 3 and less than 10
    // if is not between that range, throw an error:

    if (files.images) {
      const totalImages = vehicle.images.length + files.images.length - imagesToRemove.length;
      if (totalImages < 3) {
        throw HttpException.BadRequest("Debe tener al menos 3 imagenes");
      }
      if (totalImages > 10) {
        throw HttpException.BadRequest("No puede tener mas de 10 imagenes");
      }
    }

    // Subir nuevos archivos
    const fileUrls = await uploadVehicleFilesToR2({
      vehicleId,
      userId,
      files,
    });

    // Manejar imágenes
    let updatedImages = [...(vehicle.images || [])];


    // Eliminar imágenes seleccionadas
    if (imagesToRemove && imagesToRemove.length > 0) {
      // Eliminar archivos de R2
      for (const imageUrl of imagesToRemove) {
        await deleteFileFromR2({
          path: imageUrl,
          isPublic: true,
        });
      }

      // Filtrar las imágenes eliminadas
      updatedImages = updatedImages.filter(img => !imagesToRemove.includes(img));
    }

    // Agregar nuevas imágenes
    if (fileUrls.imageUrls && fileUrls.imageUrls.length > 0) {
      updatedImages = [...updatedImages, ...fileUrls.imageUrls];
    }

    // Actualizar el vehículo con las URLs de las imágenes
    await prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        images: updatedImages,
      }
    });

    // Actualizar documentos
    const updateData: any = {};

    if (fileUrls.plateDocumentUrl) {
      if (vehicleDocuments?.plateDocument) {
        await deleteFileFromR2({
          path: vehicleDocuments.plateDocument,
          isPublic: false,
        });
      }
      updateData.plateDocument = fileUrls.plateDocumentUrl;
    }

    if (fileUrls.vinDocumentUrl) {
      if (vehicleDocuments?.vinDocument) {
        await deleteFileFromR2({
          path: vehicleDocuments.vinDocument,
          isPublic: false,
        });
      }
      updateData.vinDocument = fileUrls.vinDocumentUrl;
    }

    if (fileUrls.registrationDocumentUrl) {
      if (vehicleDocuments?.registrationDocument) {
        await deleteFileFromR2({
          path: vehicleDocuments.registrationDocument,
          isPublic: false,
        });
      }
      updateData.registrationDocument = fileUrls.registrationDocumentUrl;
    }

    if (fileUrls.insurancePolicyDocumentUrl) {
      if (vehicleDocuments?.insurancePolicyDocument) {
        await deleteFileFromR2({
          path: vehicleDocuments.insurancePolicyDocument,
          isPublic: false,
        });
      }
      updateData.insurancePolicyDocument = fileUrls.insurancePolicyDocumentUrl;
    }

    // Actualizar o crear documentos según sea necesario
    if (Object.keys(updateData).length > 0) {
      if (vehicleDocuments) {
        await prisma.vehicleDocument.update({
          where: { vehicleId },
          data: updateData
        });
      } else {
        await prisma.vehicleDocument.create({
          data: {
            vehicleId,
            ...updateData
          }
        });
      }
    }

    return vehicle;
  }

  static async update(id: string, data: any, userId: string, isAdmin: boolean) {
    // Verificar si el vehículo existe y pertenece al usuario
    const vehicle = await prisma.vehicle.findUnique({
      where: { id }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Si no es admin, verificar que el vehículo pertenezca al usuario
    if (!isAdmin && vehicle.hostId !== userId) {
      throw HttpException.Forbidden("You don't have permission to update this vehicle");
    }

    return await prisma.vehicle.update({
      where: { id },
      data
    });
  }

  // Eliminar un vehículo
  static async delete(id: string, userId: string, isAdmin: boolean) {
    // Verificar si el vehículo existe y pertenece al usuario
    const vehicle = await prisma.vehicle.findUnique({
      where: { id }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Si no es admin, verificar que el vehículo pertenezca al usuario
    if (!isAdmin && vehicle.hostId !== userId) {
      throw HttpException.Forbidden("You don't have permission to delete this vehicle");
    }

    return await prisma.vehicle.delete({
      where: { id }
    });
  }

  // Cambiar el estado de un vehículo
  static async updateStatus(id: string, status: string, userId: string, isAdmin: boolean) {
    // Verificar si el vehículo existe y pertenece al usuario
    const vehicle = await prisma.vehicle.findUnique({
      where: { id }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Si no es admin, verificar que el vehículo pertenezca al usuario
    if (!isAdmin && vehicle.hostId !== userId) {
      throw HttpException.Forbidden("You don't have permission to update this vehicle");
    }

    return await prisma.vehicle.update({
      where: { id },
      data: { status }
    });
  }

  // Añadir un método para obtener un vehículo por ID para el host
  static async getByIdForHost(id: string, hostId: string) {
    const vehicle = await prisma.vehicle.findFirst({
      where: {
        id,
        hostId
      },
      include: {
        host: {
          select: {
            id: true,
            name: true,
            image: true,
            createdAt: true
          }
        },
        vehicleReviews: {
          select: {
            rating: true
          }
        }
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Calcular estadísticas de reseñas
    const reviews = vehicle.vehicleReviews;
    const totalReviews = reviews.length;
    const averageRating = totalReviews > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
      : 0;

    // Remover vehicleReviews del objeto y agregar las estadísticas calculadas
    const { vehicleReviews, ...vehicleData } = vehicle;

    return {
      ...vehicleData,
      totalReviews,
      averageRating: averageRating > 0 ? Number(averageRating.toFixed(1)) : 0
    };
  }

  // Obtener vehículos pendientes de aprobación (admin)
  static async getPendingApprovalVehicles() {
    return await prisma.vehicle.findMany({
      where: {
        status: "pending"
      },
      include: {
        host: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc' // Ordenar por más antiguo primero
      }
    });
  }

  // Aprobar un vehículo
  static async approveVehicle(id: string, userId: string) {
    const vehicle = await prisma.vehicle.findUnique({
      where: { id }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Solo aprobar si el estatus está en pendiente o rechazado
    if (vehicle.status !== "pending" && vehicle.status !== "rejected") {
      throw HttpException.BadRequest("Vehicle is not in pending status");
    }

    return await prisma.vehicle.update({
      where: { id },
      data: {
        status: "active",
        approvalHistory: {
          push: {
            action: "approved",
            date: new Date(),
            reason: null,
            userId,
          }
        }
      }
    });
  }

  // Rechazar un vehículo con razón
  static async rejectVehicle(id: string, reason: string, userId: string) {
    const vehicle = await prisma.vehicle.findUnique({
      where: { id }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    return await prisma.vehicle.update({
      where: { id },
      data: {
        status: "rejected",
        approvalHistory: {
          push: {
            action: "rejected",
            date: new Date(),
            reason: reason,
            userId,
          }
        }
      }
    });
  }

  // Obtener un vehículo por ID (para administradores)
  static async getByIdForAdmin(id: string) {
    const vehicle = await prisma.vehicle.findUnique({
      where: { id },
      include: {
        host: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        availability: true
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // add the user name and email of the rejected and approved actions

    // return vehicle;

    let approvalHistoryEnriched: any[] = [];

    await Promise.all(vehicle.approvalHistory.map(async (item: any) => {
      if (item.userId) {
        const user = await prisma.user.findUnique({
          where: { id: item.userId },
          select: {
            id: true,
            name: true,
            email: true,
          }
        });
        return approvalHistoryEnriched.push({
          ...item,
          user: user || {
            id: null,
            name: 'No encontrado',
            email: 'No encontrado',
          }
        });
      }

      return approvalHistoryEnriched.push(item);

    }));

    return {
      ...vehicle,
      approvalHistory: approvalHistoryEnriched
    };

  }

  static async getStatsByHostId(hostId: string) {
    // Obtener todos los vehículos del host (sin paginación)
    const vehicles = await prisma.vehicle.findMany({
      where: {
        hostId
      },
      include: {
        host: {
          select: {
            id: true,
            name: true,
            email: true,
            // phone: true
          }
        },
        availability: true,
        vehicleReviews: {
          select: {
            rating: true
          }
        }
      }
    });

    // Calcular estadísticas
    const totalVehicles = vehicles.length;
    const activeVehicles = vehicles.filter(v => v.status === "active").length;
    const maintenanceVehicles = vehicles.filter(v => v.status === "maintenance").length;

    // Calcular promedio de calificación de todos los vehículos
    let totalRatings = 0;
    let totalReviewsCount = 0;

    vehicles.forEach(vehicle => {
      const reviews = vehicle.vehicleReviews;
      if (reviews.length > 0) {
        const vehicleRatingSum = reviews.reduce((sum, review) => sum + review.rating, 0);
        totalRatings += vehicleRatingSum;
        totalReviewsCount += reviews.length;
      }
    });

    const averageRating = totalReviewsCount > 0 ? (totalRatings / totalReviewsCount) : 0;

    // Calcular ingresos (esto podría requerir datos adicionales de reservas)
    // Por ahora usamos un valor de placeholder como en el frontend
    // const averageIncome = 850;

    // Calcular ingresos reales del mes actual

    const today = DateTime.now();
    const startDate = today.startOf('month').toJSDate();
    const endDate = today.endOf('month').toJSDate();


    const reservations = await prisma.vehicleReservation.findMany({
      where: {
        vehicle: {
          hostId
        },
        startDate: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        vehicle: {
          select: {
            price: true
          }
        }
      }
    });
    // console.log('Reservations:', reservations);

    // const totalIncome = reservations.reduce((sum, reservation) => sum + reservation.vehicle.price, 0);
    // every reservation has a totalPrice
    const totalIncome = reservations.reduce((sum, reservation) => sum + reservation.totalPrice, 0);
    const averageIncome = totalIncome / reservations.length;

    const totalReservations = reservations.length;

    return {
      totalVehicles,
      totalReservations,
      activeVehicles,
      maintenanceVehicles,
      averageRating,
      averageIncome
    }
  }

  static async getVehicleDocuments({
    vehicleId,
    userId,
    isAdmin = false,
  }: {
    vehicleId: string;
    userId: string;
    isAdmin?: boolean;
  }) {

    // Obtener el vehículo para verificar permisos
    const vehicle = await prisma.vehicle.findUnique({
      where: { id: vehicleId },
      select: {
        id: true,
        hostId: true,
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Verificar permisos
    if (!isAdmin && vehicle.hostId !== userId) {
      throw HttpException.Forbidden("You don't have permission to access documents for this vehicle");
    }

    // Obtener documentos del vehículo
    const documents = await prisma.vehicleDocument.findUnique({
      where: { vehicleId },
      select: {
        id: true,
        plateDocument: true,
        vinDocument: true,
        registrationDocument: true,
        insurancePolicyDocument: true,
      }
    });
    console.log('Documents:', documents);

    if (!documents) {
      // Si no hay documentos, devolver objeto vacío
      return {
        plateDocument: null,
        vinDocument: null,
        registrationDocument: null,
        insurancePolicyDocument: null,
      };
    }

    // Devolver los documentos
    return documents;
  }

  static async requestReview(id: string, userId: string) {
    const vehicle = await prisma.vehicle.findUnique({
      where: { id }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehicle not found");
    }

    // Verificar que el vehículo pertenezca al usuario
    if (vehicle.hostId !== userId) {
      throw HttpException.Forbidden("You don't have permission to request a review for this vehicle");
    }

    // Verificar que el vehículo esté en estado rechazado
    if (vehicle.status !== "rejected") {
      throw HttpException.BadRequest("Only rejected vehicles can request a new review");
    }

    return await prisma.vehicle.update({
      where: { id },
      data: {
        status: "pending",
        approvalHistory: {
          push: {
            action: "review_requested",
            date: new Date(),
            reason: null,
            userId,
          }
        }
      }
    });
  }

  // Método para obtener estadísticas para el administrador
  static async getStatsForAdmin() {
    // Obtener todos los vehículos
    const vehicles = await prisma.vehicle.findMany({
      include: {
        vehicleReviews: {
          select: {
            rating: true
          }
        }
      }
    });

    // Calcular estadísticas
    const total = vehicles.length;
    const active = vehicles.filter(v => v.status === "active").length;
    const pending = vehicles.filter(v => v.status === "pending").length;

    // Calcular promedio de calificación de todos los vehículos
    let totalRatings = 0;
    let totalReviewsCount = 0;

    vehicles.forEach(vehicle => {
      const reviews = vehicle.vehicleReviews;
      if (reviews.length > 0) {
        const vehicleRatingSum = reviews.reduce((sum, review) => sum + review.rating, 0);
        totalRatings += vehicleRatingSum;
        totalReviewsCount += reviews.length;
      }
    });

    const averageRating = totalReviewsCount > 0 ? (totalRatings / totalReviewsCount) : 0;

    // Obtener reservas para calcular ingresos
    const reservations = await prisma.vehicleReservation.findMany({
      where: {
        status: "confirmed"
      },
      include: {
        vehicle: true
      }
    });

    const totalIncome = reservations.reduce((sum, reservation) => sum + reservation.totalPrice, 0);
    const averageIncome = reservations.length > 0 ? totalIncome / reservations.length : 0;
    const totalReservations = reservations.length;

    return {
      stats: {
        total,
        active,
        pending,
        totalReservations,
        averageRating,
        averageIncome
      }
    };
  }
}

