"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DollarSign, TrendingUp, Calendar, CreditCard } from "lucide-react"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { hostTransactionsApi, type HostTransactionStats } from "@/lib/api/transactions.api"

export function HostEarningsStats() {
  const [statsFilter, setStatsFilter] = useState(/* "all-time" */"current-month")

  // Funciones para obtener rangos de fechas por mes
  const getMonthRange = (monthsBack: number) => {
    const now = new Date()
    const startDate = new Date(now.getFullYear(), now.getMonth() - monthsBack, 1)
    const endDate = new Date(now.getFullYear(), now.getMonth() - monthsBack + 1, 0, 23, 59, 59, 999)
    return { startDate, endDate }
  }

  const getStatsDateRange = () => {
    switch (statsFilter) {
      case "current-month":
        return getMonthRange(0)
      case "previous-month":
        return getMonthRange(1)
      case "2-months-ago":
        return getMonthRange(2)
      case "3-months-ago":
        return getMonthRange(3)
      case "all-time":
        // Para "todos los tiempos", usar un rango muy amplio
        const now = new Date()
        const startDate = new Date(2020, 0, 1) // Desde enero 2020
        const endDate = now
        return { startDate, endDate }
      default:
        return getMonthRange(0)
    }
  }

  const getPreviousMonthRange = () => {
    switch (statsFilter) {
      case "current-month":
        return getMonthRange(1)
      case "previous-month":
        return getMonthRange(2)
      case "2-months-ago":
        return getMonthRange(3)
      case "3-months-ago":
        return getMonthRange(4)
      case "all-time":
        // Para comparación con "todos los tiempos", usar el mes anterior
        return getMonthRange(1)
      default:
        return getMonthRange(1)
    }
  }

  // Query para obtener estadísticas del período seleccionado
  const { data: statsData, /* error: statsError */ } = useQuery({
    queryKey: ['host-transaction-stats', statsFilter],
    queryFn: async () => {
      const { startDate, endDate } = getStatsDateRange()
      const response = await hostTransactionsApi.getStats({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })
      return response;
    },
    staleTime: 60 * 1000, // 1 minuto
  })



  // Query para obtener estadísticas del período anterior (para comparación)
  const { data: previousStatsData } = useQuery({
    queryKey: ['host-transaction-stats-previous', statsFilter],
    queryFn: async () => {
      const { startDate, endDate } = getPreviousMonthRange()
      const response = await hostTransactionsApi.getStats({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })
      return response;
    },
    staleTime: 60 * 1000, // 1 minuto
  })


  const stats: HostTransactionStats = statsData || {
    totalEarnings: 0,
    totalAmount: 0,
    totalPlatformFees: 0,
    totalTransferred: 0,
    pendingTransfer: 0,
    totalTransactions: 0,
    averageCommissionRate: 12
  }

  const previousStats: HostTransactionStats = previousStatsData || {
    totalEarnings: 0,
    totalAmount: 0,
    totalPlatformFees: 0,
    totalTransferred: 0,
    pendingTransfer: 0,
    totalTransactions: 0,
    averageCommissionRate: 12
  }

  // Funciones para calcular cambios porcentuales
  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return Math.round(((current - previous) / previous) * 100)
  }

  const formatPercentageChange = (current: number, previous: number) => {
    const change = calculatePercentageChange(current, previous)
    const sign = change > 0 ? '+' : ''
    return `${sign}${change}%`
  }

  const getStatsFilterLabel = () => {
    switch (statsFilter) {
      case "current-month":
        return "mes pasado"
      case "previous-month":
        return "hace 2 meses"
      case "2-months-ago":
        return "hace 3 meses"
      case "3-months-ago":
        return "hace 4 meses"
      case "all-time":
        return "mes pasado"
      default:
        return "mes pasado"
    }
  }

  return (
    <div className="space-y-4">
      {/* Selector de período para estadísticas */}
      <div className="flex items-center gap-4">
        <h2 className="text-lg font-semibold">Estadísticas</h2>
        <Select value={statsFilter} onValueChange={setStatsFilter}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Seleccionar período" />
          </SelectTrigger>
          <SelectContent>
            {/* <SelectItem value="all-time">Todos los tiempos</SelectItem> */}
            <SelectItem value="current-month">Mes actual</SelectItem>
            <SelectItem value="previous-month">Mes anterior</SelectItem>
            <SelectItem value="2-months-ago">2 meses atrás</SelectItem>
            <SelectItem value="3-months-ago">3 meses atrás</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ingresos Totales</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(stats.totalAmount || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatPercentageChange(stats.totalAmount || 0, previousStats.totalAmount || 0)} desde {getStatsFilterLabel()}
            </p>
          </CardContent>
        </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ganancias Netas</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
              ${(stats.totalEarnings || 0).toLocaleString()}
          </div>
            <p className="text-xs text-muted-foreground">
              {formatPercentageChange(stats.totalEarnings || 0, previousStats.totalEarnings || 0)} desde {getStatsFilterLabel()}
            </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transferido</CardTitle>
            <Calendar className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
              ${(stats.totalTransferred || 0).toLocaleString()}
          </div>
            <p className="text-xs text-muted-foreground">
              {formatPercentageChange(stats.totalTransferred || 0, previousStats.totalTransferred || 0)} desde {getStatsFilterLabel()}
            </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendiente de Transferir</CardTitle>
          <Calendar className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
              ${(stats.pendingTransfer || 0).toLocaleString()}
          </div>
            <p className="text-xs text-muted-foreground">
              {formatPercentageChange(stats.pendingTransfer || 0, previousStats.pendingTransfer || 0)} desde {getStatsFilterLabel()}
            </p>
        </CardContent>
      </Card>
      </div>

      {/* Segunda fila con información adicional */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Comisiones Totales</CardTitle>
            <CreditCard className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(stats.totalPlatformFees || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {(stats.averageCommissionRate || 12).toFixed(1)}% promedio
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transacciones</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats.totalTransactions || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatPercentageChange(stats.totalTransactions || 0, previousStats.totalTransactions || 0)} desde {getStatsFilterLabel()}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
