"use client"

import { /* ArrowUp, */ Car, CreditCard, Calendar, /* TrendingUp */ } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { hostTransactionsApi } from "@/lib/api/transactions.api"
import { vehiclesApi } from "@/lib/api/vehicles.api"

interface HostDashboardStats {
  totalVehicles: number;
  activeRentals: number;
  monthlyEarnings: number;
  occupancyRate: number;
}

export function HostDashboardStats() {
  const [stats, setStats] = useState<HostDashboardStats>({
    totalVehicles: 0,
    activeRentals: 0,
    monthlyEarnings: 0,
    occupancyRate: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Obtener estadísticas de vehículos
        const vehiclesResponse = await vehiclesApi.host.getStats();

        // Obtener estadísticas de ganancias del mes actual
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        const transactionsResponse = await hostTransactionsApi.getEarningsStats({
          startDate: startOfMonth.toISOString(),
          endDate: endOfMonth.toISOString()
        });

        if (vehiclesResponse && transactionsResponse) {
          const monthlyEarnings = transactionsResponse.totalEarnings || 0;

          setStats({
            totalVehicles: vehiclesResponse.totalVehicles || 0,
            activeRentals: vehiclesResponse.totalReservations || 0,
            monthlyEarnings,
            occupancyRate: 78 // Placeholder - se calculará con más datos
          });
        }
      } catch (error) {
        console.error('Error fetching host dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Mis Vehículos</CardTitle>
          <Car className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalVehicles}</div>
          <p className="text-xs text-muted-foreground">
            {stats.totalVehicles === 1 ? 'vehículo registrado' : 'vehículos registrados'}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Actualmente Rentados</CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.activeRentals}</div>
          <p className="text-xs text-muted-foreground">
            {stats.activeRentals === 1 ? 'reserva activa' : 'reservas activas'}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Ganancias del Mes</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            ${stats.monthlyEarnings.toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
          </div>
          <p className="text-xs text-muted-foreground">
            ganancias netas este mes
          </p>
        </CardContent>
      </Card>
      {/* <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Tasa de Ocupación</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.occupancyRate}%</div>
          <p className="text-xs text-muted-foreground">
            tiempo ocupado promedio
          </p>
        </CardContent>
      </Card> */}
    </div>
  )
}
