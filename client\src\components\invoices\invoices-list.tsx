"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  FileText, 
  Download, 
  Eye, 
  Search,
  Calendar,
  DollarSign
} from "lucide-react"
import { clientInvoicesApi, hostInvoicesApi, /* Invoice, */ InvoiceFilters } from "@/lib/api/invoices.api"
import { DateTime } from "luxon"
import Link from "next/link"

interface InvoicesListProps {
  userType: 'client' | 'host';
  showFilters?: boolean;
}

export function InvoicesList({ userType, showFilters = true }: InvoicesListProps) {
  const [filters, setFilters] = useState<InvoiceFilters>({
    limit: 10,
    offset: 0,
    status: undefined
  })
  const [searchQuery, setSearchQuery] = useState("")

  // Seleccionar API según tipo de usuario
  const api = userType === 'client' ? clientInvoicesApi : hostInvoicesApi

  // Query para obtener facturas
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [`${userType}-invoices`, filters],
    queryFn: () => api.getInvoices(filters),
    staleTime: 60 * 1000, // 1 minuto
  })

  const invoices = data?.success ? data.data.invoices : []
  const pagination = data?.success ? data.data.pagination : null

  // Filtrar facturas por búsqueda local
  const filteredInvoices = invoices.filter(invoice => 
    searchQuery === "" || 
    invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.transaction.reservation.vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.transaction.reservation.vehicle.model.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: "secondary" as const, label: "Borrador" },
      sent: { variant: "default" as const, label: "Enviada" },
      paid: { variant: "default" as const, label: "Pagada", className: "bg-green-100 text-green-800" as const },
      cancelled: { variant: "destructive" as const, label: "Cancelada" },
      refunded: { variant: "destructive" as const, label: "Reembolsada" }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft

    return (
      <Badge variant={config.variant} className={(config as any).className || ""}>
        {config.label}
      </Badge>
    )
  }

  const handleDownloadPdf = async (invoiceId: string) => {
    try {
      if (userType === 'client') {
        const response = await clientInvoicesApi.downloadPdf(invoiceId)
        if (response.success) {
          // El endpoint debería redirigir al PDF
          window.open(`/api/v1/client/invoices/${invoiceId}/pdf`, '_blank')
        }
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Facturas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Facturas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>Error al cargar las facturas</p>
            <Button variant="outline" onClick={() => refetch()} className="mt-2">
              Reintentar
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Facturas
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {showFilters && (
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por número, cliente o vehículo..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select
              value={filters.status || "all"}
              onValueChange={(value) => setFilters(prev => ({ 
                ...prev, 
                status: value === "all" ? undefined : value,
                offset: 0 
              }))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="draft">Borrador</SelectItem>
                <SelectItem value="sent">Enviada</SelectItem>
                <SelectItem value="paid">Pagada</SelectItem>
                <SelectItem value="cancelled">Cancelada</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="space-y-4">
          {filteredInvoices.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No tienes facturas aún</p>
              <p className="text-sm">Las facturas aparecerán aquí cuando se generen automáticamente</p>
            </div>
          ) : (
            filteredInvoices.map((invoice) => (
              <div key={invoice.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{invoice.invoiceNumber}</h3>
                      {getStatusBadge(invoice.status)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {invoice.transaction.reservation.vehicle.make} {invoice.transaction.reservation.vehicle.model} {invoice.transaction.reservation.vehicle.year}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {DateTime.fromISO(invoice.issuedAt).toFormat('dd/MM/yyyy')}
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4" />
                        ${invoice.totalAmount.toLocaleString('es-MX', { minimumFractionDigits: 2 })}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link href={`/dashboard/${userType}/invoices/${invoice.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Ver
                      </Button>
                    </Link>
                    {invoice.pdfUrl && userType === 'client' && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDownloadPdf(invoice.id)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        PDF
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {pagination && pagination.pages > 1 && (
          <div className="flex items-center justify-between pt-4">
            <p className="text-sm text-muted-foreground">
              Mostrando {filters.offset! + 1} - {Math.min(filters.offset! + filters.limit!, pagination.total)} de {pagination.total} facturas
            </p>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={filters.offset === 0}
                onClick={() => setFilters(prev => ({ ...prev, offset: Math.max(0, prev.offset! - prev.limit!) }))}
              >
                Anterior
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={filters.offset! + filters.limit! >= pagination.total}
                onClick={() => setFilters(prev => ({ ...prev, offset: prev.offset! + prev.limit! }))}
              >
                Siguiente
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
