"use client"

import { useEffect } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import toast from "react-hot-toast"

export default function BankingSuccessPage() {
  const router = useRouter()

  useEffect(() => {
    toast.success('¡Configuración bancaria completada exitosamente!')
  }, [])

  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle className="text-xl">¡Configuración Completada!</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            Tu cuenta bancaria ha sido configurada exitosamente. Ahora puedes recibir pagos automáticos de tus reservas.
          </p>
          <div className="space-y-2">
            <Button 
              onClick={() => router.push('/dashboard/host/banking')}
              className="w-full"
            >
              Ver Configuración Bancaria
            </Button>
            <Button 
              variant="outline"
              onClick={() => router.push('/dashboard/host/earnings')}
              className="w-full"
            >
              Ver Ganancias
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
