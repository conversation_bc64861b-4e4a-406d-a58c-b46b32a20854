"use client"

import Link from "next/link"
import VehicleCard from "@/components/vehicles/vehicle-card"
import VehicleFilters from "@/components/vehicles/vehicle-filters"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { useQuery } from "@tanstack/react-query"
import { useSearchParams, useRouter } from 'next/navigation'
import { PaginationControl } from "@/components/ui/pagination-control"
import { useState } from "react"

export default function VehiclesPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const page = Number(searchParams.get('page') || 1)
  const limit = Number(searchParams.get('limit') || 10)
  const [filters, setFilters] = useState({})

  const { data, isLoading, error } = useQuery({
    queryKey: ['vehicles', page, limit, filters],
    queryFn: () => vehiclesApi.getAll({
      page,
      limit,
      ...filters
    }),
    staleTime: 60 * 1000, // 1 minuto
  })

  const vehicles = data?.data
  const pagination = data?.pagination

  const handleFiltersChange = (newFilters: any) => {
    setFilters(newFilters)
    // Reset to page 1 when filters change
    if (page !== 1) {
      const params = new URLSearchParams(searchParams.toString())
      params.set('page', '1')
      router.push(`?${params.toString()}`)
    }
  }

  const handlePageChange = (pageIndex: number) => {
    if (pageIndex !== page) {
      const params = new URLSearchParams(searchParams.toString())
      params.set('page', pageIndex.toString())
      router.push(`?${params.toString()}`)
    }
  }

  return (
    <div className="bg-background">
      <div className="container mx-auto py-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Filtros */}
          <VehicleFilters onFiltersChange={handleFiltersChange} variant="public" />

          {/* Lista de Vehículos */}
          <div className="md:col-span-3">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : error ? (
                <div className="text-center text-red-500">No se pudieron cargar los vehículos. Por favor, intenta de nuevo más tarde.</div>
              ) : !vehicles || vehicles.length === 0 ? (
              <div className="text-center">No se encontraron vehículos.</div>
            ) : (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {vehicles.map((vehicle) => (
                          <Link key={vehicle.id} href={`/vehicles/${vehicle.id}`} prefetch={false}>
                            <VehicleCard
                              id={vehicle.id}
                              make={vehicle.make}
                              model={vehicle.model}
                              price={vehicle.price}
                              rating={vehicle.averageRating || 0}
                              reviews={vehicle.totalReviews || 0}
                              // mock rating and reviews using math random
                              // rating={Math.floor(Math.random() * 5) + 1}
                              // reviews={Math.floor(Math.random() * 100)}
                              images={vehicle.images}
                              features={vehicle.features}
                              year={vehicle.year}
                              engineSize={vehicle.engineSize}
                              transmission={vehicle.transmission}
                              trim={vehicle.trim}
                              bodyType={vehicle.bodyType}
                              createdAt={vehicle.createdAt}
                            />
                          </Link>
                        ))}
                      </div>

                      {pagination && pagination.totalPages > 1 && (
                        <PaginationControl
                          currentPage={page}
                          totalPages={pagination.totalPages}
                          onPageChange={handlePageChange}
                          className="mt-8"
                        />
                      )}
                    </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}



