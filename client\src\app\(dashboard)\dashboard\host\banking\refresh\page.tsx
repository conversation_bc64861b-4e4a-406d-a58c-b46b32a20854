"use client"

import { useEffect } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import toast from "react-hot-toast"

export default function BankingRefreshPage() {
  const router = useRouter()

  useEffect(() => {
    toast.error('La configuración bancaria necesita ser completada')
  }, [])

  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
            <AlertCircle className="h-6 w-6 text-orange-600" />
          </div>
          <CardTitle className="text-xl">Configuración Pendiente</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            La configuración de tu cuenta bancaria no se completó. Puedes intentar nuevamente desde tu panel de configuración.
          </p>
          <div className="space-y-2">
            <Button 
              onClick={() => router.push('/dashboard/host/banking')}
              className="w-full"
            >
              Intentar Nuevamente
            </Button>
            <Button 
              variant="outline"
              onClick={() => router.push('/dashboard/host')}
              className="w-full"
            >
              Ir al Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
