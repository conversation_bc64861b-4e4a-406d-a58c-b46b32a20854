"use client"

import { StripeOnboarding } from "@/components/host/stripe-onboarding"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  Clock, 
  Shield, 
  CreditCard,
  FileText,
  User,
  Building2,
  AlertCircle
} from "lucide-react"

export default function HostOnboardingPage() {
  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Configuración de Pagos</h1>
        <p className="text-muted-foreground">
          Configura tu cuenta para recibir transferencias automáticas de tus ganancias como host.
        </p>
      </div>

      {/* Componente principal de onboarding */}
      <StripeOnboarding showFullCard={true} />

      {/* Información del proceso */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Documentos Necesarios</span>
            </CardTitle>
            <CardDescription>
              Prepara estos documentos para acelerar el proceso
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-3">
              <User className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Identificación Oficial</p>
                <p className="text-xs text-muted-foreground">INE, Pasaporte o Cédula Profesional</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Building2 className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Comprobante de Domicilio</p>
                <p className="text-xs text-muted-foreground">No mayor a 3 meses</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <CreditCard className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Información Bancaria</p>
                <p className="text-xs text-muted-foreground">CLABE interbancaria de 18 dígitos</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Proceso de Verificación</span>
            </CardTitle>
            <CardDescription>
              Tiempos estimados para cada etapa
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm">Envío de documentos</span>
              </div>
              <Badge variant="secondary">5 min</Badge>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-sm">Revisión automática</span>
              </div>
              <Badge variant="secondary">Inmediato</Badge>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Verificación manual</span>
              </div>
              <Badge variant="secondary">1-3 días</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Beneficios */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Beneficios de la Verificación</span>
          </CardTitle>
          <CardDescription>
            Qué obtienes al completar tu configuración de pagos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-start space-x-3 p-3 rounded-lg bg-green-50 border border-green-200">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-green-800">Transferencias Automáticas</p>
                <p className="text-xs text-green-600">Recibe tus ganancias cada 2 días hábiles</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 rounded-lg bg-blue-50 border border-blue-200">
              <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-blue-800">Protección Completa</p>
                <p className="text-xs text-blue-600">Tus datos están protegidos por Stripe</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 rounded-lg bg-purple-50 border border-purple-200">
              <CreditCard className="h-5 w-5 text-purple-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-purple-800">Sin Comisiones Extra</p>
                <p className="text-xs text-purple-600">Solo la comisión estándar de la plataforma</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Información importante */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">Información Importante</p>
            <ul className="text-sm space-y-1 ml-4 list-disc">
              <li>El proceso se realiza a través de Stripe, nuestro procesador de pagos certificado</li>
              <li>Tus datos están protegidos con cifrado de nivel bancario</li>
              <li>Una vez verificado, podrás recibir transferencias automáticas</li>
              <li>Puedes actualizar tu información bancaria en cualquier momento</li>
              <li>El soporte está disponible 24/7 para ayudarte con cualquier duda</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  )
}
