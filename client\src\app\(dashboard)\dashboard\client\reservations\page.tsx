'use client'

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Car, Clock, /* MapPin */ } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import Image from "next/image"
import { reservationsApi } from "@/lib/api/reservations.api"

export default function ClientReservationsPage() {
  const [activeTab, setActiveTab] = useState("active")
  
  const { data: reservations, isLoading } = useQuery({
    queryKey: ['client-reservations'],
    queryFn: reservationsApi.client.getReservations,
    staleTime: 60 * 1000, // 1 minuto
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Activa</Badge>
      case 'confirmed':
        return <Badge className="bg-green-500">Confirmada</Badge>
      case 'pending':
        return <Badge className="bg-blue-500">Pendiente</Badge>
      case 'upcoming':
        return <Badge className="bg-blue-500">Próxima</Badge>
      case 'completed':
        return <Badge className="bg-gray-500">Completada</Badge>
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelada</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }
  console.log('reservations', reservations);
  const filteredReservations = reservations?.filter(reservation => {
    if (activeTab === "active") {
      return ['active', 'confirmed', 'pending', 'upcoming'].includes(reservation.status.toLowerCase());
    }
    if (activeTab === "completed") return reservation.status.toLowerCase() === "completed";
    if (activeTab === "cancelled") return reservation.status.toLowerCase() === "cancelled";
    return true;
  });

  // Función para formatear fechas
  const formatDate = (dateString: string | Date) => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return format(date, "d 'de' MMMM, yyyy", { locale: es });
  }

  // Función para obtener la imagen del vehículo
  const getVehicleImage = (vehicle: any) => {
    if (!vehicle) return "/placeholder.svg?height=300&width=500";
    if (vehicle.images && vehicle.images.length > 0) return vehicle.images[0];
    return "/placeholder.svg?height=300&width=500";
  }

  // Función para obtener el nombre del vehículo
  const getVehicleName = (vehicle: any) => {
    if (!vehicle) return "Vehículo no disponible";
    return `${vehicle.make} ${vehicle.model} ${vehicle.year}`;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Mis Reservaciones</h1>
        <p className="text-muted-foreground">Gestiona tus reservas de vehículos</p>
      </div>

      <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="active">Confirmadas y Próximas</TabsTrigger>
          <TabsTrigger value="completed">Completadas</TabsTrigger>
          <TabsTrigger value="cancelled">Canceladas</TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab} className="space-y-4">
          {isLoading ? (
            // Esqueletos de carga
            Array.from({ length: 3 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row gap-4">
                    <Skeleton className="h-40 w-full md:w-48 rounded-md" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-6 w-48" />
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-64" />
                      <Skeleton className="h-4 w-24" />
                      <div className="flex justify-end mt-4">
                        <Skeleton className="h-10 w-32" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : !filteredReservations || filteredReservations.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground py-4">No tienes reservaciones {activeTab === "active" ? "activas o próximas" : activeTab === "completed" ? "completadas" : "canceladas"}</p>
              </CardContent>
            </Card>
          ) : (
            filteredReservations.map(reservation => (
              <Card key={reservation.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="relative h-40 w-full md:w-48 rounded-md overflow-hidden">
                      <Image
                        src={getVehicleImage(reservation.vehicle)}
                        alt={getVehicleName(reservation.vehicle)}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-lg font-bold">{getVehicleName(reservation.vehicle)}</h3>
                          {reservation.vehicle?.host && (
                            <p className="text-sm text-muted-foreground">
                              Anfitrión: {reservation.vehicle.host.name}
                            </p>
                          )}
                        </div>
                        {getStatusBadge(reservation.status)}
                      </div>
                      
                      <div className="mt-4 space-y-2">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>
                            {formatDate(reservation.startDate)} - {formatDate(reservation.endDate)}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>
                            {format(new Date(reservation.startDate), "HH:mm")} - {format(new Date(reservation.endDate), "HH:mm")}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Car className="h-4 w-4 mr-2" />
                          <span>Total: ${reservation.totalPrice}</span>
                        </div>
                      </div>
                      
                      <div className="flex justify-end mt-4">
                        <Link href={`/dashboard/client/reservations/${reservation.id}`}>
                          <Button>Ver detalles</Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
