"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DollarSign, CreditCard, Calendar } from "lucide-react"
import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { clientTransactionsApi } from "@/lib/api/transactions.api"

export function ClientPaymentStats() {
  const [statsFilter, setStatsFilter] = useState("all-time");

  // Función para obtener fechas según el filtro
  const getDateRange = (filter: string) => {
    const now = new Date();
    let startDate: Date, endDate: Date, previousStartDate: Date, previousEndDate: Date;

    switch (filter) {
      case "this-month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        previousStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        previousEndDate = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case "last-month":
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        previousStartDate = new Date(now.getFullYear(), now.getMonth() - 2, 1);
        previousEndDate = new Date(now.getFullYear(), now.getMonth() - 1, 0);
        break;
      case "last-3-months":
        startDate = new Date(now.getFullYear(), now.getMonth() - 2, 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        previousStartDate = new Date(now.getFullYear(), now.getMonth() - 5, 1);
        previousEndDate = new Date(now.getFullYear(), now.getMonth() - 2, 0);
        break;
      default: // all-time
        return null;
    }

    return {
      current: { startDate: startDate.toISOString(), endDate: endDate.toISOString() },
      previous: { startDate: previousStartDate.toISOString(), endDate: previousEndDate.toISOString() }
    };
  };

  // React Query para estadísticas actuales
  const { data: currentStats, isLoading } = useQuery({
    queryKey: ['client-payment-stats', statsFilter],
    queryFn: async () => {
      const dateRange = getDateRange(statsFilter);
      if (dateRange) {
        return await clientTransactionsApi.getPaymentStats({
          startDate: dateRange.current.startDate,
          endDate: dateRange.current.endDate
        });
      } else {
        return await clientTransactionsApi.getPaymentStats();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  const dateRange = getDateRange(statsFilter);
  // React Query para estadísticas anteriores (para comparación)
  const { data: previousStats } = useQuery({
    queryKey: ['client-payment-stats-previous', statsFilter],
    queryFn: async () => {
      if (!dateRange) {
        return { success: true, data: { stats: { totalSpent: 0, totalTransactions: 0, successfulPayments: 0 } } };
      }
      return await clientTransactionsApi.getPaymentStats({
        startDate: dateRange.previous.startDate,
        endDate: dateRange.previous.endDate
      });
    },
    // enabled only if dateRange is defined and statsFilter is not "all-time"
    enabled: !!dateRange && statsFilter !== "all-time",
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  // Procesar datos
  const stats = currentStats?.success ? currentStats.data?.stats : null;
  const prevStats = previousStats?.success ? previousStats.data?.stats : null;

  // Función para formatear el cambio porcentual
  const formatPercentageChange = (current: number, previous: number) => {
    if (previous === 0) {
      return current > 0 ? "+100% desde período anterior" : "0% desde período anterior";
    }
    const change = ((current - previous) / previous) * 100;
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(1)}% desde período anterior`;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Estadísticas</h2>
          <div className="h-10 w-48 bg-muted animate-pulse rounded" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
                <div className="h-3 w-24 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Valores por defecto si no hay datos
  const currentData = (stats as any) || { totalSpent: 0, totalTransactions: 0, successfulPayments: 0 };
  const previousData = (prevStats as any) || { totalSpent: 0, totalTransactions: 0, successfulPayments: 0 };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Estadísticas</h2>
        <Select value={statsFilter} onValueChange={setStatsFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Seleccionar período" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all-time">Todos los tiempos</SelectItem>
            <SelectItem value="this-month">Este mes</SelectItem>
            <SelectItem value="last-month">Mes pasado</SelectItem>
            <SelectItem value="last-3-months">Últimos 3 meses</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Gastado</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(currentData.totalSpent || 0).toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
            </div>
            <p className="text-xs text-muted-foreground">
              {statsFilter === "all-time"
                ? `en ${currentData.successfulPayments || 0} ${(currentData.successfulPayments || 0) === 1 ? 'transacción' : 'transacciones'}`
                : formatPercentageChange(currentData.totalSpent || 0, previousData.totalSpent || 0)
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {statsFilter === "this-month" ? "Este Mes" :
                statsFilter === "last-month" ? "Mes Pasado" :
                  statsFilter === "last-3-months" ? "Últimos 3 Meses" : "Este Período"}
            </CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(currentData.totalSpent || 0).toLocaleString('es-MX', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
            </div>
            <p className="text-xs text-muted-foreground">
              {statsFilter === "all-time"
                ? "gastado en el período"
                : formatPercentageChange(currentData.totalSpent || 0, previousData.totalSpent || 0)
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transacciones</CardTitle>
            <CreditCard className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentData.totalTransactions || 0}</div>
            <p className="text-xs text-muted-foreground">
              {statsFilter === "all-time"
                ? `${currentData.successfulPayments || 0} exitosas`
                : formatPercentageChange(currentData.totalTransactions || 0, previousData.totalTransactions || 0)
              }
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
