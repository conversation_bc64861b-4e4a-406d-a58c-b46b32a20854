"use client"

import { useState, useEffect } from "react"
import { useBookingStoreForVehicle } from "@/hooks/use-booking-store-for-vehicle"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { toast } from "react-hot-toast"
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js'
import {
  CardNumberElement,
  CardExpiryElement,
  CardCvcElement,
  useStripe,
  useElements,
  Elements,
} from '@stripe/react-stripe-js';
import { useUser } from '@/context/user-context'
import { apiService } from '@/services/api'

// Asegúrate de que la clave pública de Stripe sea válida
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
)

// Opciones para los elementos de Stripe con estilos mejorados
const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      color: '#32325d',
      fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
      fontSmoothing: 'antialiased',
      fontSize: '16px',
      lineHeight: '40px', // Aumentar para centrar verticalmente
      '::placeholder': {
        color: '#aab7c4'
      },
    },
    invalid: {
      color: '#fa755a',
      iconColor: '#fa755a'
    }
  },
  hidePostalCode: true
}

// Añadir esta opción para deshabilitar el relleno automático en desarrollo
const stripeElementsOptions: StripeElementsOptions = {
  // Esto permite que Stripe funcione en entornos de desarrollo sin HTTPS
  loader: 'auto',
  appearance: {
    theme: 'stripe',
  },
}

function PaymentForm({ vehicleId }: { vehicleId: string }) {
  // const { data: session } = useSession()
  const session = useUser()
  const { vehicle, dateRange: rangeDate, totalPrice, prevStep, nextStep, setPaymentInfo } = useBookingStoreForVehicle(vehicleId)

  const dateRange = {
    startDate: rangeDate.startDate!,
    endDate: rangeDate.endDate!,
  }
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [cardName, setCardName] = useState("")
  const [email, setEmail] = useState("")
  const [stripeReady, setStripeReady] = useState(false)

  // Estados para manejar errores de los elementos de Stripe
  const [cardNumberError, setCardNumberError] = useState<string | null>(null)
  const [cardExpiryError, setCardExpiryError] = useState<string | null>(null)
  const [cardCvcError, setCardCvcError] = useState<string | null>(null)

  const stripe = useStripe()
  const elements = useElements()

  // Prellenar el email con el de la sesión si está disponible
  useEffect(() => {
    if (session?.user?.email) {
      setEmail(session.user.email)
    }
  }, [session])

  // Verificar que Stripe esté listo
  useEffect(() => {
    if (stripe && elements) {
      setStripeReady(true)
    }
  }, [stripe, elements])

  const days = dateRange.endDate && dateRange.startDate
    ? Math.ceil((dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24))
    : 0

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!stripe || !elements) {
      toast.error("El sistema de pagos no está listo. Por favor, intenta de nuevo.")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const cardNumber = elements.getElement(CardNumberElement)

      if (!cardNumber) {
        throw new Error("Error al cargar el elemento de tarjeta")
      }

      // Crear PaymentMethod primero
      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardNumber,
        billing_details: {
          name: cardName,
          email: email,
        },
      })

      if (paymentMethodError) {
        throw new Error(paymentMethodError.message)
      }

      type PaymentResponse = {
        success: boolean;
        requires_action: boolean;
        client_secret: string;
        status: string;
        payment_intent_id: string;
        error?: string;
      }

      if (!vehicle) {
        return toast.error("No se pudo procesar el pago. Por favor, intenta de nuevo.")
      }

      // Procesar el pago en el servidor usando apiService
      const response = await apiService.post<PaymentResponse>('/payments', {
        amount: totalPrice * 100, // Convertir a centavos
        currency: "mxn",
        customer_email: email,
        customer_name: cardName,
        payment_method_id: paymentMethod.id,
        description: `Reserva de ${vehicle.make} ${vehicle.model} por ${days} ${days === 1 ? 'día' : 'días'}`,
        // Note: reservationId is not included here as the reservation is created after payment
      })



      if (response.success) {
        const data = response.data

        if (!data.success) {
          throw new Error(data.error || "Error al procesar el pago")
        }

        if (data.requires_action) {
          // Manejar autenticación adicional si es necesario
          const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(
            data.client_secret
          )

          if (confirmError) {
            throw new Error(confirmError.message)
          }

          if (paymentIntent.status === "succeeded") {
            // Guardar información del pago exitoso
            setPaymentInfo({
              paymentIntentId: data.payment_intent_id,
              amount: totalPrice,
              currency: "mxn"
            })
            toast.success("¡Pago procesado correctamente!")
            nextStep()
          }
        } else if (data.status === "succeeded") {
          // Guardar información del pago exitoso
          setPaymentInfo({
            paymentIntentId: data.payment_intent_id,
            amount: totalPrice,
            currency: "mxn"
          })
          toast.success("¡Pago procesado correctamente!")
          nextStep()
        }


      } else {
        // throw new Error(response.error || "Error al procesar el pago")
      }

    } catch (error) {
      console.error("Error al procesar el pago:", error)
      setError(error instanceof Error ? error.message : "Error inesperado")
      toast.error("No se pudo procesar el pago. Por favor, intenta de nuevo.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Manejadores de eventos para los elementos de Stripe
  const handleCardNumberChange = (event: any) => {
    setCardNumberError(event.error ? event.error.message : null)
  }

  const handleCardExpiryChange = (event: any) => {
    setCardExpiryError(event.error ? event.error.message : null)
  }

  const handleCardCvcChange = (event: any) => {
    setCardCvcError(event.error ? event.error.message : null)
  }

  if (!vehicle) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="p-4 border border-red-300 bg-red-50 rounded-lg text-red-800">
          <h3 className="font-bold">Información incompleta</h3>
          <p>Por favor, selecciona un vehículo y fechas de reserva.</p>
          <Button
            onClick={prevStep}
            className="mt-4"
            variant="outline"
          >
            Volver a seleccionar fechas
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-3xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">Información de pago</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="border rounded-lg p-4">
          <h3 className="font-bold mb-2">Detalles del vehículo</h3>
          <p className="mb-1">{vehicle.make} {vehicle.model} {vehicle.year}</p>
          <p className="mb-1">Color: {vehicle.color}</p>
          <p className="font-medium">${vehicle.price} / día</p>
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="font-bold mb-2">Fechas de reserva</h3>
          <p className="mb-1">
            <span className="font-medium">Desde:</span> {format(dateRange.startDate, "PPP", { locale: es })}
          </p>
          <p className="mb-1">
            <span className="font-medium">Hasta:</span> {format(dateRange.endDate, "PPP", { locale: es })}
          </p>
          <p className="font-medium">{days} {days === 1 ? 'día' : 'días'}</p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-xl font-bold mb-4">Método de pago</h3>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Correo electrónico</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="mt-1"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cardName">Nombre en la tarjeta</Label>
              <Input
                id="cardName"
                value={cardName}
                onChange={(e) => setCardName(e.target.value)}
                placeholder="Juan Pérez"
                className="mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="cardNumber">Número de tarjeta</Label>
              <div className="mt-1 border rounded-md bg-white h-[40px] flex items-center px-3">
                {stripeReady && (
                  <CardNumberElement
                    id="cardNumber"
                    options={CARD_ELEMENT_OPTIONS}
                    onChange={handleCardNumberChange}
                    className="w-full h-full"
                  />
                )}
              </div>
              {cardNumberError && (
                <p className="text-sm text-red-500 mt-1">{cardNumberError}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="expiry">Fecha de expiración</Label>
                <div className="mt-1 border rounded-md bg-white h-[40px] flex items-center px-3">
                  {stripeReady && (
                    <CardExpiryElement
                      id="expiry"
                      options={CARD_ELEMENT_OPTIONS}
                      onChange={handleCardExpiryChange}
                      className="w-full h-full"
                    />
                  )}
                </div>
                {cardExpiryError && (
                  <p className="text-sm text-red-500 mt-1">{cardExpiryError}</p>
                )}
              </div>
              <div>
                <Label htmlFor="cvc">CVV</Label>
                <div className="mt-1 border rounded-md bg-white h-[40px] flex items-center px-3">
                  {stripeReady && (
                    <CardCvcElement
                      id="cvc"
                      options={CARD_ELEMENT_OPTIONS}
                      onChange={handleCardCvcChange}
                      className="w-full h-full"
                    />
                  )}
                </div>
                {cardCvcError && (
                  <p className="text-sm text-red-500 mt-1">{cardCvcError}</p>
                )}
              </div>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
              {error}
            </div>
          )}

          <div className="p-4 bg-gray-50 rounded-lg mt-6">
            <div className="flex justify-between mb-2">
              <span>Precio por día:</span>
              <span className="font-medium">${vehicle.price}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Duración:</span>
              <span className="font-medium">{days} {days === 1 ? 'día' : 'días'}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Total:</span>
              <span>${totalPrice}</span>
            </div>
          </div>

          <div className="flex justify-between pt-4">
            <Button
              type="button"
              onClick={prevStep}
              variant="outline"
            >
              Volver
            </Button>
            <Button
              type="submit"
              className="bg-[#1a2b5e] hover:bg-[#152348]"
              disabled={isSubmitting || !stripeReady}
            >
              {isSubmitting ? "Procesando..." : "Procesar pago"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Componente para mostrar un mensaje de carga mientras Stripe se inicializa
function LoadingStripe() {
  return (
    <div className="max-w-3xl mx-auto text-center py-12">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
      <p className="text-lg">Cargando el sistema de pagos...</p>
    </div>
  )
}

export default function BookingPayment({ vehicleId }: { vehicleId: string }) {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simular un tiempo de carga para asegurar que Stripe se inicialice correctamente
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return <LoadingStripe />
  }

  return (
    <Elements stripe={stripePromise} options={stripeElementsOptions}>
      <PaymentForm vehicleId={vehicleId} />
    </Elements>
  )
}






