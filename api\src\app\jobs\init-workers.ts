import { transferWorker, reportsWorker, setupRecurringJobs } from './payment-jobs';

/**
 * <PERSON><PERSON>alizar todos los workers de BullMQ
 */
export async function initializeWorkers() {
  try {
    console.log('🚀 Inicializando workers de BullMQ...');

    // Los workers ya están definidos en payment-jobs.ts
    // Solo necesitamos configurar los trabajos recurrentes
    await setupRecurringJobs();

    console.log('✅ Workers de BullMQ inicializados correctamente');
    console.log('📋 Workers activos:');
    console.log('   - Transfer Worker: Procesando transferencias a hosts');
    console.log('   - Reports Worker: Generando reportes mensuales');

    return {
      transferWorker,
      reportsWorker
    };
  } catch (error) {
    console.error('❌ Error inicializando workers de BullMQ:', error);
    throw error;
  }
}

/**
 * Cerrar todos los workers de forma segura
 */
export async function closeWorkers() {
  try {
    console.log('🔄 Cerrando workers de BullMQ...');

    await Promise.all([
      transferWorker.close(),
      reportsWorker.close()
    ]);

    console.log('✅ Workers cerrados correctamente');
  } catch (error) {
    console.error('❌ Error cerrando workers:', error);
    throw error;
  }
}