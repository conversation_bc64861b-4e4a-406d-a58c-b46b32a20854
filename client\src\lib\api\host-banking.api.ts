import { apiService } from '@/services/api';

// Tipos para información bancaria
export interface BankAccountInfo {
  id: string;
  accountHolderName: string;
  routingNumber: string; // CLABE
  accountNumber: string;
  bankName: string;
  accountType: 'checking' | 'savings';
  stripeExternalAccountId?: string;
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface HostBankingInfo {
  bankAccounts: BankAccountInfo[];
  defaultAccountIndex: number | null;
}

export interface CreateBankAccountData {
  accountHolderName: string;
  routingNumber: string;
  accountNumber: string;
  bankName: string;
  accountType: 'checking' | 'savings';
  stripeExternalAccountId?: string;
}

export interface UpdateBankAccountData extends CreateBankAccountData {
  id: string;
}

// API para hosts
export const hostBankingApi = {
  /**
   * Obtener información bancaria del host
   */
  getBankingInfo: async () => {
    // return apiService.get<{ success: boolean; data: HostBankingInfo }>('/host/banking-info');
    const response = await apiService.get<{ success: boolean; data: HostBankingInfo }>('/host/banking-info');
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.error.message);
    }
  },

  /**
   * Agregar cuenta bancaria
   */
  addBankAccount: async (data: CreateBankAccountData) => {
    return apiService.post<{ success: boolean; data: BankAccountInfo }>('/host/banking-info/accounts', data);
  },

  /**
   * Actualizar cuenta bancaria
   */
  updateBankAccount: async (data: UpdateBankAccountData) => {
    return apiService.put<{ success: boolean; data: BankAccountInfo }>('/host/banking-info/accounts', data);
  },

  /**
   * Eliminar cuenta bancaria
   */
  removeBankAccount: async (accountId: string) => {
    return apiService.delete<{ success: boolean; data: any }>(`/host/banking-info/accounts/${accountId}`);
  },

  /**
   * Establecer cuenta por defecto
   */
  setDefaultAccount: async (accountId: string) => {
    return apiService.post<{ success: boolean; data: any }>('/host/banking-info/set-default', { accountId });
  }
};

// API para administradores
export const adminBankingApi = {
  /**
   * Obtener información bancaria de un host específico
   */
  getHostBankingInfo: async (hostId: string) => {
    return apiService.get<{ success: boolean; data: HostBankingInfo }>(`/admin/banking-info/${hostId}`);
  },

  /**
   * Obtener cuenta bancaria por defecto de un host
   */
  getDefaultBankAccount: async (hostId: string) => {
    return apiService.get<{ success: boolean; data: BankAccountInfo | null }>(`/admin/banking-info/${hostId}/default`);
  }
};
