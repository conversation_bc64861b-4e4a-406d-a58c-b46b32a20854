{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__316d0c2f._.js", "server/edge/chunks/edge-wrapper_c902b4af.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3mJj0rm+yjcTJ0+q5ouXUz7Y0y1/mmcifCdkVVukIoM=", "__NEXT_PREVIEW_MODE_ID": "1b832f24b04c626052032042297edbf5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1f9fc98ea816c7a1c729e12b2720d5df8541d7d2bb0d2a03903ae724b808f94d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "78dcdec26f875c043bc4a53fccc1d651c2f70c17268bbc39149008769f15acd3"}}}, "sortedMiddleware": ["/"], "functions": {}}