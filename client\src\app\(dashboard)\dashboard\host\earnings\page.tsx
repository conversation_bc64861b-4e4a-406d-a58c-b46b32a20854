"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { Building2, Settings } from "lucide-react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { HostEarningsStats } from "@/components/host/host-earnings-stats"
import { HostEarningsChart } from "@/components/host/host-earnings-chart"
import { HostPayoutHistory } from "@/components/host/host-payout-history"
import { BankingSetupAlert } from "@/components/host/banking-setup-alert"
import { hostBankingApi } from "@/lib/api/host-banking.api"

export default function HostEarningsPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("overview")

  // Query para verificar si tiene información bancaria configurada
  const { data: bankingData } = useQuery({
    queryKey: ['host-banking-info'],
    queryFn: async () => {
      const response = await hostBankingApi.getBankingInfo()
      return response.data
    },
    staleTime: 60 * 1000, // 1 minuto
  })
  console.log('Banking Data:', bankingData)
  console.log('Banking Data:', bankingData?.bankAccounts)
  console.log('Banking Data:', bankingData?.defaultAccountIndex)

  const hasBankingConfigured = bankingData?.bankAccounts &&
    bankingData.bankAccounts.length > 0 &&
    bankingData.defaultAccountIndex !== null

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div className="flex flex-col space-y-2">
          <h1 className="text-2xl font-bold">Ganancias y Reportes</h1>
          <p className="text-muted-foreground">
            Gestiona tus ingresos, revisa reportes mensuales y analiza el rendimiento de tus vehículos.
          </p>
        </div>

        {/* Botones de navegación - siempre visibles */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/host/banking')}
          >
            <Building2 className="h-4 w-4 mr-2" />
            {hasBankingConfigured ? 'Gestionar Cuentas Bancarias' : 'Configurar Información Bancaria'}
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/host/settings')}
          >
            <Settings className="h-4 w-4 mr-2" />
            Configuración
          </Button>
        </div>
      </div>

      <BankingSetupAlert />

      <HostEarningsStats />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="transactions">Historial de Transacciones</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <HostEarningsChart />
            <HostPayoutHistory showOnlyRecent={true} />
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <HostPayoutHistory showOnlyRecent={false} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
