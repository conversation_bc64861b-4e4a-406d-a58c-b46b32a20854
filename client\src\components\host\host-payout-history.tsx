"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { CheckCircle, Clock, XCircle, Search, Filter, /* ChevronLeft, ChevronRight */ } from "lucide-react"
import { hostTransactionsApi } from "@/lib/api/transactions.api"
import { AdminDateRangeModal } from "@/components/admin/AdminDateRangeModal"
import { PaginationControl } from '../ui/pagination-control'

interface HostPayoutHistoryProps {
  showOnlyRecent?: boolean
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "transferred":
      return <CheckCircle className="h-4 w-4 text-green-600" />
    case "succeeded":
      return <Clock className="h-4 w-4 text-yellow-600" />
    case "failed":
      return <XCircle className="h-4 w-4 text-red-600" />
    default:
      return null
  }
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "transferred":
      return <Badge className="bg-green-100 text-green-800">Transferido</Badge>
    case "succeeded":
      return <Badge className="bg-yellow-100 text-yellow-800">Pendiente</Badge>
    case "failed":
      return <Badge className="bg-red-100 text-red-800">Fallido</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export function HostPayoutHistory({ showOnlyRecent = false }: HostPayoutHistoryProps) {
  const [page, setPage] = useState(0)
  const [statusFilter, setStatusFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>()

  const limit = showOnlyRecent ? 4 : 10

  // Query para obtener transacciones del host
  const { data: transactionsData, isLoading } = useQuery({
    queryKey: ['host-transactions', page, statusFilter, searchTerm, dateRange, showOnlyRecent],
    queryFn: async () => {
      const response = await hostTransactionsApi.getTransactions({
        limit,
        offset: page * limit,
        ...(statusFilter !== "all" && { status: statusFilter }),
        ...(dateRange && {
          startDate: dateRange.from.toISOString(),
          endDate: dateRange.to.toISOString()
        }),
      })
      if (!response.success) {
        throw new Error(response.error || 'Error al obtener transacciones')
      }
      return response.data
    },
    staleTime: 30 * 1000, // 30 segundos
  })

  const transactions = transactionsData?.data?.transactions || []
  const pagination = transactionsData?.data?.pagination || { total: 0, limit, offset: 0, pages: 0, currentPage: 1 }

  console.log('pagination', pagination)

  const handleDateRangeSelect = (ranges: any) => {
    const { startDate, endDate } = ranges.selection
    if (startDate && endDate) {
      setDateRange({ from: startDate, to: endDate })
      setShowDatePicker(false)
    }
  }

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Historial de Pagos</CardTitle>
          {!showOnlyRecent && (
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar transacciones..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-[200px]"
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="succeeded">Pendiente</SelectItem>
                  <SelectItem value="transferred">Transferido</SelectItem>
                  <SelectItem value="failed">Fallido</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                onClick={() => setShowDatePicker(true)}
              >
                <Filter className="mr-2 h-4 w-4" />
                Fechas
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(limit)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="h-4 w-4 bg-muted rounded" />
                  <div>
                    <div className="h-4 w-20 bg-muted rounded mb-2" />
                    <div className="h-3 w-16 bg-muted rounded" />
                  </div>
                </div>
                <div className="text-right">
                  <div className="h-6 w-16 bg-muted rounded mb-1" />
                  <div className="h-3 w-12 bg-muted rounded" />
                </div>
              </div>
            ))}
          </div>
        ) : transactions.length > 0 ? (
            <div className="space-y-4">
              {transactions.map((transaction: any) => (
                <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(transaction.status)}
                    <div>
                      <p className="font-medium">${transaction.hostEarnings.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">
                        Ganancia de ${transaction.amount.toLocaleString()} (Comisión: ${transaction.platformFee.toLocaleString()})
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {transaction.reservation?.vehicle?.make} {transaction.reservation?.vehicle?.model}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    {getStatusBadge(transaction.status)}
                    <p className="text-sm text-muted-foreground mt-1">
                      {new Date(transaction.createdAt).toLocaleDateString('es-ES', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                      }).replace(/\//g, '-')}
                    </p>
                  </div>
                </div>
              ))}

              {/* {!showOnlyRecent && pagination.total > 1 && (
                <div className="flex items-center justify-between pt-4">
                  <p className="text-sm text-muted-foreground">
                    Mostrando {pagination.offset + 1} a {Math.min(pagination.offset + pagination.limit, pagination.total)} de {pagination.total} transacciones
                  </p>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(page - 1)}
                      disabled={page === 0}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Anterior
                    </Button>
                    <span className="text-sm">
                      Página {page + 1} de {pagination.pages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(page + 1)}
                      disabled={page >= pagination.pages - 1}
                    >
                      Siguiente
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )} */}
              {
                !showOnlyRecent && (

                  <PaginationControl
                    currentPage={page}
                    totalPages={pagination.total}
                    onPageChange={handlePageChange}
                  />
                )
              }
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>No hay transacciones disponibles</p>
              </div>
        )}
      </CardContent>

      {/* Modal de Selección de Fechas */}
      <AdminDateRangeModal
        open={showDatePicker}
        onOpenChange={setShowDatePicker}
        onDateRangeSelect={handleDateRangeSelect}
        initialRange={dateRange ? {
          startDate: dateRange.from,
          endDate: dateRange.to,
          key: 'selection'
        } : undefined}
      />
    </Card>
  )
}
