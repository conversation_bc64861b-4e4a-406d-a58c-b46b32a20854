// "use client"

// import { useQuery } from "@tanstack/react-query"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { Button } from "@/components/ui/button"
// import { Badge } from "@/components/ui/badge"
// import { Separator } from "@/components/ui/separator"
// import { 
//   FileText, 
//   Download, 
//   ExternalLink,
//   // Calendar,
//   // DollarSign,
//   User,
//   Building,
//   Car
// } from "lucide-react"
// import { clientInvoicesApi, hostInvoicesApi, Invoice } from "@/lib/api/invoices.api"
// import { DateTime } from "luxon"

// interface InvoiceDetailProps {
//   invoiceId: string;
//   userType: 'client' | 'host';
// }

// export function InvoiceDetail({ invoiceId, userType }: InvoiceDetailProps) {
//   // Seleccionar API según tipo de usuario
//   const api = userType === 'client' ? clientInvoicesApi : hostInvoicesApi

//   // Query para obtener detalle de factura
//   const { data, isLoading, error } = useQuery({
//     queryKey: [`${userType}-invoice`, invoiceId],
//     queryFn: () => api.getInvoice(invoiceId),
//     staleTime: 60 * 1000, // 1 minuto
//   })

//   const invoice = data?.success ? data.data : null

//   const getStatusBadge = (status: string) => {
//     const statusConfig = {
//       draft: { variant: "secondary" as const, label: "Borrador" },
//       sent: { variant: "default" as const, label: "Enviada" },
//       paid: { variant: "default" as const, label: "Pagada", className: "bg-green-100 text-green-800" },
//       cancelled: { variant: "destructive" as const, label: "Cancelada" },
//       refunded: { variant: "destructive" as const, label: "Reembolsada" }
//     }

//     const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft

//     return (
//       <Badge variant={config.variant} className={config.className}>
//         {config.label}
//       </Badge>
//     )
//   }

//   const handleDownloadPdf = async () => {
//     if (!invoice) return

//     try {
//       if (userType === 'client') {
//         const response = await clientInvoicesApi.downloadPdf(invoice.id)
//         if (response.success) {
//           window.open(`/api/v1/client/invoices/${invoice.id}/pdf`, '_blank')
//         }
//       }
//     } catch (error) {
//       console.error('Error downloading PDF:', error)
//     }
//   }

//   if (isLoading) {
//     return (
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <FileText className="h-5 w-5" />
//             Cargando factura...
//           </CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="flex items-center justify-center py-8">
//             <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
//           </div>
//         </CardContent>
//       </Card>
//     )
//   }

//   if (error || !invoice) {
//     return (
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <FileText className="h-5 w-5" />
//             Error
//           </CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="text-center py-8 text-muted-foreground">
//             <p>No se pudo cargar la factura</p>
//           </div>
//         </CardContent>
//       </Card>
//     )
//   }

//   return (
//     <div className="space-y-6">
//       {/* Header */}
//       <Card>
//         <CardHeader>
//           <div className="flex items-start justify-between">
//             <div className="space-y-2">
//               <div className="flex items-center gap-2">
//                 <CardTitle className="flex items-center gap-2">
//                   <FileText className="h-5 w-5" />
//                   Factura {invoice.invoiceNumber}
//                 </CardTitle>
//                 {getStatusBadge(invoice.status)}
//               </div>
//               <p className="text-muted-foreground">
//                 Emitida el {DateTime.fromISO(invoice.issuedAt).toFormat('dd/MM/yyyy')}
//               </p>
//             </div>
//             <div className="flex gap-2">
//               {invoice.publicUrl && (
//                 <Button variant="outline" asChild>
//                   <a href={invoice.publicUrl} target="_blank" rel="noopener noreferrer">
//                     <ExternalLink className="h-4 w-4 mr-2" />
//                     Ver Online
//                   </a>
//                 </Button>
//               )}
//               {invoice.pdfUrl && userType === 'client' && (
//                 <Button onClick={handleDownloadPdf}>
//                   <Download className="h-4 w-4 mr-2" />
//                   Descargar PDF
//                 </Button>
//               )}
//             </div>
//           </div>
//         </CardHeader>
//       </Card>

//       <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
//         {/* Información del Cliente */}
//         <Card>
//           <CardHeader>
//             <CardTitle className="flex items-center gap-2">
//               <User className="h-5 w-5" />
//               Información del Cliente
//             </CardTitle>
//           </CardHeader>
//           <CardContent className="space-y-3">
//             <div>
//               <p className="font-medium">{invoice.customerName}</p>
//               <p className="text-sm text-muted-foreground">{invoice.customerEmail}</p>
//             </div>
//             {invoice.customerTaxId && (
//               <div>
//                 <p className="text-sm font-medium">RFC</p>
//                 <p className="text-sm text-muted-foreground">{invoice.customerTaxId}</p>
//               </div>
//             )}
//           </CardContent>
//         </Card>

//         {/* Información del Emisor */}
//         <Card>
//           <CardHeader>
//             <CardTitle className="flex items-center gap-2">
//               <Building className="h-5 w-5" />
//               Información del Emisor
//             </CardTitle>
//           </CardHeader>
//           <CardContent className="space-y-3">
//             <div>
//               <p className="font-medium">{invoice.issuerName}</p>
//               <p className="text-sm text-muted-foreground">RFC: {invoice.issuerTaxId}</p>
//             </div>
//           </CardContent>
//         </Card>
//       </div>

//       {/* Información de la Reserva */}
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <Car className="h-5 w-5" />
//             Información de la Reserva
//           </CardTitle>
//         </CardHeader>
//         <CardContent className="space-y-3">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div>
//               <p className="text-sm font-medium">Vehículo</p>
//               <p className="text-sm text-muted-foreground">
//                 {invoice.transaction.reservation.vehicle.make} {invoice.transaction.reservation.vehicle.model} {invoice.transaction.reservation.vehicle.year}
//               </p>
//             </div>
//             <div>
//               <p className="text-sm font-medium">Período de Renta</p>
//               <p className="text-sm text-muted-foreground">
//                 {DateTime.fromISO(invoice.transaction.reservation.startDate).toFormat('dd/MM/yyyy')} - {DateTime.fromISO(invoice.transaction.reservation.endDate).toFormat('dd/MM/yyyy')}
//               </p>
//             </div>
//           </div>
//         </CardContent>
//       </Card>

//       {/* Detalles de la Factura */}
//       <Card>
//         <CardHeader>
//           <CardTitle>Detalles de la Factura</CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="space-y-4">
//             {/* Items */}
//             <div className="space-y-2">
//               {invoice.items.map((item, index) => (
//                 <div key={item.id} className="flex justify-between items-start py-2">
//                   <div className="flex-1">
//                     <p className="font-medium">{item.description}</p>
//                     <p className="text-sm text-muted-foreground">
//                       Cantidad: {item.quantity} × ${item.unitPrice.toLocaleString('es-MX', { minimumFractionDigits: 2 })}
//                     </p>
//                   </div>
//                   <div className="text-right">
//                     <p className="font-medium">${item.totalPrice.toLocaleString('es-MX', { minimumFractionDigits: 2 })}</p>
//                     {item.taxAmount > 0 && (
//                       <p className="text-sm text-muted-foreground">
//                         + ${item.taxAmount.toLocaleString('es-MX', { minimumFractionDigits: 2 })} IVA
//                       </p>
//                     )}
//                   </div>
//                 </div>
//               ))}
//             </div>

//             <Separator />

//             {/* Totales */}
//             <div className="space-y-2">
//               <div className="flex justify-between">
//                 <span>Subtotal:</span>
//                 <span>${invoice.subtotal.toLocaleString('es-MX', { minimumFractionDigits: 2 })}</span>
//               </div>
//               {invoice.taxAmount > 0 && (
//                 <div className="flex justify-between">
//                   <span>IVA:</span>
//                   <span>${invoice.taxAmount.toLocaleString('es-MX', { minimumFractionDigits: 2 })}</span>
//                 </div>
//               )}
//               <Separator />
//               <div className="flex justify-between text-lg font-semibold">
//                 <span>Total:</span>
//                 <span>${invoice.totalAmount.toLocaleString('es-MX', { minimumFractionDigits: 2 })} {invoice.currency.toUpperCase()}</span>
//               </div>
//             </div>

//             {/* Fechas importantes */}
//             <Separator />
//             <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
//               <div>
//                 <p className="font-medium">Fecha de Emisión</p>
//                 <p className="text-muted-foreground">
//                   {DateTime.fromISO(invoice.issuedAt).toFormat('dd/MM/yyyy')}
//                 </p>
//               </div>
//               {invoice.dueDate && (
//                 <div>
//                   <p className="font-medium">Fecha de Vencimiento</p>
//                   <p className="text-muted-foreground">
//                     {DateTime.fromISO(invoice.dueDate).toFormat('dd/MM/yyyy')}
//                   </p>
//                 </div>
//               )}
//               {invoice.paidAt && (
//                 <div>
//                   <p className="font-medium">Fecha de Pago</p>
//                   <p className="text-muted-foreground">
//                     {DateTime.fromISO(invoice.paidAt).toFormat('dd/MM/yyyy')}
//                   </p>
//                 </div>
//               )}
//             </div>
//           </div>
//         </CardContent>
//       </Card>
//     </div>
//   )
// }
