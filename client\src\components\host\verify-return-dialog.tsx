"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { CheckCircle, AlertCircle } from "lucide-react"
import { reservationsApi } from "@/lib/api/reservations.api"
import toast from "react-hot-toast"

interface VerifyReturnDialogProps {
  isOpen: boolean
  onClose: () => void
  reservation: {
    id: string
    vehicle: {
      make: string
      model: string
    }
    user: {
      name: string
    }
    endDate: string
    hostVerification: boolean
  }
  onVerified: () => void
}

export function VerifyReturnDialog({
  isOpen,
  onClose,
  reservation,
  onVerified
}: VerifyReturnDialogProps) {
  const [notes, setNotes] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleVerify = async () => {
    setIsLoading(true)
    try {
      await reservationsApi.host.verifyVehicleReturn(reservation.id, notes)
      toast.success("Entrega verificada exitosamente")
      onVerified()
      onClose()
      setNotes("")
    } catch (error) {
      console.error("Error verifying return:", error)
      toast.error("Error al verificar la entrega")
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {reservation.hostVerification ? (
              <>
                <CheckCircle className="h-5 w-5 text-green-500" />
                Entrega Verificada
              </>
            ) : (
              <>
                <AlertCircle className="h-5 w-5 text-orange-500" />
                Verificar Entrega del Vehículo
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {reservation.hostVerification ? (
              "Esta reserva ya ha sido verificada."
            ) : (
              "Confirma que has recibido el vehículo de vuelta en buenas condiciones."
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Label className="text-muted-foreground">Vehículo</Label>
              <p className="font-medium">
                {reservation.vehicle.make} {reservation.vehicle.model}
              </p>
            </div>
            <div>
              <Label className="text-muted-foreground">Cliente</Label>
              <p className="font-medium">{reservation.user.name}</p>
            </div>
            <div className="col-span-2">
              <Label className="text-muted-foreground">Fecha de fin</Label>
              <p className="font-medium">{formatDate(reservation.endDate)}</p>
            </div>
          </div>

          {!reservation.hostVerification && (
            <div className="space-y-2">
              <Label htmlFor="notes">
                Notas adicionales (opcional)
              </Label>
              <Textarea
                id="notes"
                placeholder="Ej: Vehículo entregado en perfectas condiciones, tanque lleno..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                Estas notas quedarán registradas en el historial de la reserva.
              </p>
            </div>
          )}

          {reservation.hostVerification && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-green-700 font-medium mb-2">
                <CheckCircle className="h-4 w-4" />
                Entrega verificada exitosamente
              </div>
              <p className="text-sm text-green-600">
                Una vez verificada la entrega, el cliente recibirá una notificación 
                para dejar una reseña de su experiencia.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {reservation.hostVerification ? "Cerrar" : "Cancelar"}
          </Button>
          {!reservation.hostVerification && (
            <Button 
              onClick={handleVerify} 
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? "Verificando..." : "Verificar Entrega"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
