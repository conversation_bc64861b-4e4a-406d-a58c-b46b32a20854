import { prisma } from "@/lib/prisma";
import { HttpException } from "@/exceptions/HttpExceptions";
import { DateTime } from "luxon";
// import { scheduleHostTransfer } from "../../../jobs/payment-jobs";
import { getTimezone } from '@/app/utils/zones';

export class ReservationsService {
  // Crear una nueva reserva
  static async create(data: {
    vehicleId: string;
    userId: string;
    startDate: string;
    endDate: string;
    totalPrice: number;
    contactName?: string;
    contactEmail?: string;
    contactPhone?: string;
  }) {
    // Verificar que el vehículo existe
    const vehicle = await prisma.vehicle.findUnique({
      where: { id: data.vehicleId }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    // Verificar que las fechas son válidas
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);

    if (startDate >= endDate) {
      throw HttpException.BadRequest("La fecha de inicio debe ser anterior a la fecha de fin");
    }

    // Verificar que el vehículo está disponible en esas fechas
    const existingReservation = await prisma.vehicleReservation.findFirst({
      where: {
        vehicleId: data.vehicleId,
        status: { in: ['pending', 'confirmed'] },
        OR: [
          {
            // Nueva reserva comienza durante una existente
            startDate: { lte: endDate },
            endDate: { gte: startDate }
          }
        ]
      }
    });

    if (existingReservation) {
      throw HttpException.BadRequest("El vehículo no está disponible en las fechas seleccionadas");
    }

    // Crear la reserva
    const reservation = await prisma.vehicleReservation.create({
      data: {
        vehicleId: data.vehicleId,
        userId: data.userId,
        startDate,
        endDate,
        totalPrice: data.totalPrice,
        contactName: data.contactName,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone,
        status: 'confirmed',
        paymentStatus: 'pending'
      }
    });

    return reservation;
  }

  /**
   * Programar transferencia al host cuando una reserva inicia
   */
  static async scheduleTransferForReservation(reservationId: string) {
    try {
      const reservation = await prisma.vehicleReservation.findUnique({
        where: { id: reservationId },
        include: {
          transaction: true,
          vehicle: {
            select: { hostId: true }
          }
        }
      });

      if (!reservation) {
        throw HttpException.NotFound('Reserva no encontrada');
      }

      if (!reservation.transaction) {
        throw HttpException.BadRequest('La reserva no tiene una transacción asociada');
      }

      if (reservation.transaction.status !== 'succeeded') {
        throw HttpException.BadRequest('La transacción no está en estado válido para programar transferencia');
      }

      // Programar la transferencia
      // await scheduleHostTransfer(
      //   reservation.transaction.id,
      //   reservation.id,
      //   reservation.startDate
      // );

      return { success: true, message: 'Transferencia programada correctamente' };
    } catch (error) {
      console.error('Error scheduling transfer for reservation:', error);
      throw HttpException.InternalServerError('Error al programar transferencia');
    }
  }

  static async getByHostId(hostId: string, options: { page: number, limit: number }) {
    const { page, limit } = options;
    const skip = (page - 1) * limit;

    // Obtener el total de reservas para la paginación
    const totalCount = await prisma.vehicleReservation.count({
      where: {
        vehicle: {
          hostId
        }
      }
    });

    // Obtener las reservas con paginación

    const reservations = await prisma.vehicleReservation.findMany({
      where: {
        vehicle: {
          hostId
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true,
            price: true,
            host: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          }
        }
      },
      skip,
      take: limit,
      orderBy: {
        createdAt: 'desc'
      }
    });

    return {
      data: reservations,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    };
  }


  // Obtener todas las reservas (admin)
  static async getAllReservations() {
    return await prisma.vehicleReservation.findMany({
      include: {
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  // Obtener reservas de un usuario
  static async getUserReservations(userId: string) {
    return await prisma.vehicleReservation.findMany({
      where: {
        userId
      },
      include: {
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true,
            price: true,
            host: {
              select: {
                id: true,
                name: true,
                image: true
              }
            },
            features: true
          }
        }
      },
      orderBy: {
        startDate: 'desc'
      }
    });
  }

  // Obtener reservas de un host (excluyendo las propias)
  static async getHostReservations(hostId: string) {
    return await prisma.vehicleReservation.findMany({
      where: {
        vehicle: {
          hostId
        },
        // Excluir reservas hechas por el propio host
        NOT: {
          user: {
            id: hostId
          }
        }
      },
      include: {
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      },
      orderBy: {
        startDate: 'desc'
      }
    });
  }

  // Obtener reservas personales del host (reservas que el host ha hecho)
  static async getHostPersonalReservations(hostId: string) {
    return await prisma.vehicleReservation.findMany({
      where: {
        // Reservas donde el usuario es el host
        userId: hostId
      },
      include: {
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true,
            host: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          }
        }
      },
      orderBy: {
        startDate: 'desc'
      }
    });
  }

  // Obtener reservas de un vehículo para un host
  static async getVehicleReservations(vehicleId: string, hostId: string) {
    // Verificar que el vehículo pertenece al host
    const vehicle = await prisma.vehicle.findFirst({
      where: {
        id: vehicleId,
        hostId
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado o no pertenece al host");
    }

    return await prisma.vehicleReservation.findMany({
      where: {
        vehicleId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      },
      orderBy: {
        startDate: 'desc'
      }
    });
  }

  // Obtener una reserva por ID (usuario)
  static async getReservationById(id: string, userId: string) {
    const reservation = await prisma.vehicleReservation.findFirst({
      where: {
        id,
        userId
      },
      include: {
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true,
            price: true,
            host: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          }
        }
      }
    });

    if (!reservation) {
      throw HttpException.NotFound("Reserva no encontrada");
    }

    return reservation;
  }

  // Obtener una reserva por ID (admin)
  static async getReservationByIdAdmin(id: string) {
    const reservation = await prisma.vehicleReservation.findUnique({
      where: { id },
      include: {
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true,
            host: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      }
    });

    if (!reservation) {
      throw HttpException.NotFound("Reserva no encontrada");
    }

    return reservation;
  }

  // Cancelar una reserva (usuario)
  static async cancelReservation(id: string, userId: string) {
    const reservation = await prisma.vehicleReservation.findFirst({
      where: {
        id,
        userId
      }
    });

    if (!reservation) {
      throw HttpException.NotFound("Reserva no encontrada");
    }

    // Solo se pueden cancelar reservas pendientes o confirmadas
    if (reservation.status !== 'pending' && reservation.status !== 'confirmed') {
      throw HttpException.BadRequest("No se puede cancelar esta reserva");
    }

    return await prisma.vehicleReservation.update({
      where: { id },
      data: { status: 'cancelled' }
    });
  }

  // Actualizar estado de reserva (host)
  static async updateReservationStatus(id: string, status: string, hostId: string) {
    const reservation = await prisma.vehicleReservation.findUnique({
      where: { id },
      include: {
        vehicle: true
      }
    });

    if (!reservation) {
      throw HttpException.NotFound("Reserva no encontrada");
    }

    // Verificar que el vehículo pertenece al host
    if (reservation.vehicle.hostId !== hostId) {
      throw HttpException.Forbidden
    }

    // Verificar que el estado es válido
    if (!['pending', 'confirmed', 'cancelled'].includes(status)) {
      throw HttpException.BadRequest("Estado de reserva no válido");
    }

    return await prisma.vehicleReservation.update({
      where: { id },
      data: { status }
    });
  }

  // Actualizar estado de reserva (admin)
  static async updateReservationStatusAdmin(id: string, status: string) {
    const reservation = await prisma.vehicleReservation.findUnique({
      where: { id }
    });

    if (!reservation) {
      throw HttpException.NotFound("Reserva no encontrada");
    }

    // Verificar que el estado es válido
    if (!['pending', 'confirmed', 'cancelled'].includes(status)) {
      throw HttpException.BadRequest("Estado de reserva no válido");
    }

    return await prisma.vehicleReservation.update({
      where: { id },
      data: { status }
    });
  }

  // Bloquear fechas (para host o admin)
  static async blockDates(data: {
    vehicleId: string;
    userId: string;
    startDate: string;
    endDate: string;
    by: 'host' | 'admin';
    reason?: string;
  }) {
    // Verificar que el vehículo existe
    const vehicle = await prisma.vehicle.findUnique({
      where: { id: data.vehicleId }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    // Si es un host, verificar que es el propietario del vehículo
    if (data.by === 'host' && vehicle.hostId !== data.userId) {
      throw HttpException.Forbidden("No tienes permiso para bloquear fechas de este vehículo");
    }

    // Obtener la zona horaria del vehículo
    const timezone = getTimezone(vehicle.country_code as 'mx' | 'us', vehicle.state_code);

    // Usar Luxon para manejar correctamente las zonas horarias
    // Convertir las fechas a medianoche en la zona horaria del vehículo
    const startDate = DateTime.fromISO(data.startDate, { zone: timezone }).startOf('day').toJSDate();
    const endDate = DateTime.fromISO(data.endDate, { zone: timezone }).startOf('day').toJSDate();

    console.log('startDate (vehicle timezone):', startDate.toISOString());
    console.log('endDate (vehicle timezone):', endDate.toISOString());

    if (startDate >= endDate) {
      throw HttpException.BadRequest("La fecha de inicio debe ser anterior a la fecha de fin");
    }

    const dataCreation = {
      vehicleId: data.vehicleId,
      userId: data.userId,
      startDate,
      endDate,
      totalPrice: 0, // No hay costo para bloqueos
      status: 'confirmed', // Los bloqueos siempre están confirmados
      by: data.by,
      reason: data.reason || 'Bloqueado',
    }

    // Crear la "reserva" de bloqueo
    return await prisma.vehicleReservation.create({
      data: dataCreation
    });
  }

  // Obtener fechas no disponibles
  static async getUnavailableDates(vehicleId: string) {
    const vehicle = await prisma.vehicle.findUnique({
      where: { id: vehicleId },
      select: { country_code: true, state_code: true }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    const timezone = getTimezone(vehicle.country_code as 'mx' | 'us', vehicle.state_code);

    const reservations = await prisma.vehicleReservation.findMany({
      where: {
        vehicleId,
        status: { in: ['pending', 'confirmed'] },
        // Al menos un día de la reserva debe estar en el futuro
        OR: [
          { startDate: { gte: new Date() } },
          { endDate: { gte: new Date() } }
        ]
      },
      select: {
        startDate: true,
        endDate: true,
        by: true,
        reason: true
      }
    });

    // Generar todas las fechas entre inicio y fin para cada reserva
    const unavailableDates = [];

    for (const reservation of reservations) {
      // Usar Luxon para manejar correctamente las zonas horarias
      const start = DateTime.fromJSDate(reservation.startDate, { zone: timezone }).startOf('day');
      const end = DateTime.fromJSDate(reservation.endDate, { zone: timezone }).startOf('day');
      
      // Generar todas las fechas entre inicio y fin
      let currentDate = start;
      while (currentDate <= end) {
        unavailableDates.push({
          date: currentDate.toISODate(), // Formato YYYY-MM-DD
          by: reservation.by,
          reason: reservation.reason
        });
        currentDate = currentDate.plus({ days: 1 });
      }
    }

    return unavailableDates;
  }

  // Obtener todas las reservas para el administrador
  static async getAllReservationsForAdmin() {
    const reservations = await prisma.vehicleReservation.findMany({
      include: {
        vehicle: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            images: true,
            hostId: true,
            host: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Añadir un indicador para reservaciones personales (cuando el usuario es el mismo que el host)
    return reservations.map(reservation => ({
      ...reservation,
      isPersonalReservation: reservation.userId === reservation.vehicle.hostId
    }));
  }

  static async getStatsByHostId(hostId: string) {
    // Obtener todas las reservas del host
    const reservations = await prisma.vehicleReservation.findMany({
      where: {
        vehicle: {
          hostId
        }
      },
      include: {
        // review: true
      }
    });

    // Calcular estadísticas
    const totalReservations = reservations.length;
    const activeReservations = reservations.filter(r => r.status === 'confirmed').length;
    const pendingReservations = reservations.filter(r => r.status === 'pending').length;
    const completedReservations = reservations.filter(r => r.status === 'completed').length;
    const cancelledReservations = reservations.filter(r => r.status === 'cancelled').length;


    // Calcular ganancias totales
    const totalEarnings = reservations
      .filter(r => r.status === 'completed')
      .reduce((sum, r) => sum + r.totalPrice, 0);

    return {
      totalReservations,
      activeReservations,
      pendingReservations,
      completedReservations,
      cancelledReservations,
      // averageRating,
      totalEarnings
    };
  }

  // Verificar la entrega del vehículo por parte del host
  static async verifyVehicleReturn(reservationId: string, hostId: string, notes?: string) {
    try {
      // Verificar que la reserva existe y pertenece al host
      const reservation = await prisma.vehicleReservation.findFirst({
        where: {
          id: reservationId,
          vehicle: {
            hostId: hostId
          },
          status: 'completed'
        },
        include: {
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true,
              hostId: true
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!reservation) {
        return {
          success: false,
          error: 'Reserva no encontrada o no pertenece a este host',
          status: 404
        };
      }

      if (reservation.hostVerification) {
        return {
          success: false,
          error: 'La entrega ya ha sido verificada anteriormente',
          status: 400
        };
      }

      // Marcar como verificado
      const updatedReservation = await prisma.vehicleReservation.update({
        where: { id: reservationId },
        data: {
          hostVerification: true,
          hostVerificationDate: new Date(),
          hostVerificationNotes: notes
        },
        include: {
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      console.log(`✅ Host ${hostId} verificó entrega de reserva ${reservationId}`);

      return {
        success: true,
        data: updatedReservation,
        message: 'Entrega verificada exitosamente'
      };

    } catch (error) {
      console.error('❌ Error verifying vehicle return:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  }
}

