import { HostDashboardStats } from "@/components/host/host-dashboard-stats"
// import { HostRecentActivity } from "@/components/host/host-recent-activity"
import { HostEarningsChart } from "@/components/host/host-earnings-chart"
// import { HostUpcomingReservations } from "@/components/host/host-upcoming-reservations"
// import { StripeOnboarding } from "@/components/host/stripe-onboarding"

export default function HostDashboard() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Mi Dashboard</h1>
        <p className="text-muted-foreground">
          Bienvenido de vuelta. Aquí tienes un resumen de tus vehículos y ganancias.
        </p>
      </div>

      {/* Onboarding de Stripe - Se muestra solo si es necesario */}
      {/* <StripeOnboarding showFullCard={true} /> */}

      <HostDashboardStats />

      {/* grid-cols-1 lg:grid-cols-2 */}
      <div className="grid  gap-6">
        <HostEarningsChart />
        {/* <HostRecentActivity /> */}
      </div>

      {/* <HostUpcomingReservations /> */}
    </div>
  )
}
